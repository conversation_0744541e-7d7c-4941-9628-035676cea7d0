package main

import (
	"database/sql"
	"fmt"
	"log"
	"time"

	_ "github.com/mattn/go-sqlite3"
)

func main() {
	// 连接数据库
	db, err := sql.Open("sqlite3", "data/scanner.db")
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}
	defer db.Close()

	fmt.Println("=== 扫描引擎状态分析 ===")
	fmt.Printf("分析时间: %s\n\n", time.Now().Format("2006-01-02 15:04:05"))

	// 查询最近失败的任务
	var taskID int
	var taskName, taskType, taskStatus string
	err = db.QueryRow(`
		SELECT id, name, scan_type, status 
		FROM scan_tasks 
		WHERE status = 'failed' 
		ORDER BY id DESC 
		LIMIT 1
	`).Scan(&taskID, &taskName, &taskType, &taskStatus)

	if err != nil {
		fmt.Println("未找到失败的任务")
		return
	}

	fmt.Printf("🔍 分析任务: %d (%s)\n", taskID, taskName)
	fmt.Println("================================================================================")

	// 查询任务的详细执行流程
	fmt.Println("📋 任务执行流程:")
	logRows, err := db.Query(`
		SELECT level, stage, target, message, progress, created_at
		FROM scan_logs 
		WHERE task_id = ?
		ORDER BY created_at ASC
	`, taskID)

	if err != nil {
		log.Printf("查询日志失败: %v", err)
		return
	}
	defer logRows.Close()

	var stages []string
	var lastProgress int
	var startTime, endTime string
	logCount := 0

	for logRows.Next() {
		var level, stage, target, message, logTime string
		var progress int
		
		err := logRows.Scan(&level, &stage, &target, &message, &progress, &logTime)
		if err != nil {
			continue
		}
		
		logCount++
		if logCount == 1 {
			startTime = logTime
		}
		endTime = logTime
		
		// 记录阶段变化
		if len(stages) == 0 || stages[len(stages)-1] != stage {
			stages = append(stages, stage)
		}
		
		lastProgress = progress
		
		// 显示关键日志
		if level == "ERROR" || containsKeyword(message, []string{"失败", "错误", "超时", "failed"}) {
			fmt.Printf("❌ [%s] %s: %s\n", formatTime(logTime), stage, message)
		} else if containsKeyword(stage, []string{"启动", "完成", "监控"}) {
			fmt.Printf("📍 [%s] %s: %s (进度:%d%%)\n", formatTime(logTime), stage, truncateMessage(message, 50), progress)
		}
	}

	fmt.Printf("\n📊 执行统计:\n")
	fmt.Printf("  开始时间: %s\n", startTime)
	fmt.Printf("  结束时间: %s\n", endTime)
	fmt.Printf("  执行时长: %s\n", calculateDuration(startTime, endTime))
	fmt.Printf("  最终进度: %d%%\n", lastProgress)
	fmt.Printf("  执行阶段: %v\n", stages)

	// 分析失败模式
	fmt.Printf("\n🔍 失败模式分析:\n")
	analyzeFailurePattern(db, taskID)

	// 检查系统状态
	fmt.Printf("\n🖥️ 系统状态检查:\n")
	checkSystemStatus(db)

	// 提供修复建议
	fmt.Printf("\n💡 修复建议:\n")
	provideSuggestions(stages, lastProgress)
}

func formatTime(timeStr string) string {
	t, err := time.Parse("2006-01-02T15:04:05.999999999Z07:00", timeStr)
	if err != nil {
		// 尝试其他格式
		t, err = time.Parse("2006-01-02 15:04:05", timeStr)
		if err != nil {
			return timeStr[:16]
		}
	}
	return t.Format("15:04:05")
}

func truncateMessage(message string, maxLen int) string {
	if len(message) <= maxLen {
		return message
	}
	return message[:maxLen-3] + "..."
}

func containsKeyword(text string, keywords []string) bool {
	for _, keyword := range keywords {
		if contains(text, keyword) {
			return true
		}
	}
	return false
}

func contains(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

func calculateDuration(start, end string) string {
	startTime, err1 := time.Parse("2006-01-02T15:04:05.999999999Z07:00", start)
	endTime, err2 := time.Parse("2006-01-02T15:04:05.999999999Z07:00", end)
	
	if err1 != nil || err2 != nil {
		return "无法计算"
	}
	
	duration := endTime.Sub(startTime)
	return duration.String()
}

func analyzeFailurePattern(db *sql.DB, taskID int) {
	// 检查是否有引擎相关错误
	var engineErrors int
	db.QueryRow(`
		SELECT COUNT(*) 
		FROM scan_logs 
		WHERE task_id = ? AND (message LIKE '%引擎%' OR message LIKE '%engine%')
	`, taskID).Scan(&engineErrors)

	// 检查是否有网络相关错误
	var networkErrors int
	db.QueryRow(`
		SELECT COUNT(*) 
		FROM scan_logs 
		WHERE task_id = ? AND (message LIKE '%网络%' OR message LIKE '%连接%' OR message LIKE '%timeout%')
	`, taskID).Scan(&networkErrors)

	// 检查是否有监控相关日志
	var monitorLogs int
	db.QueryRow(`
		SELECT COUNT(*) 
		FROM scan_logs 
		WHERE task_id = ? AND stage LIKE '%监控%'
	`, taskID).Scan(&monitorLogs)

	fmt.Printf("  引擎相关日志: %d条\n", engineErrors)
	fmt.Printf("  网络相关日志: %d条\n", networkErrors)
	fmt.Printf("  监控相关日志: %d条\n", monitorLogs)

	// 分析失败模式
	if monitorLogs > 0 && engineErrors == 0 && networkErrors == 0 {
		fmt.Printf("  🔍 失败模式: 监控系统检测到异常并停止任务\n")
		fmt.Printf("  📝 说明: 任务启动成功但被监控系统标记为失败\n")
	} else if networkErrors > 0 {
		fmt.Printf("  🔍 失败模式: 网络连接问题\n")
	} else if engineErrors > 0 {
		fmt.Printf("  🔍 失败模式: 扫描引擎问题\n")
	} else {
		fmt.Printf("  🔍 失败模式: 静默失败（无明显错误日志）\n")
	}
}

func checkSystemStatus(db *sql.DB) {
	// 检查最近的任务成功率
	var totalTasks, failedTasks int
	db.QueryRow(`
		SELECT COUNT(*) 
		FROM scan_tasks 
		WHERE created_at > datetime('now', '-1 hour')
	`).Scan(&totalTasks)

	db.QueryRow(`
		SELECT COUNT(*) 
		FROM scan_tasks 
		WHERE created_at > datetime('now', '-1 hour') AND status = 'failed'
	`).Scan(&failedTasks)

	if totalTasks > 0 {
		successRate := float64(totalTasks-failedTasks) / float64(totalTasks) * 100
		fmt.Printf("  最近1小时任务成功率: %.1f%% (%d/%d)\n", successRate, totalTasks-failedTasks, totalTasks)
	} else {
		fmt.Printf("  最近1小时无任务执行\n")
	}

	// 检查是否有系统错误日志
	var systemErrors int
	db.QueryRow(`
		SELECT COUNT(*) 
		FROM scan_logs 
		WHERE created_at > datetime('now', '-1 hour') AND level = 'ERROR'
	`).Scan(&systemErrors)

	fmt.Printf("  最近1小时系统错误数: %d条\n", systemErrors)
}

func provideSuggestions(stages []string, lastProgress int) {
	fmt.Printf("========================================\n")
	
	// 基于执行阶段提供建议
	if contains(fmt.Sprintf("%v", stages), "监控") {
		fmt.Printf("1. 监控系统问题:\n")
		fmt.Printf("   - 检查任务监控服务是否正常运行\n")
		fmt.Printf("   - 验证监控超时配置是否合理\n")
		fmt.Printf("   - 查看监控服务日志获取更多信息\n\n")
	}

	if lastProgress < 50 {
		fmt.Printf("2. 早期失败问题:\n")
		fmt.Printf("   - 检查目标URL的可访问性\n")
		fmt.Printf("   - 验证网络连接和防火墙设置\n")
		fmt.Printf("   - 确认扫描引擎配置正确\n\n")
	}

	fmt.Printf("3. 通用修复步骤:\n")
	fmt.Printf("   - 重新启动扫描服务\n")
	fmt.Printf("   - 检查系统资源使用情况\n")
	fmt.Printf("   - 查看详细的应用程序日志\n")
	fmt.Printf("   - 尝试使用简单目标进行测试扫描\n")
}
