package core

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// EngineManager 扫描引擎管理器
// 负责管理所有扫描引擎的注册、调度、协调等功能
type EngineManager struct {
	// 引擎注册表
	engines map[EngineType]ScanEngine
	mutex   sync.RWMutex

	// 任务管理
	activeTasks map[string]*TaskInfo
	taskMutex   sync.RWMutex

	// 配置
	config *ManagerConfig

	// 日志
	logger *logrus.Logger

	// 状态
	initialized bool
	running     bool
}

// ManagerConfig 管理器配置
type ManagerConfig struct {
	// 并发控制
	MaxConcurrentTasks int           `json:"max_concurrent_tasks"`
	TaskTimeout        time.Duration `json:"task_timeout"`

	// 引擎配置
	EnabledEngines []EngineType               `json:"enabled_engines"`
	EngineConfigs  map[EngineType]interface{} `json:"engine_configs"`

	// 调度配置
	SchedulerEnabled  bool          `json:"scheduler_enabled"`
	SchedulerInterval time.Duration `json:"scheduler_interval"`

	// 监控配置
	MetricsEnabled      bool          `json:"metrics_enabled"`
	HealthCheckEnabled  bool          `json:"health_check_enabled"`
	HealthCheckInterval time.Duration `json:"health_check_interval"`
}

// TaskInfo 任务信息
type TaskInfo struct {
	ID         string                 `json:"id"`
	Request    *ScanRequest           `json:"request"`
	EngineType EngineType             `json:"engine_type"`
	Status     ScanStatusType         `json:"status"`
	Progress   float64                `json:"progress"`
	StartTime  time.Time              `json:"start_time"`
	EndTime    *time.Time             `json:"end_time"`
	Result     *ScanResult            `json:"result"`
	Error      error                  `json:"error"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// EngineInfo 引擎信息
type EngineInfo struct {
	Type        EngineType  `json:"type"`
	Name        string      `json:"name"`
	Version     string      `json:"version"`
	Description string      `json:"description"`
	Enabled     bool        `json:"enabled"`
	Status      string      `json:"status"`
	Statistics  interface{} `json:"statistics"`
}

// ManagerStatistics 管理器统计信息
type ManagerStatistics struct {
	// 引擎统计
	TotalEngines   int `json:"total_engines"`
	EnabledEngines int `json:"enabled_engines"`
	RunningEngines int `json:"running_engines"`

	// 任务统计
	TotalTasks     int64 `json:"total_tasks"`
	CompletedTasks int64 `json:"completed_tasks"`
	FailedTasks    int64 `json:"failed_tasks"`
	ActiveTasks    int   `json:"active_tasks"`

	// 性能统计
	AverageTaskTime time.Duration `json:"average_task_time"`
	TotalScanTime   time.Duration `json:"total_scan_time"`

	// 系统统计
	Uptime       time.Duration `json:"uptime"`
	LastTaskTime time.Time     `json:"last_task_time"`
}

// NewEngineManager 创建引擎管理器
func NewEngineManager() *EngineManager {
	config := &ManagerConfig{
		MaxConcurrentTasks:  10,
		TaskTimeout:         60 * time.Minute,
		EnabledEngines:      []EngineType{},
		EngineConfigs:       make(map[EngineType]interface{}),
		SchedulerEnabled:    true,
		SchedulerInterval:   1 * time.Second,
		MetricsEnabled:      true,
		HealthCheckEnabled:  true,
		HealthCheckInterval: 30 * time.Second,
	}

	return &EngineManager{
		engines:     make(map[EngineType]ScanEngine),
		activeTasks: make(map[string]*TaskInfo),
		config:      config,
		logger:      logrus.New(),
	}
}

// Initialize 初始化管理器
func (m *EngineManager) Initialize() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.initialized {
		return nil
	}

	m.logger.Info("初始化扫描引擎管理器")

	// 初始化所有注册的引擎
	for engineType, engine := range m.engines {
		if err := engine.Initialize(); err != nil {
			m.logger.Errorf("引擎初始化失败 %s: %v", engineType, err)
			return fmt.Errorf("引擎初始化失败 %s: %v", engineType, err)
		}
		m.logger.Infof("引擎初始化成功: %s", engineType)
	}

	m.initialized = true
	m.running = true

	// 启动后台服务
	if m.config.SchedulerEnabled {
		go m.runScheduler()
	}

	if m.config.HealthCheckEnabled {
		go m.runHealthCheck()
	}

	m.logger.Info("扫描引擎管理器初始化完成")
	return nil
}

// Shutdown 关闭管理器
func (m *EngineManager) Shutdown() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	m.logger.Info("关闭扫描引擎管理器")

	m.running = false

	// 停止所有活跃任务
	m.taskMutex.Lock()
	for taskID := range m.activeTasks {
		m.stopTaskInternal(taskID)
	}
	m.taskMutex.Unlock()

	// 清理所有引擎
	for engineType, engine := range m.engines {
		if err := engine.Cleanup(); err != nil {
			m.logger.Errorf("引擎清理失败 %s: %v", engineType, err)
		}
	}

	m.initialized = false
	m.logger.Info("扫描引擎管理器已关闭")

	return nil
}

// RegisterEngine 注册扫描引擎
func (m *EngineManager) RegisterEngine(engine ScanEngine) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	engineType := engine.GetType()

	if _, exists := m.engines[engineType]; exists {
		return fmt.Errorf("引擎类型已存在: %s", engineType)
	}

	m.engines[engineType] = engine
	m.logger.Infof("注册扫描引擎: %s - %s", engineType, engine.GetName())

	return nil
}

// UnregisterEngine 注销扫描引擎
func (m *EngineManager) UnregisterEngine(engineType EngineType) error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	engine, exists := m.engines[engineType]
	if !exists {
		return fmt.Errorf("引擎类型不存在: %s", engineType)
	}

	// 清理引擎
	if err := engine.Cleanup(); err != nil {
		m.logger.Errorf("引擎清理失败 %s: %v", engineType, err)
	}

	delete(m.engines, engineType)
	m.logger.Infof("注销扫描引擎: %s", engineType)

	return nil
}

// GetEngine 获取指定类型的引擎
func (m *EngineManager) GetEngine(engineType EngineType) (ScanEngine, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	engine, exists := m.engines[engineType]
	if !exists {
		return nil, fmt.Errorf("引擎类型不存在: %s", engineType)
	}

	return engine, nil
}

// ListEngines 列出所有引擎
func (m *EngineManager) ListEngines() []*EngineInfo {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	engines := make([]*EngineInfo, 0, len(m.engines))

	for engineType, engine := range m.engines {
		info := &EngineInfo{
			Type:        engineType,
			Name:        engine.GetName(),
			Version:     engine.GetVersion(),
			Description: engine.GetDescription(),
			Enabled:     engine.IsEnabled(),
			Status:      "running",
		}

		// 获取引擎统计信息（如果支持）
		if baseEngine, ok := engine.(*BaseEngine); ok {
			info.Statistics = baseEngine.GetStatistics()
		}

		engines = append(engines, info)
	}

	return engines
}

// SubmitScanRequest 提交扫描请求
func (m *EngineManager) SubmitScanRequest(request *ScanRequest) (*TaskInfo, error) {
	if !m.running {
		return nil, fmt.Errorf("引擎管理器未运行")
	}

	// 检查并发限制
	m.taskMutex.RLock()
	activeCount := len(m.activeTasks)
	m.taskMutex.RUnlock()

	if activeCount >= m.config.MaxConcurrentTasks {
		return nil, fmt.Errorf("达到最大并发任务数限制: %d", m.config.MaxConcurrentTasks)
	}

	// 选择合适的引擎
	engineType, err := m.selectEngine(request)
	if err != nil {
		return nil, fmt.Errorf("无法选择合适的引擎: %v", err)
	}

	// 创建任务信息
	taskInfo := &TaskInfo{
		ID:         request.ID,
		Request:    request,
		EngineType: engineType,
		Status:     StatusPending,
		Progress:   0.0,
		StartTime:  time.Now(),
		Metadata:   make(map[string]interface{}),
	}

	// 注册任务
	m.taskMutex.Lock()
	m.activeTasks[request.ID] = taskInfo
	m.taskMutex.Unlock()

	// 异步执行扫描
	go m.executeScan(taskInfo)

	m.logger.Infof("提交扫描任务: %s, 引擎: %s, 目标: %s",
		request.ID, engineType, request.Target.Value)

	return taskInfo, nil
}

// GetTaskStatus 获取任务状态
func (m *EngineManager) GetTaskStatus(taskID string) (*TaskInfo, error) {
	m.taskMutex.RLock()
	defer m.taskMutex.RUnlock()

	taskInfo, exists := m.activeTasks[taskID]
	if !exists {
		return nil, fmt.Errorf("任务不存在: %s", taskID)
	}

	// 返回任务信息的副本
	info := *taskInfo
	return &info, nil
}

// StopTask 停止任务
func (m *EngineManager) StopTask(taskID string) error {
	m.taskMutex.Lock()
	defer m.taskMutex.Unlock()

	return m.stopTaskInternal(taskID)
}

// stopTaskInternal 内部停止任务方法
func (m *EngineManager) stopTaskInternal(taskID string) error {
	taskInfo, exists := m.activeTasks[taskID]
	if !exists {
		return fmt.Errorf("任务不存在: %s", taskID)
	}

	// 直接从engines map获取引擎，避免调用GetEngine造成死锁
	engine, exists := m.engines[taskInfo.EngineType]
	if !exists {
		return fmt.Errorf("引擎类型不存在: %s", taskInfo.EngineType)
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := engine.Stop(ctx, taskID); err != nil {
		m.logger.Errorf("停止任务失败 %s: %v", taskID, err)
	}

	// 更新任务状态
	taskInfo.Status = StatusCancelled
	endTime := time.Now()
	taskInfo.EndTime = &endTime

	m.logger.Infof("任务已停止: %s", taskID)
	return nil
}

// ListActiveTasks 列出活跃任务
func (m *EngineManager) ListActiveTasks() []*TaskInfo {
	m.taskMutex.RLock()
	defer m.taskMutex.RUnlock()

	tasks := make([]*TaskInfo, 0, len(m.activeTasks))
	for _, task := range m.activeTasks {
		// 返回任务信息的副本
		taskCopy := *task
		tasks = append(tasks, &taskCopy)
	}

	return tasks
}

// GetStatistics 获取管理器统计信息
func (m *EngineManager) GetStatistics() *ManagerStatistics {
	m.mutex.RLock()
	m.taskMutex.RLock()
	defer m.mutex.RUnlock()
	defer m.taskMutex.RUnlock()

	stats := &ManagerStatistics{
		TotalEngines:   len(m.engines),
		EnabledEngines: 0,
		RunningEngines: 0,
		ActiveTasks:    len(m.activeTasks),
	}

	// 统计引擎状态
	for _, engine := range m.engines {
		if engine.IsEnabled() {
			stats.EnabledEngines++
			stats.RunningEngines++ // 简化实现，假设启用的引擎都在运行
		}
	}

	// 统计任务信息
	var totalTime time.Duration
	var completedCount int64
	var failedCount int64
	var lastTaskTime time.Time

	for _, task := range m.activeTasks {
		switch task.Status {
		case StatusCompleted:
			completedCount++
			if task.EndTime != nil {
				duration := task.EndTime.Sub(task.StartTime)
				totalTime += duration
				if task.EndTime.After(lastTaskTime) {
					lastTaskTime = *task.EndTime
				}
			}
		case StatusFailed:
			failedCount++
			if task.EndTime != nil && task.EndTime.After(lastTaskTime) {
				lastTaskTime = *task.EndTime
			}
		}
	}

	stats.CompletedTasks = completedCount
	stats.FailedTasks = failedCount
	stats.TotalTasks = completedCount + failedCount + int64(len(m.activeTasks))
	stats.TotalScanTime = totalTime
	stats.LastTaskTime = lastTaskTime

	if completedCount > 0 {
		stats.AverageTaskTime = totalTime / time.Duration(completedCount)
	}

	return stats
}

// 私有方法

// selectEngine 选择合适的引擎
func (m *EngineManager) selectEngine(request *ScanRequest) (EngineType, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// 如果请求中指定了引擎类型，优先使用
	if request.Config != nil && len(request.Config.EnabledChecks) > 0 {
		for _, check := range request.Config.EnabledChecks {
			if engineType := EngineType(check); m.isEngineAvailable(engineType) {
				return engineType, nil
			}
		}
	}

	// 根据目标类型自动选择引擎
	for engineType, engine := range m.engines {
		m.logger.Infof("检查引擎 %s: 启用=%t, 可扫描=%t", engineType, engine.IsEnabled(), engine.CanScan(request.Target))
		if engine.IsEnabled() && engine.CanScan(request.Target) {
			m.logger.Infof("选择引擎 %s 处理目标 %s", engineType, request.Target.Type)
			return engineType, nil
		}
	}

	return "", fmt.Errorf("没有可用的引擎处理目标: %s", request.Target.Type)
}

// isEngineAvailable 检查引擎是否可用
func (m *EngineManager) isEngineAvailable(engineType EngineType) bool {
	engine, exists := m.engines[engineType]
	return exists && engine.IsEnabled()
}

// executeScan 执行扫描
func (m *EngineManager) executeScan(taskInfo *TaskInfo) {
	// 更新任务状态
	m.updateTaskStatus(taskInfo.ID, StatusRunning)

	// 获取引擎
	engine, err := m.GetEngine(taskInfo.EngineType)
	if err != nil {
		m.completeTask(taskInfo.ID, nil, fmt.Errorf("获取引擎失败: %v", err))
		return
	}

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), m.config.TaskTimeout)
	defer cancel()

	// 执行扫描
	result, err := engine.Scan(ctx, taskInfo.Request)

	// 完成任务
	m.completeTask(taskInfo.ID, result, err)
}

// updateTaskStatus 更新任务状态
func (m *EngineManager) updateTaskStatus(taskID string, status ScanStatusType) {
	m.taskMutex.Lock()
	defer m.taskMutex.Unlock()

	if taskInfo, exists := m.activeTasks[taskID]; exists {
		taskInfo.Status = status
		if status == StatusRunning && taskInfo.Progress == 0 {
			taskInfo.Progress = 1.0 // 开始执行
		}
	}
}

// completeTask 完成任务
func (m *EngineManager) completeTask(taskID string, result *ScanResult, err error) {
	m.taskMutex.Lock()
	defer m.taskMutex.Unlock()

	taskInfo, exists := m.activeTasks[taskID]
	if !exists {
		return
	}

	endTime := time.Now()
	taskInfo.EndTime = &endTime
	taskInfo.Result = result
	taskInfo.Error = err

	if err != nil {
		taskInfo.Status = StatusFailed
		m.logger.Errorf("任务执行失败 %s: %v", taskID, err)
	} else {
		taskInfo.Status = StatusCompleted
		taskInfo.Progress = 100.0
		m.logger.Infof("任务执行完成 %s", taskID)
	}
}

// runScheduler 运行调度器
func (m *EngineManager) runScheduler() {
	ticker := time.NewTicker(m.config.SchedulerInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if !m.running {
				return
			}
			m.scheduleCleanup()
		}
	}
}

// scheduleCleanup 调度清理
func (m *EngineManager) scheduleCleanup() {
	m.taskMutex.Lock()
	defer m.taskMutex.Unlock()

	// 清理完成的任务（保留1小时）
	cutoff := time.Now().Add(-1 * time.Hour)

	for taskID, task := range m.activeTasks {
		if task.EndTime != nil && task.EndTime.Before(cutoff) {
			delete(m.activeTasks, taskID)
		}
	}
}

// runHealthCheck 运行健康检查
func (m *EngineManager) runHealthCheck() {
	ticker := time.NewTicker(m.config.HealthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if !m.running {
				return
			}
			m.performHealthCheck()
		}
	}
}

// performHealthCheck 执行健康检查
func (m *EngineManager) performHealthCheck() {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	for engineType, engine := range m.engines {
		if !engine.IsEnabled() {
			continue
		}

		// 简单的健康检查：检查引擎是否响应
		// 实际实现可以更复杂，比如发送测试请求
		if engine.GetName() == "" {
			m.logger.Warnf("引擎健康检查失败: %s", engineType)
		}
	}
}
