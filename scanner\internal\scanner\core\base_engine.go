package core

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// BaseEngine 基础扫描引擎
// 提供所有扫描引擎的通用功能实现
type BaseEngine struct {
	// 基础信息
	name        string
	engineType  EngineType
	version     string
	description string
	
	// 状态管理
	enabled     bool
	initialized bool
	mutex       sync.RWMutex
	
	// 任务管理
	activeTasks map[string]*TaskContext
	taskMutex   sync.RWMutex
	
	// 配置
	defaultConfig *ScanConfig
	
	// 日志
	logger *logrus.Logger
	
	// 统计信息
	stats *EngineStatistics
}

// TaskContext 任务上下文
type TaskContext struct {
	ID          string
	Request     *ScanRequest
	Status      ScanStatusType
	Progress    float64
	StartTime   time.Time
	CancelFunc  context.CancelFunc
	Result      *ScanResult
	Error       error
	LastUpdate  time.Time
}

// EngineStatistics 引擎统计信息
type EngineStatistics struct {
	TotalScans      int64         `json:"total_scans"`      // 总扫描次数
	SuccessfulScans int64         `json:"successful_scans"` // 成功扫描次数
	FailedScans     int64         `json:"failed_scans"`     // 失败扫描次数
	AverageTime     time.Duration `json:"average_time"`     // 平均扫描时间
	TotalTime       time.Duration `json:"total_time"`       // 总扫描时间
	LastScanTime    time.Time     `json:"last_scan_time"`   // 最后扫描时间
	ActiveTasks     int           `json:"active_tasks"`     // 活跃任务数
}

// NewBaseEngine 创建基础引擎
func NewBaseEngine(name string, engineType EngineType, version, description string) *BaseEngine {
	return &BaseEngine{
		name:        name,
		engineType:  engineType,
		version:     version,
		description: description,
		enabled:     true,
		activeTasks: make(map[string]*TaskContext),
		logger:      logrus.New(),
		stats:       &EngineStatistics{},
		defaultConfig: &ScanConfig{
			Timeout:     30 * time.Minute,
			Concurrency: 10,
			Depth:       3,
			ScanMode:    ScanModeStandard,
			UserAgent:   "VulnScanner/1.0",
		},
	}
}

// GetName 获取引擎名称
func (e *BaseEngine) GetName() string {
	return e.name
}

// GetType 获取引擎类型
func (e *BaseEngine) GetType() EngineType {
	return e.engineType
}

// GetVersion 获取引擎版本
func (e *BaseEngine) GetVersion() string {
	return e.version
}

// GetDescription 获取引擎描述
func (e *BaseEngine) GetDescription() string {
	return e.description
}

// IsEnabled 检查引擎是否启用
func (e *BaseEngine) IsEnabled() bool {
	e.mutex.RLock()
	defer e.mutex.RUnlock()
	return e.enabled
}

// SetEnabled 设置引擎启用状态
func (e *BaseEngine) SetEnabled(enabled bool) {
	e.mutex.Lock()
	defer e.mutex.Unlock()
	e.enabled = enabled
}

// CanScan 检查是否能扫描指定目标
func (e *BaseEngine) CanScan(target *ScanTarget) bool {
	if !e.IsEnabled() {
		return false
	}
	
	supportedTargets := e.GetSupportedTargets()
	for _, supportedType := range supportedTargets {
		if target.Type == supportedType {
			return true
		}
	}
	return false
}

// GetSupportedTargets 获取支持的目标类型（子类需要重写）
func (e *BaseEngine) GetSupportedTargets() []TargetType {
	return []TargetType{TargetTypeURL}
}

// ValidateConfig 验证扫描配置
func (e *BaseEngine) ValidateConfig(config *ScanConfig) error {
	if config == nil {
		return fmt.Errorf("扫描配置不能为空")
	}
	
	if config.Timeout <= 0 {
		return fmt.Errorf("超时时间必须大于0")
	}
	
	if config.Concurrency <= 0 {
		return fmt.Errorf("并发数必须大于0")
	}
	
	if config.Depth < 0 {
		return fmt.Errorf("扫描深度不能为负数")
	}
	
	return nil
}

// GetDefaultConfig 获取默认配置
func (e *BaseEngine) GetDefaultConfig() *ScanConfig {
	e.mutex.RLock()
	defer e.mutex.RUnlock()
	
	// 返回配置的副本
	config := *e.defaultConfig
	config.CreatedAt = time.Now()
	return &config
}

// Initialize 初始化引擎
func (e *BaseEngine) Initialize() error {
	e.mutex.Lock()
	defer e.mutex.Unlock()
	
	if e.initialized {
		return nil
	}
	
	e.logger.Infof("初始化扫描引擎: %s", e.name)
	
	// 初始化统计信息
	e.stats = &EngineStatistics{}
	
	e.initialized = true
	e.logger.Infof("扫描引擎初始化完成: %s", e.name)
	
	return nil
}

// Cleanup 清理资源
func (e *BaseEngine) Cleanup() error {
	e.mutex.Lock()
	defer e.mutex.Unlock()
	
	e.logger.Infof("清理扫描引擎资源: %s", e.name)
	
	// 停止所有活跃任务
	e.taskMutex.Lock()
	for taskID, task := range e.activeTasks {
		if task.CancelFunc != nil {
			task.CancelFunc()
		}
		delete(e.activeTasks, taskID)
	}
	e.taskMutex.Unlock()
	
	e.initialized = false
	e.logger.Infof("扫描引擎资源清理完成: %s", e.name)
	
	return nil
}

// Scan 执行扫描（基础实现，子类需要重写）
func (e *BaseEngine) Scan(ctx context.Context, request *ScanRequest) (*ScanResult, error) {
	return nil, fmt.Errorf("子类必须实现Scan方法")
}

// Stop 停止扫描
func (e *BaseEngine) Stop(ctx context.Context, taskID string) error {
	e.taskMutex.Lock()
	defer e.taskMutex.Unlock()
	
	task, exists := e.activeTasks[taskID]
	if !exists {
		return fmt.Errorf("任务不存在: %s", taskID)
	}
	
	if task.CancelFunc != nil {
		task.CancelFunc()
	}
	
	task.Status = StatusCancelled
	task.LastUpdate = time.Now()
	
	e.logger.Infof("已停止扫描任务: %s", taskID)
	return nil
}

// GetStatus 获取扫描状态
func (e *BaseEngine) GetStatus(taskID string) (*ScanStatus, error) {
	e.taskMutex.RLock()
	defer e.taskMutex.RUnlock()
	
	task, exists := e.activeTasks[taskID]
	if !exists {
		return nil, fmt.Errorf("任务不存在: %s", taskID)
	}
	
	return &ScanStatus{
		TaskID:      taskID,
		Status:      task.Status,
		Progress:    task.Progress,
		CurrentStep: fmt.Sprintf("扫描进行中 - %s", e.name),
		Message:     fmt.Sprintf("任务状态: %s", task.Status),
		UpdatedAt:   task.LastUpdate,
	}, nil
}

// GetProgress 获取扫描进度
func (e *BaseEngine) GetProgress(taskID string) (*ScanProgress, error) {
	e.taskMutex.RLock()
	defer e.taskMutex.RUnlock()
	
	task, exists := e.activeTasks[taskID]
	if !exists {
		return nil, fmt.Errorf("任务不存在: %s", taskID)
	}
	
	return &ScanProgress{
		TaskID:        taskID,
		Stage:         e.name,
		Progress:      task.Progress,
		CurrentTarget: task.Request.Target.Value,
		Message:       fmt.Sprintf("正在执行%s扫描", e.name),
		Timestamp:     time.Now(),
	}, nil
}

// GetStatistics 获取引擎统计信息
func (e *BaseEngine) GetStatistics() *EngineStatistics {
	e.mutex.RLock()
	defer e.mutex.RUnlock()
	
	// 更新活跃任务数
	e.taskMutex.RLock()
	e.stats.ActiveTasks = len(e.activeTasks)
	e.taskMutex.RUnlock()
	
	// 返回统计信息的副本
	stats := *e.stats
	return &stats
}

// 受保护的方法，供子类使用

// CreateTaskContext 创建任务上下文
func (e *BaseEngine) CreateTaskContext(request *ScanRequest) (*TaskContext, context.Context) {
	ctx, cancel := context.WithTimeout(context.Background(), request.Config.Timeout)
	
	task := &TaskContext{
		ID:         request.ID,
		Request:    request,
		Status:     StatusPending,
		Progress:   0.0,
		StartTime:  time.Now(),
		CancelFunc: cancel,
		LastUpdate: time.Now(),
	}
	
	e.taskMutex.Lock()
	e.activeTasks[request.ID] = task
	e.taskMutex.Unlock()
	
	return task, ctx
}

// UpdateTaskProgress 更新任务进度
func (e *BaseEngine) UpdateTaskProgress(taskID string, progress float64, message string) {
	e.taskMutex.Lock()
	defer e.taskMutex.Unlock()
	
	if task, exists := e.activeTasks[taskID]; exists {
		task.Progress = progress
		task.LastUpdate = time.Now()
		if progress >= 100.0 {
			task.Status = StatusCompleted
		} else if task.Status == StatusPending {
			task.Status = StatusRunning
		}
	}
}

// UpdateTaskStatus 更新任务状态
func (e *BaseEngine) UpdateTaskStatus(taskID string, status ScanStatusType) {
	e.taskMutex.Lock()
	defer e.taskMutex.Unlock()
	
	if task, exists := e.activeTasks[taskID]; exists {
		task.Status = status
		task.LastUpdate = time.Now()
	}
}

// CompleteTask 完成任务
func (e *BaseEngine) CompleteTask(taskID string, result *ScanResult, err error) {
	e.taskMutex.Lock()
	defer e.taskMutex.Unlock()
	
	if task, exists := e.activeTasks[taskID]; exists {
		task.Result = result
		task.Error = err
		task.LastUpdate = time.Now()
		
		if err != nil {
			task.Status = StatusFailed
			e.stats.FailedScans++
		} else {
			task.Status = StatusCompleted
			e.stats.SuccessfulScans++
		}
		
		// 更新统计信息
		duration := time.Since(task.StartTime)
		e.stats.TotalScans++
		e.stats.TotalTime += duration
		e.stats.AverageTime = e.stats.TotalTime / time.Duration(e.stats.TotalScans)
		e.stats.LastScanTime = time.Now()
		
		// 清理任务上下文（可选，也可以保留一段时间用于查询）
		// delete(e.activeTasks, taskID)
	}
}

// LogInfo 记录信息日志
func (e *BaseEngine) LogInfo(format string, args ...interface{}) {
	e.logger.Infof("[%s] "+format, append([]interface{}{e.name}, args...)...)
}

// LogError 记录错误日志
func (e *BaseEngine) LogError(format string, args ...interface{}) {
	e.logger.Errorf("[%s] "+format, append([]interface{}{e.name}, args...)...)
}

// LogWarn 记录警告日志
func (e *BaseEngine) LogWarn(format string, args ...interface{}) {
	e.logger.Warnf("[%s] "+format, append([]interface{}{e.name}, args...)...)
}

// LogDebug 记录调试日志
func (e *BaseEngine) LogDebug(format string, args ...interface{}) {
	e.logger.Debugf("[%s] "+format, append([]interface{}{e.name}, args...)...)
}
