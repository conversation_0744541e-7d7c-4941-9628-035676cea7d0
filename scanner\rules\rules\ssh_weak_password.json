{"id": "ssh_weak_password", "name": "SSH弱密码检测", "description": "检测SSH服务是否使用弱密码或默认密码", "category": "weak_password", "severity": "high", "conditions": [{"type": "service_analysis", "field": "service.name", "operator": "equals", "value": "ssh", "case_sensitive": false, "metadata": {"description": "检测SSH服务"}}, {"type": "service_analysis", "field": "service.port", "operator": "equals", "value": 22, "metadata": {"description": "检测SSH默认端口"}}, {"type": "network_test", "field": "ssh.banner", "operator": "regex", "regex": "(?i)openssh[_\\s]+[5-6]\\.", "case_sensitive": false, "metadata": {"description": "检测旧版本OpenSSH"}}], "actions": [{"type": "create_vulnerability", "target": "weak_password", "parameters": {"name": "SSH弱密码漏洞", "description": "SSH服务可能使用弱密码，存在暴力破解风险", "impact": "攻击者可通过暴力破解获取系统访问权限", "solution": "使用强密码，启用密钥认证，限制登录尝试次数", "references": ["https://www.openssh.com/security.html", "https://wiki.mozilla.org/Security/Guidelines/OpenSSH"]}}], "tags": ["ssh", "weak-password", "brute-force", "authentication"], "references": ["https://www.openssh.com/security.html", "https://wiki.mozilla.org/Security/Guidelines/OpenSSH", "https://attack.mitre.org/techniques/T1110/"], "author": "VulnScanner Team", "version": "1.0.0", "enabled": true, "config": {"timeout": 5, "weak_passwords": ["", "root", "admin", "123456", "password", "toor"], "max_attempts": 3}}