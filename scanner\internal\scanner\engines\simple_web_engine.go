package engines

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"scanner/internal/scanner/types"
	"scanner/internal/services"
	"scanner/pkg/logger"
)

// SimpleWebEngine 简单Web扫描引擎
// 实现旧版本的ScanEngine接口，用于解决接口不匹配问题
type SimpleWebEngine struct {
	name       string
	enabled    bool
	logService *services.ScanLogService
	httpClient *http.Client
}

// NewSimpleWebEngine 创建简单Web扫描引擎
func NewSimpleWebEngine() *SimpleWebEngine {
	return &SimpleWebEngine{
		name:    "Simple Web Scanner",
		enabled: true,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// GetName 获取引擎名称
func (e *SimpleWebEngine) GetName() string {
	return e.name
}

// GetType 获取引擎类型
func (e *SimpleWebEngine) GetType() string {
	return "web"
}

// GetSupportedTargets 获取支持的目标类型
func (e *SimpleWebEngine) GetSupportedTargets() []string {
	return []string{"url", "domain"}
}

// IsEnabled 检查引擎是否启用
func (e *SimpleWebEngine) IsEnabled() bool {
	return e.enabled
}

// SetLogService 设置日志服务
func (e *SimpleWebEngine) SetLogService(logService *services.ScanLogService) {
	e.logService = logService
}

// Validate 验证扫描配置
func (e *SimpleWebEngine) Validate(config *types.ScanConfig) error {
	if config == nil {
		return fmt.Errorf("扫描配置不能为空")
	}
	return nil
}

// Scan 执行扫描
func (e *SimpleWebEngine) Scan(ctx context.Context, target *types.ScanTarget, config *types.ScanConfig, progress chan<- *types.ScanProgress) (*types.ScanResult, error) {
	logger.Infof("开始Web扫描: %s", target.Value)

	// 转换TaskID为uint
	taskIDUint := uint(0)
	if config.TaskID != "" {
		if id, err := strconv.ParseUint(config.TaskID, 10, 32); err == nil {
			taskIDUint = uint(id)
		}
	}

	// 记录开始日志
	if e.logService != nil && taskIDUint > 0 {
		e.logService.LogInfo(taskIDUint, "启动", target.Value, "开始Web扫描", 0)
	}

	// 发送初始进度
	if progress != nil {
		select {
		case progress <- &types.ScanProgress{
			TaskID:    config.TaskID,
			Stage:     "初始化",
			Progress:  10,
			Message:   "开始Web扫描",
			Timestamp: time.Now(),
		}:
		case <-ctx.Done():
			return nil, ctx.Err()
		}
	}

	// 解析目标URL
	targetURL := target.Value
	if !strings.HasPrefix(targetURL, "http://") && !strings.HasPrefix(targetURL, "https://") {
		targetURL = "http://" + targetURL
	}

	// 发送进度更新
	if progress != nil {
		select {
		case progress <- &types.ScanProgress{
			TaskID:    config.TaskID,
			Stage:     "连接测试",
			Progress:  30,
			Message:   "测试目标连接",
			Timestamp: time.Now(),
		}:
		case <-ctx.Done():
			return nil, ctx.Err()
		}
	}

	// 执行基础HTTP请求
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		logger.Errorf("创建请求失败: %v", err)
		if e.logService != nil && taskIDUint > 0 {
			e.logService.LogError(taskIDUint, "请求", target.Value, "创建请求失败", err.Error(), 0)
		}
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置User-Agent
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	// 执行请求
	resp, err := e.httpClient.Do(req)
	if err != nil {
		logger.Errorf("请求执行失败: %v", err)
		if e.logService != nil && taskIDUint > 0 {
			e.logService.LogError(taskIDUint, "请求", target.Value, "请求执行失败", err.Error(), 30)
		}
		return nil, fmt.Errorf("请求执行失败: %v", err)
	}
	defer resp.Body.Close()

	// 发送进度更新
	if progress != nil {
		select {
		case progress <- &types.ScanProgress{
			TaskID:    config.TaskID,
			Stage:     "响应分析",
			Progress:  60,
			Message:   fmt.Sprintf("收到响应，状态码: %d", resp.StatusCode),
			Timestamp: time.Now(),
		}:
		case <-ctx.Done():
			return nil, ctx.Err()
		}
	}

	// 记录响应信息
	if e.logService != nil && taskIDUint > 0 {
		e.logService.LogInfo(taskIDUint, "响应", target.Value,
			fmt.Sprintf("状态码: %d, 服务器: %s", resp.StatusCode, resp.Header.Get("Server")), 60)
	}

	// 模拟一些基础检测
	vulnerabilities := []*types.Vulnerability{}

	// 检查HTTP安全头
	if progress != nil {
		select {
		case progress <- &types.ScanProgress{
			TaskID:    config.TaskID,
			Stage:     "安全检测",
			Progress:  80,
			Message:   "检查HTTP安全头",
			Timestamp: time.Now(),
		}:
		case <-ctx.Done():
			return nil, ctx.Err()
		}
	}

	// 检查缺失的安全头
	securityHeaders := map[string]string{
		"X-Frame-Options":           "点击劫持保护",
		"X-Content-Type-Options":    "MIME类型嗅探保护",
		"X-XSS-Protection":          "XSS保护",
		"Strict-Transport-Security": "HTTPS强制",
		"Content-Security-Policy":   "内容安全策略",
	}

	for header, description := range securityHeaders {
		if resp.Header.Get(header) == "" {
			vuln := &types.Vulnerability{
				ID:          fmt.Sprintf("missing-%s", strings.ToLower(header)),
				Name:        fmt.Sprintf("缺失%s安全头", description),
				Description: fmt.Sprintf("目标缺失%s安全头，可能存在安全风险", description),
				Severity:    "low",
				Type:        "configuration",
				Evidence:    fmt.Sprintf("响应头中未发现%s", header),
				Solution:    fmt.Sprintf("建议在服务器配置中添加%s安全头", header),
				References:  []string{"https://owasp.org/www-project-secure-headers/"},
				CreatedAt:   time.Now(),
			}
			vulnerabilities = append(vulnerabilities, vuln)
		}
	}

	// 发送最终进度
	if progress != nil {
		select {
		case progress <- &types.ScanProgress{
			TaskID:    config.TaskID,
			Stage:     "完成",
			Progress:  100,
			Message:   fmt.Sprintf("扫描完成，发现%d个问题", len(vulnerabilities)),
			Timestamp: time.Now(),
		}:
		case <-ctx.Done():
			return nil, ctx.Err()
		}
	}

	// 记录完成日志
	if e.logService != nil && taskIDUint > 0 {
		e.logService.LogInfo(taskIDUint, "完成", target.Value,
			fmt.Sprintf("Web扫描完成，发现%d个安全问题", len(vulnerabilities)), 100)
	}

	// 构建扫描结果
	result := &types.ScanResult{
		TaskID:          config.TaskID,
		Status:          "completed",
		Progress:        100,
		Vulnerabilities: vulnerabilities,
		Metadata: map[string]interface{}{
			"total_vulnerabilities": len(vulnerabilities),
			"status_code":           resp.StatusCode,
			"server":                resp.Header.Get("Server"),
			"content_type":          resp.Header.Get("Content-Type"),
		},
		StartTime: time.Now().Add(-30 * time.Second), // 模拟开始时间
		EndTime:   time.Now(),
		Duration:  30 * time.Second,
	}

	logger.Infof("Web扫描完成: %s, 发现%d个问题", target.Value, len(vulnerabilities))
	return result, nil
}

// Stop 停止扫描
func (e *SimpleWebEngine) Stop(ctx context.Context, taskID string) error {
	logger.Infof("停止Web扫描任务: %s", taskID)

	// 转换TaskID为uint
	if taskIDUint, err := strconv.ParseUint(taskID, 10, 32); err == nil && e.logService != nil {
		e.logService.LogInfo(uint(taskIDUint), "停止", "", "Web扫描任务已停止", 0)
	}

	return nil
}
