package main

import (
	"database/sql"
	"fmt"
	"log"
	"time"

	_ "github.com/mattn/go-sqlite3"
)

func main() {
	// 连接数据库
	db, err := sql.Open("sqlite3", "data/scanner.db")
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}
	defer db.Close()

	fmt.Println("=== 所有扫描任务状态检查 ===")
	fmt.Printf("检查时间: %s\n\n", time.Now().Format("2006-01-02 15:04:05"))

	// 查询所有任务
	rows, err := db.Query(`
		SELECT id, name, scan_type, status, progress, targets, created_at, updated_at 
		FROM scan_tasks 
		ORDER BY id DESC 
		LIMIT 10
	`)
	if err != nil {
		log.Fatal("查询任务失败:", err)
	}
	defer rows.Close()

	fmt.Printf("%-4s %-20s %-10s %-12s %-8s %-30s %-16s\n", 
		"ID", "任务名称", "类型", "状态", "进度", "目标", "创建时间")
	fmt.Println("================================================================================")

	var allTasks []TaskInfo
	for rows.Next() {
		var task TaskInfo
		err := rows.Scan(&task.ID, &task.Name, &task.ScanType, &task.Status, 
			&task.Progress, &task.Targets, &task.CreatedAt, &task.UpdatedAt)
		if err != nil {
			continue
		}
		
		allTasks = append(allTasks, task)
		
		// 截断长字段
		name := truncateString(task.Name, 18)
		targets := truncateString(task.Targets, 28)
		
		// 根据状态显示不同颜色标记
		statusMark := getStatusMark(task.Status)
		
		fmt.Printf("%-4d %-20s %-10s %s%-11s %-8d%% %-30s %-16s\n", 
			task.ID, name, task.ScanType, statusMark, task.Status, 
			task.Progress, targets, formatTime(task.CreatedAt))
	}

	if len(allTasks) == 0 {
		fmt.Println("数据库中没有任务记录")
		return
	}

	// 统计任务状态
	fmt.Printf("\n📊 任务状态统计:\n")
	statusCount := make(map[string]int)
	for _, task := range allTasks {
		statusCount[task.Status]++
	}

	for status, count := range statusCount {
		fmt.Printf("  %s %s: %d个\n", getStatusMark(status), status, count)
	}

	// 查找最近的任务
	if len(allTasks) > 0 {
		latestTask := allTasks[0]
		fmt.Printf("\n🔍 最新任务详情 (ID: %d):\n", latestTask.ID)
		fmt.Printf("  任务名称: %s\n", latestTask.Name)
		fmt.Printf("  扫描类型: %s\n", latestTask.ScanType)
		fmt.Printf("  任务状态: %s\n", latestTask.Status)
		fmt.Printf("  任务进度: %d%%\n", latestTask.Progress)
		fmt.Printf("  扫描目标: %s\n", latestTask.Targets)
		fmt.Printf("  创建时间: %s\n", latestTask.CreatedAt)
		fmt.Printf("  更新时间: %s\n", latestTask.UpdatedAt)

		// 查询该任务的日志
		checkTaskLogs(db, latestTask.ID)

		// 如果任务状态异常，进行分析
		if latestTask.Status == "failed" || latestTask.Status == "running" {
			analyzeTaskIssues(db, latestTask)
		}
	}

	// 检查系统整体状态
	fmt.Printf("\n🖥️ 系统状态检查:\n")
	checkSystemHealth(db)
}

type TaskInfo struct {
	ID        int
	Name      string
	ScanType  string
	Status    string
	Progress  int
	Targets   string
	CreatedAt string
	UpdatedAt string
}

func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen-3] + "..."
}

func getStatusMark(status string) string {
	switch status {
	case "completed":
		return "✅"
	case "failed":
		return "❌"
	case "running":
		return "🔄"
	case "pending":
		return "⏳"
	case "cancelled":
		return "🚫"
	default:
		return "❓"
	}
}

func formatTime(timeStr string) string {
	t, err := time.Parse("2006-01-02T15:04:05.999999999Z07:00", timeStr)
	if err != nil {
		t, err = time.Parse("2006-01-02 15:04:05", timeStr)
		if err != nil {
			return timeStr[:16]
		}
	}
	return t.Format("01-02 15:04")
}

func checkTaskLogs(db *sql.DB, taskID int) {
	fmt.Printf("\n📝 任务日志摘要:\n")
	
	// 查询日志统计
	var totalLogs, errorLogs int
	db.QueryRow("SELECT COUNT(*) FROM scan_logs WHERE task_id = ?", taskID).Scan(&totalLogs)
	db.QueryRow(`
		SELECT COUNT(*) FROM scan_logs 
		WHERE task_id = ? AND (level = 'ERROR' OR message LIKE '%失败%' OR message LIKE '%错误%')
	`, taskID).Scan(&errorLogs)

	fmt.Printf("  总日志数: %d条\n", totalLogs)
	fmt.Printf("  错误日志: %d条\n", errorLogs)

	if totalLogs > 0 {
		// 查询最新的几条日志
		logRows, err := db.Query(`
			SELECT level, stage, message, created_at
			FROM scan_logs 
			WHERE task_id = ?
			ORDER BY created_at DESC 
			LIMIT 3
		`, taskID)

		if err == nil {
			defer logRows.Close()
			fmt.Printf("  最新日志:\n")
			for logRows.Next() {
				var level, stage, message, logTime string
				if logRows.Scan(&level, &stage, &message, &logTime) == nil {
					fmt.Printf("    [%s] %s: %s\n", 
						formatTime(logTime), stage, truncateString(message, 50))
				}
			}
		}
	}
}

func analyzeTaskIssues(db *sql.DB, task TaskInfo) {
	fmt.Printf("\n🔍 任务问题分析:\n")

	if task.Status == "failed" {
		fmt.Printf("  ❌ 任务失败分析:\n")
		
		// 查找失败相关日志
		var failureReason string
		err := db.QueryRow(`
			SELECT message FROM scan_logs 
			WHERE task_id = ? AND (message LIKE '%失败%' OR message LIKE '%错误%' OR level = 'ERROR')
			ORDER BY created_at DESC 
			LIMIT 1
		`, task.ID).Scan(&failureReason)

		if err == nil {
			fmt.Printf("    失败原因: %s\n", failureReason)
		} else {
			fmt.Printf("    未找到明确的失败原因\n")
		}
	}

	if task.Status == "running" {
		fmt.Printf("  🔄 运行中任务分析:\n")
		
		// 检查任务是否卡住
		var lastUpdateTime string
		err := db.QueryRow(`
			SELECT created_at FROM scan_logs 
			WHERE task_id = ? 
			ORDER BY created_at DESC 
			LIMIT 1
		`, task.ID).Scan(&lastUpdateTime)

		if err == nil {
			lastUpdate, _ := time.Parse("2006-01-02T15:04:05.999999999Z07:00", lastUpdateTime)
			timeSinceUpdate := time.Since(lastUpdate)
			
			fmt.Printf("    最后更新: %s前\n", timeSinceUpdate.String())
			
			if timeSinceUpdate > 10*time.Minute {
				fmt.Printf("    ⚠️ 警告: 任务可能已卡住（超过10分钟无更新）\n")
			}
		}
	}
}

func checkSystemHealth(db *sql.DB) {
	// 检查最近任务的成功率
	var recentTotal, recentFailed int
	db.QueryRow(`
		SELECT COUNT(*) FROM scan_tasks 
		WHERE created_at > datetime('now', '-24 hours')
	`).Scan(&recentTotal)

	db.QueryRow(`
		SELECT COUNT(*) FROM scan_tasks 
		WHERE created_at > datetime('now', '-24 hours') AND status = 'failed'
	`).Scan(&recentFailed)

	if recentTotal > 0 {
		successRate := float64(recentTotal-recentFailed) / float64(recentTotal) * 100
		fmt.Printf("  24小时任务成功率: %.1f%% (%d成功/%d总计)\n", 
			successRate, recentTotal-recentFailed, recentTotal)
	} else {
		fmt.Printf("  24小时内无任务执行\n")
	}

	// 检查数据库大小
	var dbSize int64
	db.QueryRow("SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()").Scan(&dbSize)
	fmt.Printf("  数据库大小: %.2f MB\n", float64(dbSize)/1024/1024)

	// 检查表记录数
	var taskCount, logCount int
	db.QueryRow("SELECT COUNT(*) FROM scan_tasks").Scan(&taskCount)
	db.QueryRow("SELECT COUNT(*) FROM scan_logs").Scan(&logCount)
	fmt.Printf("  任务记录数: %d\n", taskCount)
	fmt.Printf("  日志记录数: %d\n", logCount)
}
