package core

import (
	"context"
	"time"
)

// ScanEngine 扫描引擎核心接口
// 所有扫描引擎必须实现此接口
type ScanEngine interface {
	// 基础信息
	GetName() string                    // 获取引擎名称
	GetType() EngineType               // 获取引擎类型
	GetVersion() string                // 获取引擎版本
	GetDescription() string            // 获取引擎描述
	
	// 能力检查
	IsEnabled() bool                   // 检查引擎是否启用
	CanScan(target *ScanTarget) bool   // 检查是否能扫描指定目标
	GetSupportedTargets() []TargetType // 获取支持的目标类型
	
	// 配置管理
	ValidateConfig(config *ScanConfig) error // 验证扫描配置
	GetDefaultConfig() *ScanConfig           // 获取默认配置
	
	// 扫描执行
	Scan(ctx context.Context, request *ScanRequest) (*ScanResult, error) // 执行扫描
	Stop(ctx context.Context, taskID string) error                       // 停止扫描
	
	// 状态管理
	GetStatus(taskID string) (*ScanStatus, error) // 获取扫描状态
	GetProgress(taskID string) (*ScanProgress, error) // 获取扫描进度
	
	// 生命周期
	Initialize() error // 初始化引擎
	Cleanup() error    // 清理资源
}

// EngineType 引擎类型枚举
type EngineType string

const (
	EngineTypeInformationGathering EngineType = "information_gathering" // 信息收集引擎
	EngineTypeWebVulnerability     EngineType = "web_vulnerability"     // Web漏洞扫描引擎
	EngineTypeNetworkVulnerability EngineType = "network_vulnerability" // 网络漏洞扫描引擎
	EngineTypeCVEScanning          EngineType = "cve_scanning"          // CVE漏洞扫描引擎
	EngineTypeCompliance           EngineType = "compliance"            // 合规检查引擎
	EngineTypeCustom               EngineType = "custom"                // 自定义引擎
)

// TargetType 目标类型枚举
type TargetType string

const (
	TargetTypeURL     TargetType = "url"     // URL目标
	TargetTypeIP      TargetType = "ip"      // IP地址目标
	TargetTypeDomain  TargetType = "domain"  // 域名目标
	TargetTypeNetwork TargetType = "network" // 网络段目标
	TargetTypeHost    TargetType = "host"    // 主机目标
)

// ScanTarget 扫描目标
type ScanTarget struct {
	ID          string            `json:"id"`          // 目标ID
	Type        TargetType        `json:"type"`        // 目标类型
	Value       string            `json:"value"`       // 目标值
	Description string            `json:"description"` // 目标描述
	Metadata    map[string]string `json:"metadata"`    // 目标元数据
	CreatedAt   time.Time         `json:"created_at"`  // 创建时间
}

// ScanConfig 扫描配置
type ScanConfig struct {
	// 基础配置
	TaskID      string            `json:"task_id"`      // 任务ID
	Timeout     time.Duration     `json:"timeout"`      // 超时时间
	Concurrency int               `json:"concurrency"`  // 并发数
	Depth       int               `json:"depth"`        // 扫描深度
	
	// 扫描选项
	EnabledChecks   []string          `json:"enabled_checks"`   // 启用的检查项
	DisabledChecks  []string          `json:"disabled_checks"`  // 禁用的检查项
	ScanMode        ScanMode          `json:"scan_mode"`        // 扫描模式
	
	// 高级配置
	CustomOptions   map[string]interface{} `json:"custom_options"`   // 自定义选项
	Headers         map[string]string      `json:"headers"`          // HTTP头
	UserAgent       string                 `json:"user_agent"`       // User-Agent
	
	// 输出配置
	OutputFormat    string            `json:"output_format"`    // 输出格式
	ReportTemplate  string            `json:"report_template"`  // 报告模板
	
	CreatedAt       time.Time         `json:"created_at"`       // 创建时间
}

// ScanMode 扫描模式枚举
type ScanMode string

const (
	ScanModeQuick       ScanMode = "quick"       // 快速扫描
	ScanModeStandard    ScanMode = "standard"    // 标准扫描
	ScanModeDeep        ScanMode = "deep"        // 深度扫描
	ScanModeCustom      ScanMode = "custom"      // 自定义扫描
)

// ScanRequest 扫描请求
type ScanRequest struct {
	ID          string       `json:"id"`          // 请求ID
	Target      *ScanTarget  `json:"target"`      // 扫描目标
	Config      *ScanConfig  `json:"config"`      // 扫描配置
	Priority    int          `json:"priority"`    // 优先级
	CreatedAt   time.Time    `json:"created_at"`  // 创建时间
	StartedAt   *time.Time   `json:"started_at"`  // 开始时间
}

// ScanResult 扫描结果
type ScanResult struct {
	ID            string                 `json:"id"`            // 结果ID
	TaskID        string                 `json:"task_id"`       // 任务ID
	EngineType    EngineType             `json:"engine_type"`   // 引擎类型
	Target        *ScanTarget            `json:"target"`        // 扫描目标
	
	// 扫描状态
	Status        ScanStatusType         `json:"status"`        // 扫描状态
	Progress      float64                `json:"progress"`      // 扫描进度 (0-100)
	
	// 时间信息
	StartedAt     time.Time              `json:"started_at"`    // 开始时间
	CompletedAt   *time.Time             `json:"completed_at"`  // 完成时间
	Duration      time.Duration          `json:"duration"`      // 扫描耗时
	
	// 扫描结果
	Vulnerabilities []*Vulnerability     `json:"vulnerabilities"` // 发现的漏洞
	Information     *InformationResult   `json:"information"`     // 信息收集结果
	Statistics      *ScanStatistics      `json:"statistics"`      // 扫描统计
	
	// 错误信息
	Error         *ScanError             `json:"error"`         // 扫描错误
	Warnings      []string               `json:"warnings"`      // 警告信息
	
	// 元数据
	Metadata      map[string]interface{} `json:"metadata"`      // 结果元数据
}

// ScanStatus 扫描状态
type ScanStatus struct {
	TaskID      string         `json:"task_id"`      // 任务ID
	Status      ScanStatusType `json:"status"`       // 状态
	Progress    float64        `json:"progress"`     // 进度
	CurrentStep string         `json:"current_step"` // 当前步骤
	Message     string         `json:"message"`      // 状态消息
	UpdatedAt   time.Time      `json:"updated_at"`   // 更新时间
}

// ScanStatusType 扫描状态类型枚举
type ScanStatusType string

const (
	StatusPending    ScanStatusType = "pending"    // 等待中
	StatusRunning    ScanStatusType = "running"    // 运行中
	StatusCompleted  ScanStatusType = "completed"  // 已完成
	StatusFailed     ScanStatusType = "failed"     // 失败
	StatusCancelled  ScanStatusType = "cancelled"  // 已取消
	StatusTimeout    ScanStatusType = "timeout"    // 超时
)

// ScanProgress 扫描进度
type ScanProgress struct {
	TaskID        string    `json:"task_id"`        // 任务ID
	Stage         string    `json:"stage"`          // 当前阶段
	Progress      float64   `json:"progress"`       // 进度百分比
	CurrentTarget string    `json:"current_target"` // 当前目标
	Message       string    `json:"message"`        // 进度消息
	Timestamp     time.Time `json:"timestamp"`      // 时间戳
}

// Vulnerability 漏洞信息
type Vulnerability struct {
	ID          string            `json:"id"`          // 漏洞ID
	Name        string            `json:"name"`        // 漏洞名称
	Type        string            `json:"type"`        // 漏洞类型
	Severity    SeverityLevel     `json:"severity"`    // 严重程度
	CVSS        float64           `json:"cvss"`        // CVSS评分
	CVE         string            `json:"cve"`         // CVE编号
	
	// 位置信息
	URL         string            `json:"url"`         // 漏洞URL
	Parameter   string            `json:"parameter"`   // 漏洞参数
	Method      string            `json:"method"`      // HTTP方法
	
	// 详细信息
	Description string            `json:"description"` // 漏洞描述
	Impact      string            `json:"impact"`      // 影响描述
	Solution    string            `json:"solution"`    // 修复建议
	References  []string          `json:"references"`  // 参考链接
	
	// 证据信息
	Evidence    *VulnEvidence     `json:"evidence"`    // 漏洞证据
	Payload     string            `json:"payload"`     // 攻击载荷
	Response    string            `json:"response"`    // 响应内容
	
	// 元数据
	Confidence  float64           `json:"confidence"`  // 置信度
	Risk        RiskLevel         `json:"risk"`        // 风险等级
	Tags        []string          `json:"tags"`        // 标签
	Metadata    map[string]string `json:"metadata"`    // 元数据
	
	// 时间信息
	DiscoveredAt time.Time        `json:"discovered_at"` // 发现时间
}

// SeverityLevel 严重程度枚举
type SeverityLevel string

const (
	SeverityCritical SeverityLevel = "critical" // 严重
	SeverityHigh     SeverityLevel = "high"     // 高危
	SeverityMedium   SeverityLevel = "medium"   // 中危
	SeverityLow      SeverityLevel = "low"      // 低危
	SeverityInfo     SeverityLevel = "info"     // 信息
)

// RiskLevel 风险等级枚举
type RiskLevel string

const (
	RiskCritical RiskLevel = "critical" // 严重风险
	RiskHigh     RiskLevel = "high"     // 高风险
	RiskMedium   RiskLevel = "medium"   // 中风险
	RiskLow      RiskLevel = "low"      // 低风险
	RiskInfo     RiskLevel = "info"     // 信息风险
)

// VulnEvidence 漏洞证据
type VulnEvidence struct {
	Type        string            `json:"type"`        // 证据类型
	Content     string            `json:"content"`     // 证据内容
	Location    string            `json:"location"`    // 证据位置
	Highlights  []string          `json:"highlights"`  // 高亮内容
	Screenshots []string          `json:"screenshots"` // 截图
	Metadata    map[string]string `json:"metadata"`    // 证据元数据
}

// InformationResult 信息收集结果
type InformationResult struct {
	// 目标信息
	TargetInfo    *TargetInformation    `json:"target_info"`    // 目标基础信息
	
	// 技术栈信息
	TechStack     *TechnologyStack      `json:"tech_stack"`     // 技术栈
	Services      []*ServiceInfo        `json:"services"`       // 服务信息
	
	// 网络信息
	NetworkInfo   *NetworkInformation   `json:"network_info"`   // 网络信息
	DNSInfo       *DNSInformation       `json:"dns_info"`       // DNS信息
	
	// 安全信息
	SecurityInfo  *SecurityInformation  `json:"security_info"`  // 安全信息
	Certificates  []*CertificateInfo    `json:"certificates"`   // 证书信息
	
	// 内容信息
	ContentInfo   *ContentInformation   `json:"content_info"`   // 内容信息
	Files         []*FileInfo           `json:"files"`          // 文件信息
	
	// 统计信息
	Statistics    *InfoStatistics       `json:"statistics"`     // 统计信息
}

// ScanStatistics 扫描统计
type ScanStatistics struct {
	// 扫描统计
	TotalTargets      int           `json:"total_targets"`      // 总目标数
	ScannedTargets    int           `json:"scanned_targets"`    // 已扫描目标数
	FailedTargets     int           `json:"failed_targets"`     // 失败目标数
	
	// 漏洞统计
	TotalVulns        int           `json:"total_vulns"`        // 总漏洞数
	CriticalVulns     int           `json:"critical_vulns"`     // 严重漏洞数
	HighVulns         int           `json:"high_vulns"`         // 高危漏洞数
	MediumVulns       int           `json:"medium_vulns"`       // 中危漏洞数
	LowVulns          int           `json:"low_vulns"`          // 低危漏洞数
	InfoVulns         int           `json:"info_vulns"`         // 信息漏洞数
	
	// 性能统计
	RequestsSent      int           `json:"requests_sent"`      // 发送请求数
	ResponsesReceived int           `json:"responses_received"` // 接收响应数
	ErrorsEncountered int           `json:"errors_encountered"` // 遇到错误数
	
	// 时间统计
	ScanDuration      time.Duration `json:"scan_duration"`      // 扫描耗时
	AverageResponse   time.Duration `json:"average_response"`   // 平均响应时间
}

// ScanError 扫描错误
type ScanError struct {
	Code      string            `json:"code"`      // 错误代码
	Message   string            `json:"message"`   // 错误消息
	Details   string            `json:"details"`   // 错误详情
	Timestamp time.Time         `json:"timestamp"` // 错误时间
	Context   map[string]string `json:"context"`   // 错误上下文
}
