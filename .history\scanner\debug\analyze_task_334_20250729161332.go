package main

import (
	"database/sql"
	"fmt"
	"log"
	"time"

	_ "github.com/mattn/go-sqlite3"
)

func main() {
	// 连接数据库
	db, err := sql.Open("sqlite3", "data/scanner.db")
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}
	defer db.Close()

	taskID := 334
	fmt.Printf("=== 任务 %d 详细分析 ===\n", taskID)
	fmt.Printf("分析时间: %s\n\n", time.Now().Format("2006-01-02 15:04:05"))

	// 查询任务详情
	var name, taskType, status, targets string
	var errorMessage sql.NullString
	var progress int
	var createdAt, updatedAt string
	var startTime, endTime sql.NullString

	err = db.QueryRow(`
		SELECT name, type, status, targets, progress, created_at, updated_at,
		       start_time, end_time, error_message
		FROM scan_tasks
		WHERE id = ?
	`, taskID).Scan(&name, &taskType, &status, &targets, &progress,
		&createdAt, &updatedAt, &startTime, &endTime, &errorMessage)

	if err != nil {
		log.Fatal("查询任务失败:", err)
	}

	fmt.Printf("📋 任务基本信息:\n")
	fmt.Printf("  ID: %d\n", taskID)
	fmt.Printf("  名称: %s\n", name)
	fmt.Printf("  类型: %s\n", taskType)
	fmt.Printf("  状态: %s\n", status)
	fmt.Printf("  目标: %s\n", targets)
	fmt.Printf("  进度: %d%%\n", progress)
	fmt.Printf("  创建时间: %s\n", createdAt)
	fmt.Printf("  更新时间: %s\n", updatedAt)
	fmt.Printf("  开始时间: %s\n", formatNullableString(startTime))
	fmt.Printf("  结束时间: %s\n", formatNullableString(endTime))
	if errorMessage.Valid && errorMessage.String != "" {
		fmt.Printf("  错误信息: %s\n", errorMessage.String)
	}

	// 计算执行时长
	if startTime.Valid && endTime.Valid {
		duration := calculateDuration(startTime.String, endTime.String)
		fmt.Printf("  执行时长: %s\n", duration)
	}

	// 查询所有日志，按时间顺序
	fmt.Printf("\n📝 完整执行日志:\n")
	fmt.Println("================================================================================")

	logRows, err := db.Query(`
		SELECT level, stage, target, message, progress, created_at
		FROM scan_logs 
		WHERE task_id = ?
		ORDER BY created_at ASC
	`, taskID)

	if err != nil {
		log.Fatal("查询日志失败:", err)
	}
	defer logRows.Close()

	logCount := 0
	var stages []string
	var lastStage string
	var firstLogTime, lastLogTime string

	for logRows.Next() {
		var level, stage, target, message, logTime string
		var logProgress int

		err := logRows.Scan(&level, &stage, &target, &message, &logProgress, &logTime)
		if err != nil {
			continue
		}

		logCount++
		if logCount == 1 {
			firstLogTime = logTime
		}
		lastLogTime = logTime

		// 记录阶段变化
		if stage != lastStage {
			stages = append(stages, stage)
			lastStage = stage
		}

		// 格式化时间
		timeStr := formatTime(logTime)

		// 根据级别显示不同标记
		levelMark := getLevelMark(level)

		fmt.Printf("%s [%s] [%s] %s -> %s: %s (进度:%d%%)\n",
			levelMark, timeStr, level, stage, target, message, logProgress)
	}

	fmt.Printf("\n📊 执行统计:\n")
	fmt.Printf("  日志总数: %d条\n", logCount)
	fmt.Printf("  执行阶段: %v\n", stages)
	if firstLogTime != "" && lastLogTime != "" {
		totalDuration := calculateDuration(firstLogTime, lastLogTime)
		fmt.Printf("  日志时间跨度: %s\n", totalDuration)
	}

	// 分析失败原因
	fmt.Printf("\n🔍 失败原因分析:\n")
	analyzeFailure(db, taskID)

	// 检查相关系统状态
	fmt.Printf("\n🖥️ 系统状态检查:\n")
	checkSystemStatus(db, taskID)

	// 提供修复建议
	fmt.Printf("\n💡 修复建议:\n")
	provideSuggestions(stages, progress, status)
}

func formatNullableTime(timeStr string) string {
	if timeStr == "" {
		return "未设置"
	}
	return formatTime(timeStr)
}

func formatTime(timeStr string) string {
	t, err := time.Parse("2006-01-02T15:04:05.999999999Z07:00", timeStr)
	if err != nil {
		t, err = time.Parse("2006-01-02 15:04:05", timeStr)
		if err != nil {
			return timeStr
		}
	}
	return t.Format("15:04:05.000")
}

func calculateDuration(start, end string) string {
	startTime, err1 := time.Parse("2006-01-02T15:04:05.999999999Z07:00", start)
	endTime, err2 := time.Parse("2006-01-02T15:04:05.999999999Z07:00", end)

	if err1 != nil || err2 != nil {
		return "无法计算"
	}

	duration := endTime.Sub(startTime)
	return duration.String()
}

func getLevelMark(level string) string {
	switch level {
	case "ERROR":
		return "❌"
	case "WARN":
		return "⚠️"
	case "INFO":
		return "ℹ️"
	case "DEBUG":
		return "🔍"
	default:
		return "📝"
	}
}

func analyzeFailure(db *sql.DB, taskID int) {
	// 检查是否有明确的错误信息
	var errorLogs []string
	logRows, err := db.Query(`
		SELECT message FROM scan_logs 
		WHERE task_id = ? AND (
			level = 'ERROR' OR 
			message LIKE '%失败%' OR 
			message LIKE '%错误%' OR 
			message LIKE '%failed%' OR
			message LIKE '%error%'
		)
		ORDER BY created_at ASC
	`, taskID)

	if err == nil {
		defer logRows.Close()
		for logRows.Next() {
			var message string
			if logRows.Scan(&message) == nil {
				errorLogs = append(errorLogs, message)
			}
		}
	}

	if len(errorLogs) > 0 {
		fmt.Printf("  🚨 发现错误日志:\n")
		for i, errMsg := range errorLogs {
			fmt.Printf("    %d. %s\n", i+1, errMsg)
		}
	} else {
		fmt.Printf("  ❓ 未发现明确的错误日志\n")
	}

	// 检查任务是否正常启动
	var startupLogs int
	db.QueryRow(`
		SELECT COUNT(*) FROM scan_logs 
		WHERE task_id = ? AND (stage LIKE '%启动%' OR stage LIKE '%开始%')
	`, taskID).Scan(&startupLogs)

	// 检查任务是否有监控日志
	var monitorLogs int
	db.QueryRow(`
		SELECT COUNT(*) FROM scan_logs 
		WHERE task_id = ? AND stage LIKE '%监控%'
	`, taskID).Scan(&monitorLogs)

	fmt.Printf("  📊 日志分析:\n")
	fmt.Printf("    启动相关日志: %d条\n", startupLogs)
	fmt.Printf("    监控相关日志: %d条\n", monitorLogs)

	// 分析失败模式
	if monitorLogs > 0 && len(errorLogs) == 0 {
		fmt.Printf("  🔍 失败模式: 监控系统检测到异常\n")
		fmt.Printf("    说明: 任务可能启动成功但被监控系统判定为失败\n")
	} else if startupLogs == 0 {
		fmt.Printf("  🔍 失败模式: 任务启动失败\n")
	} else {
		fmt.Printf("  🔍 失败模式: 执行过程中失败\n")
	}
}

func checkSystemStatus(db *sql.DB, taskID int) {
	// 检查同时期其他任务的状态
	var samePeriodTasks, samePeriodFailed int
	db.QueryRow(`
		SELECT COUNT(*) FROM scan_tasks 
		WHERE created_at BETWEEN 
			(SELECT datetime(created_at, '-1 hour') FROM scan_tasks WHERE id = ?) AND
			(SELECT datetime(created_at, '+1 hour') FROM scan_tasks WHERE id = ?)
	`, taskID, taskID).Scan(&samePeriodTasks)

	db.QueryRow(`
		SELECT COUNT(*) FROM scan_tasks 
		WHERE status = 'failed' AND created_at BETWEEN 
			(SELECT datetime(created_at, '-1 hour') FROM scan_tasks WHERE id = ?) AND
			(SELECT datetime(created_at, '+1 hour') FROM scan_tasks WHERE id = ?)
	`, taskID, taskID).Scan(&samePeriodFailed)

	fmt.Printf("  同时期任务状态: %d个任务，%d个失败\n", samePeriodTasks, samePeriodFailed)

	// 检查目标URL的历史成功率
	var targetTasks, targetFailed int
	db.QueryRow(`
		SELECT COUNT(*) FROM scan_tasks 
		WHERE targets = (SELECT targets FROM scan_tasks WHERE id = ?)
	`, taskID).Scan(&targetTasks)

	db.QueryRow(`
		SELECT COUNT(*) FROM scan_tasks 
		WHERE targets = (SELECT targets FROM scan_tasks WHERE id = ?) AND status = 'failed'
	`, taskID).Scan(&targetFailed)

	if targetTasks > 1 {
		successRate := float64(targetTasks-targetFailed) / float64(targetTasks) * 100
		fmt.Printf("  目标历史成功率: %.1f%% (%d成功/%d总计)\n",
			successRate, targetTasks-targetFailed, targetTasks)
	}
}

func provideSuggestions(stages []string, progress int, status string) {
	fmt.Printf("========================================\n")

	if progress == 0 {
		fmt.Printf("1. 任务启动问题:\n")
		fmt.Printf("   - 检查目标URL是否可访问\n")
		fmt.Printf("   - 验证网络连接和DNS解析\n")
		fmt.Printf("   - 检查扫描引擎是否正常注册\n\n")
	}

	if contains(fmt.Sprintf("%v", stages), "监控") {
		fmt.Printf("2. 监控系统问题:\n")
		fmt.Printf("   - 检查任务监控服务配置\n")
		fmt.Printf("   - 调整监控超时时间\n")
		fmt.Printf("   - 查看监控服务日志\n\n")
	}

	fmt.Printf("3. 通用排查步骤:\n")
	fmt.Printf("   - 重启扫描服务\n")
	fmt.Printf("   - 使用简单目标测试\n")
	fmt.Printf("   - 检查系统资源使用情况\n")
	fmt.Printf("   - 查看应用程序完整日志\n")
}

func contains(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
