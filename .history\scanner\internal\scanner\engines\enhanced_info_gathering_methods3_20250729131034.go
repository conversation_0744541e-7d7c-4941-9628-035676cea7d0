package engines

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"regexp"
	"strings"

	"scanner/internal/scanner/types"
)

// detectProtectionMeasures 防护措施探测 - WAF识别、主机防护策略、网络防护策略
func (e *InformationGatheringEngine) detectProtectionMeasures(ctx context.Context, target string) (*types.ProtectionInformation, error) {
	protectionInfo := &types.ProtectionInformation{
		WAF:               &types.WAFInformation{},
		HostProtection:    &types.HostProtectionInfo{},
		NetworkProtection: &types.NetworkProtectionInfo{},
		SecurityHeaders:   &types.SecurityHeadersInfo{},
	}

	// 解析目标URL
	parsedURL, err := url.Parse(target)
	if err != nil {
		return nil, err
	}

	// WAF检测
	e.detectWAF(ctx, parsedURL, protectionInfo.WAF)

	// 安全头检测
	e.detectSecurityHeaders(ctx, target, protectionInfo.SecurityHeaders)

	// 主机防护检测
	e.detectHostProtection(ctx, parsedURL, protectionInfo.HostProtection)

	// 网络防护检测
	e.detectNetworkProtection(ctx, parsedURL, protectionInfo.NetworkProtection)

	// 计算防护等级
	protectionInfo.ProtectionLevel, protectionInfo.ProtectionScore = e.calculateProtectionLevel(protectionInfo)

	return protectionInfo, nil
}

// detectWAF WAF检测
func (e *InformationGatheringEngine) detectWAF(ctx context.Context, targetURL *url.URL, wafInfo *types.WAFInformation) {
	// 发送正常请求
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL.String(), nil)
	if err != nil {
		return
	}
	req.Header.Set("User-Agent", e.userAgent)

	resp, err := e.client.Do(req)
	if err != nil {
		return
	}
	defer resp.Body.Close()

	// 检查常见WAF头
	wafHeaders := map[string]string{
		"Server":              "cloudflare",
		"CF-RAY":              "Cloudflare",
		"X-Sucuri-ID":         "Sucuri",
		"X-Akamai-Request-ID": "Akamai",
		"X-Azure-Ref":         "Azure WAF",
		"X-Varnish":           "Varnish",
		"X-Cache":             "Cache/Proxy",
		"X-CDN":               "CDN",
		"X-Forwarded-For":     "Load Balancer",
	}

	for header, wafType := range wafHeaders {
		if value := resp.Header.Get(header); value != "" {
			wafInfo.Detected = true
			wafInfo.Type = wafType
			wafInfo.Evidence = append(wafInfo.Evidence, fmt.Sprintf("%s: %s", header, value))
			wafInfo.Confidence += 0.3
		}
	}

	// 发送恶意请求测试WAF
	e.testWAFWithMaliciousRequests(ctx, targetURL, wafInfo)

	// 设置WAF厂商信息
	if wafInfo.Detected {
		e.identifyWAFVendor(wafInfo)
	}

	// 限制置信度在0-1之间
	if wafInfo.Confidence > 1.0 {
		wafInfo.Confidence = 1.0
	}
}

// testWAFWithMaliciousRequests 使用恶意请求测试WAF
func (e *InformationGatheringEngine) testWAFWithMaliciousRequests(ctx context.Context, targetURL *url.URL, wafInfo *types.WAFInformation) {
	// 常见的WAF测试载荷
	testPayloads := []string{
		"<script>alert('xss')</script>",
		"' OR '1'='1",
		"../../../etc/passwd",
		"<?php phpinfo(); ?>",
		"<img src=x onerror=alert(1)>",
	}

	for _, payload := range testPayloads {
		testURL := fmt.Sprintf("%s?test=%s", targetURL.String(), url.QueryEscape(payload))

		req, err := http.NewRequestWithContext(ctx, "GET", testURL, nil)
		if err != nil {
			continue
		}
		req.Header.Set("User-Agent", e.userAgent)

		resp, err := e.client.Do(req)
		if err != nil {
			continue
		}
		resp.Body.Close()

		// 检查WAF阻断响应
		if e.isWAFBlockResponse(resp) {
			wafInfo.Detected = true
			wafInfo.Evidence = append(wafInfo.Evidence, fmt.Sprintf("恶意请求被阻断: %d", resp.StatusCode))
			wafInfo.Confidence += 0.2
		}
	}
}

// isWAFBlockResponse 判断是否为WAF阻断响应
func (e *InformationGatheringEngine) isWAFBlockResponse(resp *http.Response) bool {
	// 常见的WAF阻断状态码
	blockStatusCodes := []int{403, 406, 429, 503}
	for _, code := range blockStatusCodes {
		if resp.StatusCode == code {
			return true
		}
	}

	// 检查响应头中的WAF特征
	wafBlockHeaders := []string{
		"X-Blocked-By",
		"X-Firewall-Blocked",
		"X-Security-Block",
	}
	for _, header := range wafBlockHeaders {
		if resp.Header.Get(header) != "" {
			return true
		}
	}

	return false
}

// identifyWAFVendor 识别WAF厂商
func (e *InformationGatheringEngine) identifyWAFVendor(wafInfo *types.WAFInformation) {
	switch wafInfo.Type {
	case "Cloudflare":
		wafInfo.Vendor = "Cloudflare"
	case "Sucuri":
		wafInfo.Vendor = "Sucuri"
	case "Akamai":
		wafInfo.Vendor = "Akamai"
	case "Azure WAF":
		wafInfo.Vendor = "Microsoft"
	default:
		wafInfo.Vendor = "Unknown"
	}
}

// detectSecurityHeaders 检测安全头
func (e *InformationGatheringEngine) detectSecurityHeaders(ctx context.Context, target string, securityHeaders *types.SecurityHeadersInfo) {
	req, err := http.NewRequestWithContext(ctx, "GET", target, nil)
	if err != nil {
		return
	}
	req.Header.Set("User-Agent", e.userAgent)

	resp, err := e.client.Do(req)
	if err != nil {
		return
	}
	defer resp.Body.Close()

	// HSTS检测
	if hsts := resp.Header.Get("Strict-Transport-Security"); hsts != "" {
		securityHeaders.HSTS = &types.HSTSInfo{
			Enabled:     true,
			HeaderValue: hsts,
			Subdomains:  strings.Contains(hsts, "includeSubDomains"),
			Preload:     strings.Contains(hsts, "preload"),
		}
		// 提取max-age
		if matches := regexp.MustCompile(`max-age=(\d+)`).FindStringSubmatch(hsts); len(matches) > 1 {
			// 这里可以解析max-age值
		}
	}

	// CSP检测
	if csp := resp.Header.Get("Content-Security-Policy"); csp != "" {
		securityHeaders.CSP = &types.CSPInfo{
			Enabled:     true,
			HeaderValue: csp,
			Directives:  strings.Split(csp, ";"),
		}
	}

	// X-Frame-Options检测
	if xfo := resp.Header.Get("X-Frame-Options"); xfo != "" {
		securityHeaders.XFrameOptions = &types.XFrameOptionsInfo{
			Enabled:     true,
			Value:       xfo,
			HeaderValue: xfo,
		}
	}

	// X-Content-Type-Options检测
	if xcto := resp.Header.Get("X-Content-Type-Options"); xcto != "" {
		securityHeaders.XContentType = &types.XContentTypeInfo{
			Enabled:     true,
			Value:       xcto,
			HeaderValue: xcto,
		}
	}

	// X-XSS-Protection检测
	if xxp := resp.Header.Get("X-XSS-Protection"); xxp != "" {
		securityHeaders.XSSProtection = &types.XSSProtectionInfo{
			Enabled:     true,
			HeaderValue: xxp,
			Block:       strings.Contains(xxp, "mode=block"),
		}
	}

	// Referrer-Policy检测
	if rp := resp.Header.Get("Referrer-Policy"); rp != "" {
		securityHeaders.ReferrerPolicy = &types.ReferrerPolicyInfo{
			Enabled:     true,
			Policy:      rp,
			HeaderValue: rp,
		}
	}
}

// detectHostProtection 检测主机防护
func (e *InformationGatheringEngine) detectHostProtection(ctx context.Context, targetURL *url.URL, hostProtection *types.HostProtectionInfo) {
	// 这里是简化实现，实际需要更复杂的检测逻辑

	// 防火墙检测（通过端口扫描结果推断）
	hostProtection.Firewall = &types.FirewallInfo{
		Detected: false, // 需要通过端口扫描结果判断
		Type:     "Unknown",
	}

	// 杀毒软件检测（通过HTTP响应特征推断）
	hostProtection.AntiVirus = &types.AntiVirusInfo{
		Detected: false,
		Product:  "Unknown",
	}

	// HIDS检测
	hostProtection.HIDS = &types.HIDSInfo{
		Detected: false,
		Product:  "Unknown",
	}
}

// detectNetworkProtection 检测网络防护
func (e *InformationGatheringEngine) detectNetworkProtection(ctx context.Context, targetURL *url.URL, networkProtection *types.NetworkProtectionInfo) {
	// IDS/IPS检测（通过响应特征推断）
	networkProtection.IDS = &types.IDSInfo{
		Detected: false,
		Product:  "Unknown",
	}

	networkProtection.IPS = &types.IPSInfo{
		Detected: false,
		Product:  "Unknown",
	}

	// DDoS防护检测
	networkProtection.DDoSProtection = &types.DDoSProtectionInfo{
		Detected: false,
		Product:  "Unknown",
	}

	// 负载均衡检测
	networkProtection.LoadBalancer = &types.LoadBalancerInfo{
		Detected: false,
		Product:  "Unknown",
	}

	// CDN检测
	networkProtection.CDN = &types.CDNInfo{
		Detected: false,
		Provider: "Unknown",
	}
}

// calculateProtectionLevel 计算防护等级
func (e *InformationGatheringEngine) calculateProtectionLevel(protectionInfo *types.ProtectionInformation) (string, int) {
	score := 0

	// WAF检测加分
	if protectionInfo.WAF.Detected {
		score += 30
	}

	// 安全头检测加分
	if protectionInfo.SecurityHeaders.HSTS != nil && protectionInfo.SecurityHeaders.HSTS.Enabled {
		score += 10
	}
	if protectionInfo.SecurityHeaders.CSP != nil && protectionInfo.SecurityHeaders.CSP.Enabled {
		score += 15
	}
	if protectionInfo.SecurityHeaders.XFrameOptions != nil && protectionInfo.SecurityHeaders.XFrameOptions.Enabled {
		score += 5
	}
	if protectionInfo.SecurityHeaders.XContentType != nil && protectionInfo.SecurityHeaders.XContentType.Enabled {
		score += 5
	}
	if protectionInfo.SecurityHeaders.XSSProtection != nil && protectionInfo.SecurityHeaders.XSSProtection.Enabled {
		score += 5
	}

	// 主机防护加分
	if protectionInfo.HostProtection.Firewall != nil && protectionInfo.HostProtection.Firewall.Detected {
		score += 15
	}
	if protectionInfo.HostProtection.AntiVirus != nil && protectionInfo.HostProtection.AntiVirus.Detected {
		score += 10
	}

	// 网络防护加分
	if protectionInfo.NetworkProtection.CDN != nil && protectionInfo.NetworkProtection.CDN.Detected {
		score += 10
	}
	if protectionInfo.NetworkProtection.LoadBalancer != nil && protectionInfo.NetworkProtection.LoadBalancer.Detected {
		score += 5
	}

	// 确定防护等级
	var level string
	if score >= 70 {
		level = "high"
	} else if score >= 40 {
		level = "medium"
	} else if score >= 20 {
		level = "low"
	} else {
		level = "none"
	}

	return level, score
}

// performComprehensiveFingerprintAnalysis 执行全面的指纹识别分析
func (e *InformationGatheringEngine) performComprehensiveFingerprintAnalysis(ctx context.Context, target string) (*types.FingerprintInformation, error) {
	fingerprintInfo := &types.FingerprintInformation{
		HTTPHeaders:           &types.HTTPHeaderAnalysis{},
		PageContent:           &types.PageContentAnalysis{},
		ErrorPages:            &types.ErrorPageAnalysis{},
		Cookies:               &types.CookieAnalysis{},
		TechStackFingerprints: make([]*types.TechStackFingerprint, 0),
	}

	// 发送HTTP请求获取响应
	req, err := http.NewRequestWithContext(ctx, "GET", target, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("User-Agent", e.userAgent)

	resp, err := e.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// HTTP头分析
	e.analyzeHTTPHeadersForFingerprint(resp, fingerprintInfo.HTTPHeaders)

	// 读取页面内容
	body := make([]byte, 20480) // 读取前20KB
	n, _ := resp.Body.Read(body)
	bodyStr := string(body[:n])

	// 页面内容分析
	e.analyzePageContentForFingerprint(bodyStr, fingerprintInfo.PageContent)

	// Cookie分析
	e.analyzeCookiesForFingerprint(resp.Cookies(), fingerprintInfo.Cookies)

	// 错误页面分析
	e.analyzeErrorPagesForFingerprint(ctx, target, fingerprintInfo.ErrorPages)

	// 技术栈指纹识别
	e.identifyTechStackFingerprints(resp, bodyStr, fingerprintInfo)

	// 计算综合置信度
	fingerprintInfo.OverallConfidence = e.calculateOverallConfidence(fingerprintInfo)

	return fingerprintInfo, nil
}

// analyzeHTTPHeadersForFingerprint HTTP头分析用于指纹识别
func (e *InformationGatheringEngine) analyzeHTTPHeadersForFingerprint(resp *http.Response, headerAnalysis *types.HTTPHeaderAnalysis) {
	headerAnalysis.TotalHeaders = len(resp.Header)

	// Server头分析
	if server := resp.Header.Get("Server"); server != "" {
		headerAnalysis.ServerHeader = &types.ServerHeaderInfo{
			Value:      server,
			WebServer:  e.extractWebServerFromHeader(server),
			Version:    e.extractVersionFromString(server, ""),
			Confidence: 0.8,
		}
	}

	// X-Powered-By头分析
	if poweredBy := resp.Header.Get("X-Powered-By"); poweredBy != "" {
		headerAnalysis.PoweredByHeader = &types.PoweredByHeaderInfo{
			Value:      poweredBy,
			Technology: e.extractTechnologyFromPoweredBy(poweredBy),
			Version:    e.extractVersionFromString(poweredBy, ""),
			Confidence: 0.9,
		}
	}

	// 自定义头分析
	customHeaders := make([]*types.CustomHeaderInfo, 0)
	interestingHeaders := []string{
		"X-Generator", "X-Drupal-Cache", "X-Varnish", "X-Cache",
		"X-Served-By", "X-Frame-Options", "X-Content-Type-Options",
	}

	for _, header := range interestingHeaders {
		if value := resp.Header.Get(header); value != "" {
			customHeaders = append(customHeaders, &types.CustomHeaderInfo{
				Name:        header,
				Value:       value,
				Technology:  e.inferTechnologyFromHeader(header, value),
				Description: e.getHeaderDescription(header),
				Confidence:  0.7,
			})
		}
	}
	headerAnalysis.CustomHeaders = customHeaders

	// 计算HTTP头分析的置信度
	headerAnalysis.Confidence = e.calculateHeaderAnalysisConfidence(headerAnalysis)
}

// analyzePageContentForFingerprint 页面内容分析用于指纹识别
func (e *InformationGatheringEngine) analyzePageContentForFingerprint(content string, pageAnalysis *types.PageContentAnalysis) {
	// HTML标签分析
	pageAnalysis.HTMLTags = e.extractHTMLTags(content)

	// JavaScript库分析
	pageAnalysis.JavaScriptLibs = e.extractJavaScriptLibs(content)

	// CSS框架分析
	pageAnalysis.CSSFrameworks = e.extractCSSFrameworks(content)

	// Meta标签分析
	pageAnalysis.MetaTags = e.extractMetaTags(content)

	// 注释分析
	pageAnalysis.Comments = e.extractComments(content)

	// 计算总元素数量
	pageAnalysis.TotalElements = len(pageAnalysis.HTMLTags) + len(pageAnalysis.JavaScriptLibs) +
		len(pageAnalysis.CSSFrameworks) + len(pageAnalysis.MetaTags) + len(pageAnalysis.Comments)

	// 计算页面内容分析的置信度
	pageAnalysis.Confidence = e.calculatePageAnalysisConfidence(pageAnalysis)
}

// analyzeCookiesForFingerprint Cookie分析用于指纹识别
func (e *InformationGatheringEngine) analyzeCookiesForFingerprint(cookies []*http.Cookie, cookieAnalysis *types.CookieAnalysis) {
	cookieAnalysis.TotalCookies = len(cookies)

	sessionCookies := make([]*types.SessionCookieInfo, 0)
	techCookies := make([]*types.TechCookieInfo, 0)

	for _, cookie := range cookies {
		// 会话Cookie识别
		if e.isSessionCookie(cookie.Name) {
			sessionCookies = append(sessionCookies, &types.SessionCookieInfo{
				Name:       cookie.Name,
				Value:      cookie.Value,
				Technology: e.inferTechnologyFromCookie(cookie.Name),
				Framework:  e.inferFrameworkFromCookie(cookie.Name),
				Secure:     cookie.Secure,
				HttpOnly:   cookie.HttpOnly,
				SameSite:   cookie.SameSite.String(),
				Confidence: 0.8,
			})
		}

		// 技术Cookie识别
		if tech := e.inferTechnologyFromCookie(cookie.Name); tech != "" {
			techCookies = append(techCookies, &types.TechCookieInfo{
				Name:       cookie.Name,
				Technology: tech,
				Framework:  e.inferFrameworkFromCookie(cookie.Name),
				Confidence: 0.7,
			})
		}
	}

	cookieAnalysis.SessionCookies = sessionCookies
	cookieAnalysis.TechCookies = techCookies

	// 计算Cookie分析的置信度
	cookieAnalysis.Confidence = e.calculateCookieAnalysisConfidence(cookieAnalysis)
}

// analyzeErrorPagesForFingerprint 错误页面分析用于指纹识别
func (e *InformationGatheringEngine) analyzeErrorPagesForFingerprint(ctx context.Context, target string, errorAnalysis *types.ErrorPageAnalysis) {
	// 测试常见的错误页面
	errorURLs := []string{
		"/nonexistent-page-404",
		"/admin/nonexistent",
		"/test/error",
	}

	errorPages := make([]*types.ErrorPageInfo, 0)

	for _, errorURL := range errorURLs {
		baseURL, _ := url.Parse(target)
		testURL := fmt.Sprintf("%s://%s%s", baseURL.Scheme, baseURL.Host, errorURL)

		req, err := http.NewRequestWithContext(ctx, "GET", testURL, nil)
		if err != nil {
			continue
		}
		req.Header.Set("User-Agent", e.userAgent)

		resp, err := e.client.Do(req)
		if err != nil {
			continue
		}
		defer resp.Body.Close()

		if resp.StatusCode >= 400 {
			body := make([]byte, 2048)
			n, _ := resp.Body.Read(body)
			content := string(body[:n])

			errorPages = append(errorPages, &types.ErrorPageInfo{
				StatusCode: resp.StatusCode,
				URL:        testURL,
				Title:      e.extractTitle(content),
				Content:    content,
				Technology: e.inferTechnologyFromErrorPage(content),
				WebServer:  e.extractWebServerFromHeader(resp.Header.Get("Server")),
				Confidence: 0.6,
			})
		}
	}

	errorAnalysis.ErrorPages = errorPages
	errorAnalysis.TotalErrors = len(errorPages)
	errorAnalysis.Confidence = e.calculateErrorAnalysisConfidence(errorAnalysis)
}

// identifyTechStackFingerprints 识别技术栈指纹
func (e *InformationGatheringEngine) identifyTechStackFingerprints(resp *http.Response, content string, fingerprintInfo *types.FingerprintInformation) {
	fingerprints := make([]*types.TechStackFingerprint, 0)

	// 从HTTP头识别技术栈
	if server := resp.Header.Get("Server"); server != "" {
		if webServer := e.extractWebServerFromHeader(server); webServer != "" {
			fingerprints = append(fingerprints, &types.TechStackFingerprint{
				Category:   "Web Server",
				Technology: webServer,
				Version:    e.extractVersionFromString(server, webServer),
				Confidence: 0.9,
				Evidence:   []string{fmt.Sprintf("Server: %s", server)},
				Source:     "HTTP Header",
			})
		}
	}

	// 从页面内容识别技术栈
	if strings.Contains(strings.ToLower(content), "wordpress") {
		fingerprints = append(fingerprints, &types.TechStackFingerprint{
			Category:   "CMS",
			Technology: "WordPress",
			Version:    e.extractWordPressVersion(content),
			Confidence: 0.8,
			Evidence:   []string{"WordPress特征字符串"},
			Source:     "Page Content",
		})
	}

	fingerprintInfo.TechStackFingerprints = fingerprints
}

// calculateOverallConfidence 计算综合置信度
func (e *InformationGatheringEngine) calculateOverallConfidence(fingerprintInfo *types.FingerprintInformation) float64 {
	totalConfidence := 0.0
	count := 0

	if fingerprintInfo.HTTPHeaders != nil {
		totalConfidence += fingerprintInfo.HTTPHeaders.Confidence
		count++
	}
	if fingerprintInfo.PageContent != nil {
		totalConfidence += fingerprintInfo.PageContent.Confidence
		count++
	}
	if fingerprintInfo.Cookies != nil {
		totalConfidence += fingerprintInfo.Cookies.Confidence
		count++
	}
	if fingerprintInfo.ErrorPages != nil {
		totalConfidence += fingerprintInfo.ErrorPages.Confidence
		count++
	}

	if count == 0 {
		return 0.0
	}

	return totalConfidence / float64(count)
}
