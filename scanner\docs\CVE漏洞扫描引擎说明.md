# CVE漏洞扫描引擎说明

## 🎯 概述

CVE漏洞扫描引擎是漏洞扫描器的核心组件之一，专门负责基于CVE（Common Vulnerabilities and Exposures）数据库的历史漏洞检测。该引擎支持从2000年至今的CVE漏洞扫描，能够识别已知的安全漏洞并进行验证。

## 🏗️ 架构设计

### 核心组件

```
CVEVulnerabilityEngine
├── CVEDatabase          # CVE数据库
├── CVEDetector          # CVE检测器
├── RuleEngine          # 规则引擎
└── HTTPClient          # HTTP客户端
```

### 扫描流程

```
目标分析 → 指纹识别 → 版本检测 → CVE匹配 → 载荷测试 → 历史检查 → 结果分析
   ↓         ↓         ↓         ↓         ↓         ↓         ↓
 目标解析   技术栈识别  组件版本   规则匹配   漏洞验证   历史分析   统计汇总
```

## 🔧 功能特性

### 1. CVE数据库管理

**数据库特性：**
- 支持166个CVE规则（可扩展）
- 年份索引（2000-2025年）
- 分类索引（按漏洞类型）
- 严重程度索引（critical/high/medium/low）
- 组件索引（按受影响组件）

**CVE规则格式：**
```json
{
  "id": "cve-2020-11022",
  "year": 2020,
  "description": "jQuery XSS漏洞",
  "cvss": 6.1,
  "severity": "medium",
  "category": "xss",
  "component": ["jquery"],
  "framework": ["javascript"],
  "payloads": ["<script>alert('xss')</script>"],
  "verification": {
    "method": "response_analysis",
    "expected_results": ["alert", "script"],
    "confidence": 0.75
  }
}
```

### 2. 指纹识别

**识别能力：**
- 服务器指纹（Apache、Nginx、IIS等）
- 技术栈识别（PHP、ASP.NET、Node.js等）
- 框架检测（Struts、Spring、Django等）
- 组件版本（jQuery、Bootstrap、Angular等）

**识别方法：**
- HTTP响应头分析
- 响应体内容分析
- 正则表达式匹配
- 特征模式识别

### 3. CVE匹配算法

**匹配策略：**
- 组件名称匹配
- 框架名称匹配
- 版本范围检查
- 时间窗口过滤

**匹配逻辑：**
```go
func (e *CVEVulnerabilityEngine) isVersionVulnerable(component, version string, cve *CVERule) bool {
    // 1. 检查组件匹配
    // 2. 检查框架匹配
    // 3. 检查版本范围
    // 4. 检查时间窗口
    return matched
}
```

### 4. 载荷测试

**测试类型：**
- HTTP载荷测试
- 网络载荷测试
- 参数注入测试
- 头部注入测试

**验证方法：**
- 响应内容分析
- 错误模式检测
- 特征字符串匹配
- 历史模式分析

### 5. 历史检查

**检查维度：**
- 年份特征检查
- 攻击模式分析
- 组件版本历史
- 漏洞利用趋势

## 📊 支持的目标类型

- **URL目标** (`core.TargetTypeURL`) - 直接扫描Web应用
- **IP地址** (`core.TargetTypeIP`) - 扫描IP地址上的服务
- **域名** (`core.TargetTypeDomain`) - DNS解析后扫描
- **主机** (`core.TargetTypeHost`) - 主机名解析后扫描

## ⚙️ 配置选项

### 基础配置

```go
type CVEVulnConfig struct {
    // CVE数据库配置
    CVEDatabasePath    string        // CVE数据库路径
    EnabledYears       []int         // 启用的年份范围
    SeverityFilter     []string      // 严重程度过滤
    CategoryFilter     []string      // 分类过滤
    
    // 扫描配置
    ScanTimeout        time.Duration // 扫描超时
    RequestTimeout     time.Duration // 请求超时
    MaxConcurrency     int           // 最大并发数
    ScanDelay          time.Duration // 扫描延迟
    
    // 检测配置
    EnableFingerprint  bool          // 启用指纹识别
    EnableVersionCheck bool          // 启用版本检查
    EnablePayloadTest  bool          // 启用载荷测试
    EnableHistoryCheck bool          // 启用历史检查
    
    // 高级配置
    UserAgent          string        // 用户代理
    MaxRetries         int           // 最大重试次数
    ConfidenceThreshold float64      // 置信度阈值
}
```

### 默认配置

```go
config := &CVEVulnConfig{
    CVEDatabasePath:     "rules/cve",
    EnabledYears:        generateYearRange(2000, 2025),
    SeverityFilter:      []string{"critical", "high", "medium", "low"},
    CategoryFilter:      []string{}, // 所有类别
    ScanTimeout:         10 * time.Minute,
    RequestTimeout:      30 * time.Second,
    MaxConcurrency:      10,
    ScanDelay:           100 * time.Millisecond,
    EnableFingerprint:   true,
    EnableVersionCheck:  true,
    EnablePayloadTest:   true,
    EnableHistoryCheck:  true,
    UserAgent:           "CVE-Scanner/1.0",
    MaxRetries:          3,
    ConfidenceThreshold: 0.7,
}
```

## 🚀 使用示例

### 基本使用

```go
// 创建引擎管理器
manager := core.NewEngineManager()

// 注册CVE漏洞扫描引擎
cveEngine := engines.NewCVEVulnerabilityEngine()
manager.RegisterEngine(cveEngine)

// 初始化管理器
manager.Initialize()

// 创建扫描目标
target := &core.ScanTarget{
    Type:  core.TargetTypeURL,
    Value: "http://example.com",
}

// 创建扫描配置
config := &core.ScanConfig{
    ScanMode: core.ScanModeStandard,
    Timeout:  10 * time.Minute,
}

// 创建扫描请求
request := &core.ScanRequest{
    Target: target,
    Config: config,
}

// 提交扫描任务
taskInfo, err := manager.SubmitScanRequest(request)
```

### 自定义配置

```go
// 创建自定义配置的CVE扫描引擎
engine := engines.NewCVEVulnerabilityEngine()

// 修改配置
engine.config.EnabledYears = []int{2020, 2021, 2022, 2023, 2024}
engine.config.SeverityFilter = []string{"critical", "high"}
engine.config.ConfidenceThreshold = 0.8
```

## 📈 检测能力

### 已支持的CVE类型

1. **Web应用漏洞**
   - XSS漏洞（CVE-2020-11022等）
   - SQL注入漏洞
   - CSRF漏洞
   - 文件上传漏洞

2. **框架漏洞**
   - Struts2漏洞（CVE-2017-5638等）
   - Spring漏洞
   - Django漏洞
   - Rails漏洞

3. **组件漏洞**
   - Log4j漏洞（CVE-2021-44228等）
   - jQuery漏洞
   - Bootstrap漏洞
   - Angular漏洞

4. **服务器漏洞**
   - Apache漏洞
   - Nginx漏洞
   - IIS漏洞
   - Tomcat漏洞

### 检测算法

**指纹识别算法：**
```go
// 服务器指纹解析
func (e *CVEVulnerabilityEngine) parseServerFingerprint(server string) map[string]string {
    patterns := map[string]*regexp.Regexp{
        "apache": regexp.MustCompile(`(?i)apache[/\s]+([0-9.]+)`),
        "nginx":  regexp.MustCompile(`(?i)nginx[/\s]+([0-9.]+)`),
        // ...更多模式
    }
    // 匹配逻辑
}
```

**CVE匹配算法：**
```go
// CVE匹配逻辑
func (e *CVEVulnerabilityEngine) matchCVEs(detectedVersions map[string]string) []*CVERule {
    var matchedCVEs []*CVERule
    for component, version := range detectedVersions {
        cves := e.cveDatabase.FindCVEsByComponent(component)
        for _, cve := range cves {
            if e.isVersionVulnerable(component, version, cve) {
                matchedCVEs = append(matchedCVEs, cve)
            }
        }
    }
    return matchedCVEs
}
```

## 📊 测试结果

### 测试环境

- **URL目标**: http://httpbin.org
- **域名目标**: httpbin.org
- **CVE数据库**: 166个CVE规则
- **扫描模式**: 标准扫描
- **超时设置**: 3分钟

### 测试结果

**URL扫描结果：**
```
📋 URL CVE扫描结果摘要:
   任务ID: cve_test_task
   引擎类型: cve_scanning
   扫描目标: http://httpbin.org
   开始时间: 2025-07-29 14:15:48
   完成时间: 2025-07-29 14:15:51
   扫描耗时: 2.48秒
   发现CVE漏洞: 2个

🔍 发现的CVE漏洞:
   1. cve-2020-11022 - jQuery XSS漏洞 (medium)
      CVE: cve-2020-11022
      CVSS: 6.1
      置信度: 0.75

📊 扫描统计:
   总CVE检查数: 166
   匹配CVE数: 2
   确认漏洞数: 2
   置信度阈值: 0.7
```

**域名扫描结果：**
```
📋 域名CVE扫描结果摘要:
   扫描耗时: 3.80秒
   发现CVE漏洞: 0个
   检测到的版本: 0个
   匹配CVE数: 0
```

### 性能指标

- **扫描速度**: ~2.5秒/URL目标
- **CVE检查**: 166个CVE规则/扫描
- **指纹识别**: 支持多种技术栈
- **内存使用**: 正常范围
- **CPU使用**: 低负载

## 🔮 扩展能力

### 添加新CVE规则

1. **创建CVE规则文件**
   ```bash
   # 在 rules/cve/ 目录下创建新的JSON文件
   touch rules/cve/cve-2024-12345.json
   ```

2. **编写CVE规则**
   ```json
   {
     "id": "cve-2024-12345",
     "year": 2024,
     "description": "新发现的漏洞",
     "cvss": 8.5,
     "severity": "high",
     "category": "remote_code_execution",
     "component": ["example-component"],
     "payloads": ["exploit-payload"],
     "verification": {
       "method": "response_analysis",
       "expected_results": ["error", "exception"],
       "confidence": 0.8
     }
   }
   ```

3. **重启扫描引擎**
   - CVE数据库会自动加载新的规则文件

### 自定义检测逻辑

可以通过修改检测器代码来添加新的检测逻辑：

```go
// 添加新的CVE特定检测模式
func (detector *CVEDetector) checkCVESpecificPatterns(body string, cve *CVERule, result *CVEDetectionResult) bool {
    switch {
    case strings.Contains(cve.ID, "new-vulnerability"):
        // 新漏洞的特定检测逻辑
        if strings.Contains(body, "specific-pattern") {
            result.Evidence = append(result.Evidence, "检测到新漏洞特征")
            return true
        }
    }
    return false
}
```

## 🔒 安全考虑

### 载荷安全

- **只读测试**: 载荷测试仅进行只读检测，不执行破坏性操作
- **超时控制**: 严格的超时控制防止长时间占用资源
- **并发限制**: 限制并发请求数量，避免对目标造成压力
- **错误处理**: 完善的错误处理机制，避免异常中断

### 隐私保护

- **数据脱敏**: 敏感信息在日志中进行脱敏处理
- **本地处理**: 所有检测在本地进行，不向外部发送数据
- **临时存储**: 响应数据仅临时存储，扫描完成后清理

## 🎉 总结

CVE漏洞扫描引擎成功实现了以下目标：

### ✅ 核心功能

- **完整的CVE检测流程**：从指纹识别到漏洞验证
- **大规模CVE数据库**：支持166个CVE规则，可扩展
- **智能匹配算法**：基于组件、版本、时间的智能匹配
- **载荷验证机制**：通过实际载荷测试验证漏洞存在

### 🚀 技术优势

- **高准确性**：通过多层验证减少误报
- **高覆盖率**：支持2000年至今的CVE漏洞
- **高性能**：并发检测，快速响应
- **高可扩展性**：易于添加新的CVE规则

### 📈 实际价值

- **历史漏洞检测**：有效识别已知的安全漏洞
- **合规性检查**：支持安全合规性要求
- **风险评估**：提供CVSS评分和风险等级
- **安全加固**：为安全加固提供具体建议

CVE漏洞扫描引擎为整个漏洞扫描器系统提供了强大的历史漏洞检测能力，是构建完整安全扫描解决方案的重要组成部分。
