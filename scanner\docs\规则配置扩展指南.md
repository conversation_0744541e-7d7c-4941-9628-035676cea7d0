# 漏洞扫描引擎规则配置扩展指南

## 🎯 概述

漏洞扫描引擎采用了完全插件化的架构设计，支持通过配置规则文件来扩展扫描功能，无需修改核心代码即可添加新的漏洞检测能力。

## 🔌 插件化架构特性

### 1. 统一接口设计
- 所有扫描引擎实现相同的 `ScanEngine` 接口
- 支持动态注册和注销引擎
- 引擎间完全解耦，可独立开发和测试

### 2. 规则驱动检测
- 基于JSON配置文件定义检测规则
- 支持复杂的条件匹配和动作执行
- 规则可以动态加载和更新

### 3. 灵活的扩展机制
- 支持自定义检测逻辑
- 可配置的扫描参数
- 模块化的组件设计

## 📁 规则文件结构

```
scanner/
└── rules/
    ├── rules/          # 单个规则文件
    │   ├── sql_injection_basic.json
    │   ├── xss_reflected.json
    │   └── directory_traversal.json
    └── groups/         # 规则组文件
        ├── web_vulnerabilities.json
        └── injection_attacks.json
```

## 🔧 规则配置格式

### 基础规则结构

```json
{
  "id": "规则唯一标识",
  "name": "规则显示名称",
  "description": "规则描述",
  "category": "规则分类",
  "severity": "严重程度",
  "conditions": [
    {
      "type": "条件类型",
      "field": "检查字段",
      "operator": "操作符",
      "value": "期望值",
      "regex": "正则表达式",
      "case_sensitive": false
    }
  ],
  "actions": [
    {
      "type": "动作类型",
      "target": "目标",
      "parameters": {
        "参数名": "参数值"
      }
    }
  ],
  "tags": ["标签1", "标签2"],
  "references": ["参考链接"],
  "author": "作者",
  "version": "版本",
  "enabled": true,
  "config": {
    "自定义配置项": "配置值"
  }
}
```

### 支持的条件类型

1. **response_analysis** - 响应分析
   - 检查HTTP响应的各个部分
   - 支持状态码、响应体、响应头检查

2. **request_analysis** - 请求分析
   - 检查HTTP请求的各个部分
   - 支持URL、方法、参数检查

3. **target_analysis** - 目标分析
   - 检查扫描目标的属性
   - 支持URL、类型、元数据检查

### 支持的操作符

- **equals** - 相等比较
- **contains** - 包含检查
- **regex** - 正则表达式匹配
- **greater_than** - 大于比较
- **less_than** - 小于比较

### 支持的动作类型

- **create_vulnerability** - 创建漏洞记录
- **set_variable** - 设置变量
- **log** - 记录日志

## 📝 规则示例

### 1. SQL注入检测规则

```json
{
  "id": "sql_injection_basic",
  "name": "基础SQL注入检测",
  "description": "检测基础的SQL注入漏洞，通过错误信息识别",
  "category": "sql_injection",
  "severity": "high",
  "conditions": [
    {
      "type": "response_analysis",
      "field": "response.body",
      "operator": "regex",
      "regex": "(?i)(mysql_fetch_array|ORA-\\d+|SQL syntax.*MySQL)",
      "case_sensitive": false
    }
  ],
  "actions": [
    {
      "type": "create_vulnerability",
      "target": "sql_injection",
      "parameters": {
        "name": "SQL注入漏洞",
        "description": "检测到SQL注入漏洞",
        "solution": "使用参数化查询"
      }
    }
  ],
  "enabled": true
}
```

### 2. XSS检测规则

```json
{
  "id": "xss_reflected",
  "name": "反射型XSS检测",
  "description": "检测反射型跨站脚本攻击漏洞",
  "category": "xss",
  "severity": "medium",
  "conditions": [
    {
      "type": "response_analysis",
      "field": "response.body",
      "operator": "contains",
      "value": "<script>alert('XSS')</script>",
      "case_sensitive": false
    },
    {
      "type": "response_analysis",
      "field": "response.headers.content-type",
      "operator": "contains",
      "value": "text/html",
      "case_sensitive": false
    }
  ],
  "actions": [
    {
      "type": "create_vulnerability",
      "target": "xss",
      "parameters": {
        "name": "反射型XSS漏洞",
        "description": "检测到反射型跨站脚本攻击漏洞"
      }
    }
  ],
  "enabled": true
}
```

## 📦 规则组配置

规则组用于将相关的规则组织在一起，便于批量管理和执行：

```json
{
  "id": "web_vulnerabilities",
  "name": "Web应用漏洞检测",
  "description": "包含所有Web应用相关的漏洞检测规则",
  "rules": [
    "sql_injection_basic",
    "xss_reflected",
    "directory_traversal"
  ],
  "enabled": true,
  "config": {
    "scan_mode": "comprehensive",
    "timeout": 300,
    "max_concurrent_rules": 5
  }
}
```

## 🚀 扩展功能的方法

### 1. 添加新的检测规则

1. **创建规则文件**
   ```bash
   # 在 rules/rules/ 目录下创建新的JSON文件
   touch rules/rules/my_custom_rule.json
   ```

2. **编写规则配置**
   ```json
   {
     "id": "my_custom_rule",
     "name": "我的自定义规则",
     "description": "检测特定的安全问题",
     "category": "custom",
     "severity": "medium",
     "conditions": [
       {
         "type": "response_analysis",
         "field": "response.body",
         "operator": "contains",
         "value": "敏感信息泄露标识"
       }
     ],
     "actions": [
       {
         "type": "create_vulnerability",
         "target": "information_disclosure"
       }
     ],
     "enabled": true
   }
   ```

3. **重启扫描引擎**
   - 规则引擎会自动加载新的规则文件

### 2. 创建规则组

1. **创建规则组文件**
   ```bash
   touch rules/groups/my_rule_group.json
   ```

2. **配置规则组**
   ```json
   {
     "id": "my_rule_group",
     "name": "我的规则组",
     "description": "自定义规则组",
     "rules": [
       "my_custom_rule",
       "sql_injection_basic"
     ],
     "enabled": true
   }
   ```

### 3. 扩展条件类型

可以通过修改规则引擎代码来添加新的条件类型：

```go
// 在 rule_engine.go 中添加新的条件处理
func (re *RuleEngine) matchCondition(condition *RuleCondition, fieldValue interface{}) bool {
    switch condition.Operator {
    case "my_custom_operator":
        return re.myCustomMatch(fieldValue, condition.Value)
    // ... 其他操作符
    }
}
```

### 4. 扩展动作类型

添加新的动作类型来执行自定义操作：

```go
// 在 rule_engine.go 中添加新的动作处理
func (re *RuleEngine) executeAction(action *RuleAction, ctx *RuleExecutionContext, result *RuleResult) {
    switch action.Type {
    case "my_custom_action":
        re.myCustomAction(action, ctx, result)
    // ... 其他动作类型
    }
}
```

## 🧪 测试规则配置

### 运行规则引擎测试

```bash
go run cmd/test_rule_engine.go
```

### 测试输出示例

```
🔧 规则引擎测试
==================================================
📋 初始化规则引擎...
✅ 规则引擎初始化成功

📊 已加载的规则:
1. 基础SQL注入检测 (sql_injection_basic) - 检测基础的SQL注入漏洞
   分类: sql_injection, 严重程度: high, 启用: true
   条件数: 2, 动作数: 2
   标签: [sql-injection database injection]

2. 反射型XSS检测 (xss_reflected) - 检测反射型跨站脚本攻击漏洞
   分类: xss, 严重程度: medium, 启用: true
   条件数: 2, 动作数: 1
   标签: [xss reflected client-side]

🧪 测试规则执行...
🔍 测试SQL注入规则...
规则 sql_injection_basic: 匹配=true, 置信度=1.00
  证据: [字段 response.body 匹配条件 字段 request.method 匹配条件]
```

## 🔮 高级扩展功能

### 1. 动态载荷生成

可以在规则配置中定义动态载荷：

```json
{
  "config": {
    "payloads": [
      "' OR '1'='1",
      "\" OR \"1\"=\"1",
      "'; DROP TABLE users--"
    ],
    "payload_encoding": ["url", "html", "unicode"]
  }
}
```

### 2. 条件链式匹配

支持复杂的条件组合：

```json
{
  "conditions": [
    {
      "type": "response_analysis",
      "field": "response.status_code",
      "operator": "equals",
      "value": 200
    },
    {
      "type": "response_analysis",
      "field": "response.body",
      "operator": "regex",
      "regex": "error|exception|warning"
    }
  ]
}
```

### 3. 变量提取和传递

规则可以提取变量供后续使用：

```json
{
  "actions": [
    {
      "type": "set_variable",
      "parameters": {
        "name": "error_message",
        "value": "从响应中提取的错误信息"
      }
    }
  ]
}
```

## 📈 最佳实践

### 1. 规则命名规范
- 使用描述性的ID：`sql_injection_error_based`
- 遵循分类命名：`category_type_method`

### 2. 性能优化
- 合理设置超时时间
- 限制并发规则数量
- 使用高效的正则表达式

### 3. 误报控制
- 设置合适的置信度阈值
- 使用多个条件进行验证
- 定期更新和优化规则

### 4. 规则维护
- 定期审查和更新规则
- 记录规则变更历史
- 建立规则测试用例

## 🎉 总结

漏洞扫描引擎的插件化架构和规则配置系统提供了强大的扩展能力：

### ✅ 主要优势
- **无需编程**：通过JSON配置即可添加新检测能力
- **热更新**：规则可以动态加载，无需重启系统
- **模块化**：规则和引擎完全解耦，易于维护
- **可复用**：规则可以在不同环境中复用

### 🚀 扩展能力
- **检测规则**：支持各种漏洞类型的检测规则
- **规则组**：批量管理和执行相关规则
- **自定义条件**：扩展新的匹配条件类型
- **自定义动作**：添加新的执行动作类型

这种设计使得漏洞扫描器具有极强的可扩展性，可以快速适应新的安全威胁和检测需求，同时保持系统的稳定性和性能。
