package types

import (
	"time"
)

// ProfessionalScanResult 专业扫描结果
type ProfessionalScanResult struct {
	TaskID    string    `json:"task_id"`
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	Duration  string    `json:"duration"`
	Status    string    `json:"status"`

	// 阶段1: 信息收集结果
	InformationGathering *InformationGatheringResult `json:"information_gathering"`

	// 阶段2: 可疑点识别结果
	SuspiciousPoints *SuspiciousPointsResult `json:"suspicious_points"`

	// 阶段3: 风险检查结果
	RiskAssessment *RiskAssessmentResult `json:"risk_assessment"`

	// 综合统计
	Summary *ScanSummary `json:"summary"`
}

// InformationGatheringResult 信息收集结果
type InformationGatheringResult struct {
	// 基础信息
	TargetInfo *TargetInformation `json:"target_info"`

	// 端口和服务信息
	PortServices []*PortService `json:"port_services"`

	// Web组件信息
	WebComponents *WebComponentInfo `json:"web_components"`

	// 系统信息
	SystemInfo *SystemInformation `json:"system_info"`

	// 网络拓扑
	NetworkTopology *NetworkTopology `json:"network_topology"`

	// 敏感信息发现
	SensitiveInfo *SensitiveInformation `json:"sensitive_info"`

	// 防护措施信息
	ProtectionInfo *ProtectionInformation `json:"protection_info"`

	// 指纹识别信息
	FingerprintInfo *FingerprintInformation `json:"fingerprint_info"`

	// 收集统计
	Statistics *GatheringStatistics `json:"statistics"`
}

// TargetInformation 目标基础信息
type TargetInformation struct {
	URL        string            `json:"url"`
	Domain     string            `json:"domain"`
	IP         string            `json:"ip"`
	IPType     string            `json:"ip_type"` // IPv4/IPv6
	Location   *GeoLocation      `json:"location"`
	DNS        *DNSInformation   `json:"dns"`
	SSL        *SSLInformation   `json:"ssl"`
	Headers    map[string]string `json:"headers"`
	StatusCode int               `json:"status_code"`
	Title      string            `json:"title"`
	Favicon    string            `json:"favicon"`
}

// PortService 端口服务信息
type PortService struct {
	Port        int               `json:"port"`
	Protocol    string            `json:"protocol"` // TCP/UDP
	State       string            `json:"state"`    // open/closed/filtered
	Service     string            `json:"service"`  // http/ssh/ftp等
	Version     string            `json:"version"`
	Banner      string            `json:"banner"`
	Fingerprint map[string]string `json:"fingerprint"`
	IsSecure    bool              `json:"is_secure"`
}

// WebComponentInfo Web组件信息
type WebComponentInfo struct {
	// Web服务器
	WebServer *ComponentVersion `json:"web_server"`

	// 编程语言和框架
	Language  *ComponentVersion   `json:"language"`
	Framework *ComponentVersion   `json:"framework"`
	CMS       *ComponentVersion   `json:"cms"`
	Libraries []*ComponentVersion `json:"libraries"`

	// 数据库
	Database *ComponentVersion `json:"database"`

	// 中间件
	Middleware []*ComponentVersion `json:"middleware"`

	// 前端技术
	Frontend *FrontendTechnology `json:"frontend"`

	// 安全组件
	Security *SecurityComponents `json:"security"`
}

// ComponentVersion 组件版本信息
type ComponentVersion struct {
	Name          string            `json:"name"`
	Version       string            `json:"version"`
	FullVersion   string            `json:"full_version"`
	Confidence    float64           `json:"confidence"` // 置信度 0-1
	DetectMethod  string            `json:"detect_method"`
	Evidence      []string          `json:"evidence"`
	Metadata      map[string]string `json:"metadata"`
	IsOutdated    bool              `json:"is_outdated"`
	LatestVersion string            `json:"latest_version"`
}

// SystemInformation 系统信息
type SystemInformation struct {
	OS           *ComponentVersion `json:"os"`
	Architecture string            `json:"architecture"`
	Kernel       string            `json:"kernel"`
	Hostname     string            `json:"hostname"`
	Timezone     string            `json:"timezone"`
	Uptime       string            `json:"uptime"`
}

// SuspiciousPointsResult 可疑点识别结果
type SuspiciousPointsResult struct {
	// 交互入口点
	InteractionPoints []*InteractionPoint `json:"interaction_points"`

	// 过时组件
	OutdatedComponents []*OutdatedComponent `json:"outdated_components"`

	// 敏感配置
	SensitiveConfigs []*SensitiveConfig `json:"sensitive_configs"`

	// 信息泄露点
	InformationLeaks []*InformationLeak `json:"information_leaks"`

	// 弱安全配置
	WeakSecurityConfigs []*WeakSecurityConfig `json:"weak_security_configs"`

	// 可疑文件和目录
	SuspiciousFiles []*SuspiciousFile `json:"suspicious_files"`

	// 统计信息
	Statistics *SuspiciousPointsStatistics `json:"statistics"`
}

// InteractionPoint 交互入口点
type InteractionPoint struct {
	Type        string            `json:"type"` // form/parameter/upload/api等
	URL         string            `json:"url"`
	Method      string            `json:"method"` // GET/POST等
	Parameters  []*Parameter      `json:"parameters"`
	Headers     map[string]string `json:"headers"`
	Description string            `json:"description"`
	RiskLevel   string            `json:"risk_level"` // High/Medium/Low
	Evidence    []string          `json:"evidence"`
}

// Parameter 参数信息
type Parameter struct {
	Name        string   `json:"name"`
	Type        string   `json:"type"` // query/form/header/cookie
	Value       string   `json:"value"`
	Required    bool     `json:"required"`
	Validation  string   `json:"validation"`
	Constraints []string `json:"constraints"`
	RiskFactors []string `json:"risk_factors"`
}

// OutdatedComponent 过时组件
type OutdatedComponent struct {
	Component      *ComponentVersion `json:"component"`
	CurrentVersion string            `json:"current_version"`
	LatestVersion  string            `json:"latest_version"`
	CVEs           []*CVEInfo        `json:"cves"`
	RiskScore      float64           `json:"risk_score"`
	Recommendation string            `json:"recommendation"`
}

// CVEInfo CVE信息
type CVEInfo struct {
	ID          string   `json:"id"`
	CVSS        float64  `json:"cvss"`
	Severity    string   `json:"severity"`
	Description string   `json:"description"`
	Published   string   `json:"published"`
	References  []string `json:"references"`
}

// RiskAssessmentResult 风险检查结果
type RiskAssessmentResult struct {
	// 漏洞检测结果
	Vulnerabilities []*DetailedVulnerability `json:"vulnerabilities"`

	// 配置风险
	ConfigurationRisks []*ConfigurationRisk `json:"configuration_risks"`

	// 合规性检查
	ComplianceChecks []*ComplianceCheck `json:"compliance_checks"`

	// 安全评分
	SecurityScore *SecurityScore `json:"security_score"`

	// 风险矩阵
	RiskMatrix *RiskMatrix `json:"risk_matrix"`

	// 统计信息
	Statistics *RiskStatistics `json:"statistics"`
}

// DetailedVulnerability 详细漏洞信息
type DetailedVulnerability struct {
	*Vulnerability // 继承基础漏洞信息

	// 扩展信息
	OWASP        []string          `json:"owasp"`        // OWASP Top 10分类
	CWE          []string          `json:"cwe"`          // CWE分类
	CVSS         *CVSSScore        `json:"cvss"`         // CVSS评分
	ExploitInfo  *ExploitInfo      `json:"exploit_info"` // 利用信息
	Impact       *ImpactAnalysis   `json:"impact"`       // 影响分析
	Remediation  *RemediationInfo  `json:"remediation"`  // 修复信息
	References   []string          `json:"references"`   // 参考链接
	Timeline     *VulnTimeline     `json:"timeline"`     // 发现时间线
	Verification *VerificationInfo `json:"verification"` // 验证信息
}

// SecurityScore 安全评分
type SecurityScore struct {
	OverallScore    float64                   `json:"overall_score"`   // 总体评分 0-100
	CategoryScores  map[string]float64        `json:"category_scores"` // 分类评分
	RiskFactors     []*RiskFactor             `json:"risk_factors"`    // 风险因子
	Recommendations []*SecurityRecommendation `json:"recommendations"` // 安全建议
	Trend           *ScoreTrend               `json:"trend"`           // 评分趋势
}

// ScanSummary 扫描总结
type ScanSummary struct {
	// 扫描统计
	TotalTargets    int `json:"total_targets"`
	ScannedTargets  int `json:"scanned_targets"`
	SuccessfulScans int `json:"successful_scans"`
	FailedScans     int `json:"failed_scans"`

	// 发现统计
	TotalFindings      int `json:"total_findings"`
	VulnerabilityCount int `json:"vulnerability_count"`
	RiskCount          int `json:"risk_count"`
	InfoCount          int `json:"info_count"`

	// 严重程度分布
	SeverityDistribution map[string]int `json:"severity_distribution"`

	// 类型分布
	TypeDistribution map[string]int `json:"type_distribution"`

	// 置信度分布
	ConfidenceDistribution map[string]int `json:"confidence_distribution"`

	// 性能统计
	Performance *PerformanceMetrics `json:"performance"`
}

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	TotalRequests    int           `json:"total_requests"`
	SuccessRequests  int           `json:"success_requests"`
	FailedRequests   int           `json:"failed_requests"`
	AverageResponse  time.Duration `json:"average_response"`
	MaxResponse      time.Duration `json:"max_response"`
	MinResponse      time.Duration `json:"min_response"`
	ThroughputPerSec float64       `json:"throughput_per_sec"`
	ErrorRate        float64       `json:"error_rate"`
}

// 其他辅助结构体...
type GeoLocation struct {
	Country   string  `json:"country"`
	Region    string  `json:"region"`
	City      string  `json:"city"`
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
	ISP       string  `json:"isp"`
	ASN       string  `json:"asn"`
}

type DNSInformation struct {
	ARecords     []string `json:"a_records"`
	AAAARecords  []string `json:"aaaa_records"`
	CNAMERecords []string `json:"cname_records"`
	MXRecords    []string `json:"mx_records"`
	TXTRecords   []string `json:"txt_records"`
	NSRecords    []string `json:"ns_records"`
}

type SSLInformation struct {
	Enabled         bool      `json:"enabled"`
	Version         string    `json:"version"`
	Cipher          string    `json:"cipher"`
	Certificate     *CertInfo `json:"certificate"`
	Vulnerabilities []string  `json:"vulnerabilities"`
}

type CertInfo struct {
	Subject    string    `json:"subject"`
	Issuer     string    `json:"issuer"`
	NotBefore  time.Time `json:"not_before"`
	NotAfter   time.Time `json:"not_after"`
	IsExpired  bool      `json:"is_expired"`
	IsWildcard bool      `json:"is_wildcard"`
	SANs       []string  `json:"sans"`
}

// 更多辅助结构体
type NetworkTopology struct {
	Subnets     []string `json:"subnets"`
	Gateways    []string `json:"gateways"`
	NameServers []string `json:"name_servers"`
	Routes      []string `json:"routes"`
}

type FrontendTechnology struct {
	JavaScript []*ComponentVersion `json:"javascript"`
	CSS        []*ComponentVersion `json:"css"`
	Frameworks []*ComponentVersion `json:"frameworks"`
	CDNs       []string            `json:"cdns"`
}

type SecurityComponents struct {
	WAF          *ComponentVersion `json:"waf"`
	Firewall     *ComponentVersion `json:"firewall"`
	AntiVirus    *ComponentVersion `json:"antivirus"`
	IDS          *ComponentVersion `json:"ids"`
	LoadBalancer *ComponentVersion `json:"load_balancer"`
}

type SensitiveConfig struct {
	Type        string   `json:"type"`     // debug/admin/backup等
	Location    string   `json:"location"` // URL或文件路径
	Description string   `json:"description"`
	RiskLevel   string   `json:"risk_level"`
	Evidence    []string `json:"evidence"`
	Impact      string   `json:"impact"`
}

type InformationLeak struct {
	Type        string   `json:"type"` // error/debug/source等
	Content     string   `json:"content"`
	Location    string   `json:"location"`
	Sensitivity string   `json:"sensitivity"` // High/Medium/Low
	Category    string   `json:"category"`    // credentials/paths/versions等
	Evidence    []string `json:"evidence"`
}

// SensitiveInformation 敏感信息发现结果
type SensitiveInformation struct {
	// 配置文件
	ConfigFiles []*SensitiveFile `json:"config_files"`

	// 备份文件
	BackupFiles []*SensitiveFile `json:"backup_files"`

	// 源码泄露
	SourceCodeLeaks []*SensitiveFile `json:"source_code_leaks"`

	// 敏感目录
	SensitiveDirectories []*SensitiveDirectory `json:"sensitive_directories"`

	// 信息泄露
	InformationLeaks []*InformationLeak `json:"information_leaks"`

	// 统计信息
	TotalFiles      int `json:"total_files"`
	HighRiskFiles   int `json:"high_risk_files"`
	MediumRiskFiles int `json:"medium_risk_files"`
	LowRiskFiles    int `json:"low_risk_files"`
}

// SensitiveFile 敏感文件信息
type SensitiveFile struct {
	Path        string            `json:"path"`        // 文件路径
	Type        string            `json:"type"`        // 文件类型 (config/backup/source)
	Size        int64             `json:"size"`        // 文件大小
	Content     string            `json:"content"`     // 文件内容（部分）
	Risk        string            `json:"risk"`        // 风险级别 (high/medium/low)
	Description string            `json:"description"` // 描述
	Headers     map[string]string `json:"headers"`     // HTTP头
	StatusCode  int               `json:"status_code"` // 状态码
	Evidence    []string          `json:"evidence"`    // 证据
	Confidence  float64           `json:"confidence"`  // 置信度
}

// SensitiveDirectory 敏感目录信息
type SensitiveDirectory struct {
	Path        string   `json:"path"`        // 目录路径
	Type        string   `json:"type"`        // 目录类型 (admin/backup/debug)
	Risk        string   `json:"risk"`        // 风险级别
	Description string   `json:"description"` // 描述
	StatusCode  int      `json:"status_code"` // 状态码
	Evidence    []string `json:"evidence"`    // 证据
}

// ProtectionInformation 防护措施信息
type ProtectionInformation struct {
	// WAF信息
	WAF *WAFInformation `json:"waf"`

	// 主机防护
	HostProtection *HostProtectionInfo `json:"host_protection"`

	// 网络防护
	NetworkProtection *NetworkProtectionInfo `json:"network_protection"`

	// 安全头
	SecurityHeaders *SecurityHeadersInfo `json:"security_headers"`

	// 防护等级评估
	ProtectionLevel string `json:"protection_level"` // high/medium/low/none
	ProtectionScore int    `json:"protection_score"` // 0-100
}

// WAFInformation WAF信息
type WAFInformation struct {
	Detected    bool     `json:"detected"`     // 是否检测到WAF
	Type        string   `json:"type"`         // WAF类型
	Vendor      string   `json:"vendor"`       // 厂商
	Version     string   `json:"version"`      // 版本
	Confidence  float64  `json:"confidence"`   // 置信度
	Evidence    []string `json:"evidence"`     // 检测证据
	BypassHints []string `json:"bypass_hints"` // 绕过提示
}

// HostProtectionInfo 主机防护信息
type HostProtectionInfo struct {
	Firewall       *FirewallInfo       `json:"firewall"`        // 防火墙
	AntiVirus      *AntiVirusInfo      `json:"antivirus"`       // 杀毒软件
	HIDS           *HIDSInfo           `json:"hids"`            // 主机入侵检测
	ProcessMonitor *ProcessMonitorInfo `json:"process_monitor"` // 进程监控
	FileMonitor    *FileMonitorInfo    `json:"file_monitor"`    // 文件监控
}

// NetworkProtectionInfo 网络防护信息
type NetworkProtectionInfo struct {
	IDS            *IDSInfo            `json:"ids"`             // 入侵检测系统
	IPS            *IPSInfo            `json:"ips"`             // 入侵防护系统
	DDoSProtection *DDoSProtectionInfo `json:"ddos_protection"` // DDoS防护
	LoadBalancer   *LoadBalancerInfo   `json:"load_balancer"`   // 负载均衡
	CDN            *CDNInfo            `json:"cdn"`             // CDN
}

// SecurityHeadersInfo 安全头信息
type SecurityHeadersInfo struct {
	HSTS           *HSTSInfo           `json:"hsts"`            // HTTP严格传输安全
	CSP            *CSPInfo            `json:"csp"`             // 内容安全策略
	XFrameOptions  *XFrameOptionsInfo  `json:"x_frame_options"` // X-Frame-Options
	XContentType   *XContentTypeInfo   `json:"x_content_type"`  // X-Content-Type-Options
	XSSProtection  *XSSProtectionInfo  `json:"xss_protection"`  // X-XSS-Protection
	ReferrerPolicy *ReferrerPolicyInfo `json:"referrer_policy"` // Referrer-Policy
}

// FingerprintInformation 指纹识别信息
type FingerprintInformation struct {
	// HTTP头分析
	HTTPHeaders *HTTPHeaderAnalysis `json:"http_headers"`

	// 页面内容分析
	PageContent *PageContentAnalysis `json:"page_content"`

	// 错误页面分析
	ErrorPages *ErrorPageAnalysis `json:"error_pages"`

	// Cookie分析
	Cookies *CookieAnalysis `json:"cookies"`

	// 技术栈指纹
	TechStackFingerprints []*TechStackFingerprint `json:"tech_stack_fingerprints"`

	// 综合置信度
	OverallConfidence float64 `json:"overall_confidence"`
}

type WeakSecurityConfig struct {
	Component        string   `json:"component"`
	Setting          string   `json:"setting"`
	CurrentValue     string   `json:"current_value"`
	RecommendedValue string   `json:"recommended_value"`
	RiskLevel        string   `json:"risk_level"`
	Description      string   `json:"description"`
	References       []string `json:"references"`
}

type SuspiciousFile struct {
	Path        string    `json:"path"`
	Type        string    `json:"type"` // backup/config/log等
	Size        int64     `json:"size"`
	Modified    time.Time `json:"modified"`
	Permissions string    `json:"permissions"`
	RiskFactors []string  `json:"risk_factors"`
	Content     string    `json:"content"` // 部分内容
}

type ConfigurationRisk struct {
	Category   string   `json:"category"` // server/application/network
	Component  string   `json:"component"`
	Issue      string   `json:"issue"`
	RiskLevel  string   `json:"risk_level"`
	Impact     string   `json:"impact"`
	Likelihood string   `json:"likelihood"`
	Mitigation string   `json:"mitigation"`
	References []string `json:"references"`
}

type ComplianceCheck struct {
	Standard    string   `json:"standard"` // OWASP/PCI-DSS/ISO27001等
	Requirement string   `json:"requirement"`
	Status      string   `json:"status"` // Pass/Fail/Warning
	Description string   `json:"description"`
	Evidence    []string `json:"evidence"`
	Remediation string   `json:"remediation"`
}

type CVSSScore struct {
	Version  string       `json:"version"`
	Vector   string       `json:"vector"`
	Score    float64      `json:"score"`
	Severity string       `json:"severity"`
	Metrics  *CVSSMetrics `json:"metrics"`
}

type CVSSMetrics struct {
	AttackVector       string `json:"attack_vector"`
	AttackComplexity   string `json:"attack_complexity"`
	PrivilegesRequired string `json:"privileges_required"`
	UserInteraction    string `json:"user_interaction"`
	Scope              string `json:"scope"`
	Confidentiality    string `json:"confidentiality"`
	Integrity          string `json:"integrity"`
	Availability       string `json:"availability"`
}

type ExploitInfo struct {
	Difficulty    string   `json:"difficulty"` // Easy/Medium/Hard
	Prerequisites []string `json:"prerequisites"`
	Steps         []string `json:"steps"`
	Tools         []string `json:"tools"`
	Payload       string   `json:"payload"`
	PublicExploit bool     `json:"public_exploit"`
	ExploitDBID   string   `json:"exploitdb_id"`
}

type ImpactAnalysis struct {
	Confidentiality string   `json:"confidentiality"` // High/Medium/Low/None
	Integrity       string   `json:"integrity"`
	Availability    string   `json:"availability"`
	BusinessImpact  []string `json:"business_impact"`
	TechnicalImpact []string `json:"technical_impact"`
	DataAtRisk      []string `json:"data_at_risk"`
}

type RemediationInfo struct {
	Priority     string   `json:"priority"` // Critical/High/Medium/Low
	Effort       string   `json:"effort"`   // Hours/Days/Weeks
	Cost         string   `json:"cost"`     // Low/Medium/High
	Steps        []string `json:"steps"`
	Verification []string `json:"verification"`
	Prevention   []string `json:"prevention"`
	Resources    []string `json:"resources"`
}

type VulnTimeline struct {
	Discovered time.Time `json:"discovered"`
	Verified   time.Time `json:"verified"`
	Reported   time.Time `json:"reported"`
	FirstSeen  time.Time `json:"first_seen"`
	LastSeen   time.Time `json:"last_seen"`
}

type VerificationInfo struct {
	Method        string    `json:"method"`     // Manual/Automated
	Confidence    float64   `json:"confidence"` // 0-1
	Evidence      []string  `json:"evidence"`
	VerifiedBy    string    `json:"verified_by"`
	VerifiedAt    time.Time `json:"verified_at"`
	FalsePositive bool      `json:"false_positive"`
}

type RiskFactor struct {
	Name        string  `json:"name"`
	Weight      float64 `json:"weight"`
	Score       float64 `json:"score"`
	Description string  `json:"description"`
}

type SecurityRecommendation struct {
	Priority    string   `json:"priority"`
	Category    string   `json:"category"`
	Title       string   `json:"title"`
	Description string   `json:"description"`
	Actions     []string `json:"actions"`
	Impact      string   `json:"impact"`
	Effort      string   `json:"effort"`
}

type ScoreTrend struct {
	Previous    float64   `json:"previous"`
	Current     float64   `json:"current"`
	Change      float64   `json:"change"`
	Direction   string    `json:"direction"` // Improving/Declining/Stable
	LastUpdated time.Time `json:"last_updated"`
}

type RiskMatrix struct {
	HighHigh     int `json:"high_high"`
	HighMedium   int `json:"high_medium"`
	HighLow      int `json:"high_low"`
	MediumHigh   int `json:"medium_high"`
	MediumMedium int `json:"medium_medium"`
	MediumLow    int `json:"medium_low"`
	LowHigh      int `json:"low_high"`
	LowMedium    int `json:"low_medium"`
	LowLow       int `json:"low_low"`
}

// 统计结构体
type GatheringStatistics struct {
	PortsScanned      int           `json:"ports_scanned"`
	ServicesFound     int           `json:"services_found"`
	ComponentsFound   int           `json:"components_found"`
	FilesFound        int           `json:"files_found"`
	Duration          time.Duration `json:"duration"`
	RequestsSent      int           `json:"requests_sent"`
	ErrorsEncountered int           `json:"errors_encountered"`
}

type SuspiciousPointsStatistics struct {
	TotalPoints        int `json:"total_points"`
	HighRiskPoints     int `json:"high_risk_points"`
	MediumRiskPoints   int `json:"medium_risk_points"`
	LowRiskPoints      int `json:"low_risk_points"`
	InteractionPoints  int `json:"interaction_points"`
	OutdatedComponents int `json:"outdated_components"`
	ConfigIssues       int `json:"config_issues"`
	InformationLeaks   int `json:"information_leaks"`
}

type RiskStatistics struct {
	TotalVulnerabilities int `json:"total_vulnerabilities"`
	CriticalVulns        int `json:"critical_vulns"`
	HighVulns            int `json:"high_vulns"`
	MediumVulns          int `json:"medium_vulns"`
	LowVulns             int `json:"low_vulns"`
	InfoVulns            int `json:"info_vulns"`
	ConfigRisks          int `json:"config_risks"`
	ComplianceIssues     int `json:"compliance_issues"`
	VerifiedVulns        int `json:"verified_vulns"`
	FalsePositives       int `json:"false_positives"`
}
