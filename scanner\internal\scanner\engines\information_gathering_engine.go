package engines

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"

	"scanner/internal/scanner/core"
)

// InformationGatheringEngine 信息收集引擎
// 负责目标信息收集、指纹识别、服务发现等功能
type InformationGatheringEngine struct {
	*core.BaseEngine

	// HTTP客户端
	httpClient *http.Client

	// 配置
	config *InfoGatheringConfig

	// 组件识别器
	componentDetector *ComponentDetector
	serviceDetector   *ServiceDetector
	fingerprintEngine *FingerprintEngine
}

// InfoGatheringConfig 信息收集配置
type InfoGatheringConfig struct {
	// HTTP配置
	Timeout      time.Duration `json:"timeout"`
	MaxRedirects int           `json:"max_redirects"`
	UserAgent    string        `json:"user_agent"`

	// 扫描配置
	PortScanEnabled  bool   `json:"port_scan_enabled"`
	PortRange        string `json:"port_range"`
	ServiceDetection bool   `json:"service_detection"`

	// 指纹识别
	FingerprintEnabled bool `json:"fingerprint_enabled"`
	DeepAnalysis       bool `json:"deep_analysis"`

	// 并发控制
	MaxConcurrency int `json:"max_concurrency"`
}

// ComponentDetector 组件检测器
type ComponentDetector struct {
	patterns map[string]*ComponentPattern
}

// ComponentPattern 组件识别模式
type ComponentPattern struct {
	Name     string           `json:"name"`
	Category string           `json:"category"`
	Patterns []*DetectionRule `json:"patterns"`
	Website  string           `json:"website"`
	Icon     string           `json:"icon"`
}

// DetectionRule 检测规则
type DetectionRule struct {
	Type       string  `json:"type"`       // header, body, script, meta, cookie
	Pattern    string  `json:"pattern"`    // 匹配模式
	Regex      bool    `json:"regex"`      // 是否为正则表达式
	Confidence float64 `json:"confidence"` // 置信度
	Version    string  `json:"version"`    // 版本提取模式
}

// ServiceDetector 服务检测器
type ServiceDetector struct {
	signatures map[int]*ServiceSignature
}

// ServiceSignature 服务签名
type ServiceSignature struct {
	Port     int      `json:"port"`
	Protocol string   `json:"protocol"`
	Service  string   `json:"service"`
	Probes   []string `json:"probes"`
	Patterns []string `json:"patterns"`
}

// FingerprintEngine 指纹识别引擎
type FingerprintEngine struct {
	webFingerprints     []*WebFingerprint
	serviceFingerprints []*ServiceFingerprint
}

// WebFingerprint Web指纹
type WebFingerprint struct {
	Name       string            `json:"name"`
	Category   string            `json:"category"`
	Headers    map[string]string `json:"headers"`
	Body       []string          `json:"body"`
	StatusCode int               `json:"status_code"`
	Confidence float64           `json:"confidence"`
}

// ServiceFingerprint 服务指纹
type ServiceFingerprint struct {
	Service    string   `json:"service"`
	Port       int      `json:"port"`
	Protocol   string   `json:"protocol"`
	Banners    []string `json:"banners"`
	Probes     []string `json:"probes"`
	Confidence float64  `json:"confidence"`
}

// NewInformationGatheringEngine 创建信息收集引擎
func NewInformationGatheringEngine() *InformationGatheringEngine {
	baseEngine := core.NewBaseEngine(
		"信息收集引擎",
		core.EngineTypeInformationGathering,
		"1.0.0",
		"负责目标信息收集、指纹识别、服务发现等功能",
	)

	config := &InfoGatheringConfig{
		Timeout:            30 * time.Second,
		MaxRedirects:       5,
		UserAgent:          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
		PortScanEnabled:    true,
		PortRange:          "1-1000",
		ServiceDetection:   true,
		FingerprintEnabled: true,
		DeepAnalysis:       true,
		MaxConcurrency:     10,
	}

	httpClient := &http.Client{
		Timeout: config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if len(via) >= config.MaxRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	engine := &InformationGatheringEngine{
		BaseEngine:        baseEngine,
		httpClient:        httpClient,
		config:            config,
		componentDetector: NewComponentDetector(),
		serviceDetector:   NewServiceDetector(),
		fingerprintEngine: NewFingerprintEngine(),
	}

	return engine
}

// GetSupportedTargets 获取支持的目标类型
func (e *InformationGatheringEngine) GetSupportedTargets() []core.TargetType {
	return []core.TargetType{
		core.TargetTypeURL,
		core.TargetTypeIP,
		core.TargetTypeDomain,
		core.TargetTypeHost,
	}
}

// Scan 执行信息收集扫描
func (e *InformationGatheringEngine) Scan(ctx context.Context, request *core.ScanRequest) (*core.ScanResult, error) {
	// 创建任务上下文
	task, taskCtx := e.CreateTaskContext(request)
	defer func() {
		if task.CancelFunc != nil {
			task.CancelFunc()
		}
	}()

	e.LogInfo("开始信息收集扫描: %s", request.Target.Value)

	// 创建扫描结果
	result := &core.ScanResult{
		ID:              fmt.Sprintf("info_%s_%d", request.ID, time.Now().Unix()),
		TaskID:          request.Config.TaskID,
		EngineType:      e.GetType(),
		Target:          request.Target,
		Status:          core.StatusRunning,
		Progress:        0.0,
		StartedAt:       time.Now(),
		Vulnerabilities: make([]*core.Vulnerability, 0),
		Information:     &core.InformationResult{},
		Statistics:      &core.ScanStatistics{},
		Warnings:        make([]string, 0),
		Metadata:        make(map[string]interface{}),
	}

	// 执行信息收集步骤
	steps := []struct {
		name     string
		progress float64
		function func(context.Context, *core.ScanRequest, *core.ScanResult) error
	}{
		{"目标基础信息收集", 20, e.collectBasicInfo},
		{"技术栈识别", 40, e.identifyTechStack},
		{"服务发现", 60, e.discoverServices},
		{"安全信息收集", 80, e.collectSecurityInfo},
		{"内容分析", 100, e.analyzeContent},
	}

	for _, step := range steps {
		select {
		case <-taskCtx.Done():
			result.Status = core.StatusCancelled
			e.CompleteTask(request.ID, result, fmt.Errorf("扫描被取消"))
			return result, fmt.Errorf("扫描被取消")
		default:
		}

		e.LogInfo("执行步骤: %s", step.name)
		e.UpdateTaskProgress(request.ID, step.progress, step.name)

		if err := step.function(taskCtx, request, result); err != nil {
			e.LogError("步骤执行失败 %s: %v", step.name, err)
			result.Warnings = append(result.Warnings, fmt.Sprintf("步骤 %s 执行失败: %v", step.name, err))
		}
	}

	// 完成扫描
	result.Status = core.StatusCompleted
	result.Progress = 100.0
	completedAt := time.Now()
	result.CompletedAt = &completedAt
	result.Duration = time.Since(result.StartedAt)

	e.LogInfo("信息收集扫描完成: %s", request.Target.Value)
	e.CompleteTask(request.ID, result, nil)

	return result, nil
}

// collectBasicInfo 收集目标基础信息
func (e *InformationGatheringEngine) collectBasicInfo(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	target := request.Target

	// 初始化目标信息
	targetInfo := &core.TargetInformation{
		Headers:  make(map[string]string),
		Cookies:  make([]*core.CookieInfo, 0),
		Metadata: make(map[string]string),
	}

	switch target.Type {
	case core.TargetTypeURL:
		return e.collectURLInfo(ctx, target.Value, targetInfo)
	case core.TargetTypeIP:
		return e.collectIPInfo(ctx, target.Value, targetInfo)
	case core.TargetTypeDomain:
		return e.collectDomainInfo(ctx, target.Value, targetInfo)
	case core.TargetTypeHost:
		return e.collectHostInfo(ctx, target.Value, targetInfo)
	}

	result.Information.TargetInfo = targetInfo
	return nil
}

// collectURLInfo 收集URL信息
func (e *InformationGatheringEngine) collectURLInfo(ctx context.Context, targetURL string, targetInfo *core.TargetInformation) error {
	// 解析URL
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return fmt.Errorf("URL解析失败: %v", err)
	}

	targetInfo.URL = targetURL
	targetInfo.Domain = parsedURL.Hostname()
	targetInfo.Protocol = parsedURL.Scheme

	// 解析端口
	if parsedURL.Port() != "" {
		if port, err := strconv.Atoi(parsedURL.Port()); err == nil {
			targetInfo.Port = port
		}
	} else {
		// 默认端口
		switch parsedURL.Scheme {
		case "http":
			targetInfo.Port = 80
		case "https":
			targetInfo.Port = 443
		}
	}

	// 发送HTTP请求
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		return fmt.Errorf("创建HTTP请求失败: %v", err)
	}

	req.Header.Set("User-Agent", e.config.UserAgent)

	startTime := time.Now()
	resp, err := e.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	targetInfo.ResponseTime = time.Since(startTime)
	targetInfo.StatusCode = resp.StatusCode
	targetInfo.ContentType = resp.Header.Get("Content-Type")
	targetInfo.Server = resp.Header.Get("Server")
	targetInfo.LastSeen = time.Now()

	// 收集HTTP头
	for name, values := range resp.Header {
		if len(values) > 0 {
			targetInfo.Headers[name] = values[0]
		}
	}

	// 收集Cookie
	for _, cookie := range resp.Cookies() {
		cookieInfo := &core.CookieInfo{
			Name:     cookie.Name,
			Value:    cookie.Value,
			Domain:   cookie.Domain,
			Path:     cookie.Path,
			Secure:   cookie.Secure,
			HttpOnly: cookie.HttpOnly,
			SameSite: fmt.Sprintf("%v", cookie.SameSite),
		}
		if !cookie.Expires.IsZero() {
			cookieInfo.Expires = cookie.Expires.Format(time.RFC3339)
		}
		if cookie.MaxAge > 0 {
			cookieInfo.MaxAge = cookie.MaxAge
		}
		targetInfo.Cookies = append(targetInfo.Cookies, cookieInfo)
	}

	// 读取页面内容（前1KB用于标题提取）
	buffer := make([]byte, 1024)
	n, _ := resp.Body.Read(buffer)
	content := string(buffer[:n])

	// 提取页面标题
	if title := e.extractTitle(content); title != "" {
		targetInfo.Title = title
	}

	// 解析IP地址
	if ips, err := net.LookupIP(parsedURL.Hostname()); err == nil && len(ips) > 0 {
		targetInfo.IP = ips[0].String()
	}

	return nil
}

// collectIPInfo 收集IP信息
func (e *InformationGatheringEngine) collectIPInfo(ctx context.Context, ip string, targetInfo *core.TargetInformation) error {
	targetInfo.IP = ip

	// 尝试反向DNS解析
	if names, err := net.LookupAddr(ip); err == nil && len(names) > 0 {
		targetInfo.Domain = names[0]
	}

	// 尝试连接常见端口
	commonPorts := []int{80, 443, 22, 21, 25, 53, 110, 143, 993, 995}
	for _, port := range commonPorts {
		if e.isPortOpen(ip, port) {
			targetInfo.Port = port
			break
		}
	}

	return nil
}

// collectDomainInfo 收集域名信息
func (e *InformationGatheringEngine) collectDomainInfo(ctx context.Context, domain string, targetInfo *core.TargetInformation) error {
	targetInfo.Domain = domain

	// DNS解析
	if ips, err := net.LookupIP(domain); err == nil && len(ips) > 0 {
		targetInfo.IP = ips[0].String()
	}

	// 尝试HTTP/HTTPS连接
	for _, scheme := range []string{"https", "http"} {
		testURL := fmt.Sprintf("%s://%s", scheme, domain)
		if err := e.collectURLInfo(ctx, testURL, targetInfo); err == nil {
			targetInfo.URL = testURL
			targetInfo.Protocol = scheme
			break
		}
	}

	return nil
}

// collectHostInfo 收集主机信息
func (e *InformationGatheringEngine) collectHostInfo(ctx context.Context, host string, targetInfo *core.TargetInformation) error {
	// 主机可能是IP或域名
	if net.ParseIP(host) != nil {
		return e.collectIPInfo(ctx, host, targetInfo)
	} else {
		return e.collectDomainInfo(ctx, host, targetInfo)
	}
}

// identifyTechStack 识别技术栈
func (e *InformationGatheringEngine) identifyTechStack(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	// 这里实现技术栈识别逻辑
	techStack := &core.TechnologyStack{
		JavaScript:  make([]*core.ComponentInfo, 0),
		CSS:         make([]*core.ComponentInfo, 0),
		Middleware:  make([]*core.ComponentInfo, 0),
		Analytics:   make([]*core.ComponentInfo, 0),
		Advertising: make([]*core.ComponentInfo, 0),
		Widgets:     make([]*core.ComponentInfo, 0),
	}

	// 基于HTTP头识别技术栈
	if result.Information.TargetInfo != nil {
		e.identifyFromHeaders(result.Information.TargetInfo.Headers, techStack)
	}

	result.Information.TechStack = techStack
	return nil
}

// discoverServices 发现服务
func (e *InformationGatheringEngine) discoverServices(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	if !e.config.PortScanEnabled {
		return nil
	}

	services := make([]*core.ServiceInfo, 0)

	// 获取目标IP
	var targetIP string
	if result.Information.TargetInfo != nil {
		targetIP = result.Information.TargetInfo.IP
	}

	if targetIP == "" {
		return fmt.Errorf("无法获取目标IP地址")
	}

	// 解析端口范围
	ports, err := e.parsePortRange(e.config.PortRange)
	if err != nil {
		return fmt.Errorf("端口范围解析失败: %v", err)
	}

	// 扫描端口
	for _, port := range ports {
		if e.isPortOpen(targetIP, port) {
			service := &core.ServiceInfo{
				Port:     port,
				Protocol: "tcp",
				State:    "open",
				Metadata: make(map[string]string),
			}

			// 服务识别
			if e.config.ServiceDetection {
				e.identifyService(targetIP, port, service)
			}

			services = append(services, service)
		}
	}

	result.Information.Services = services
	return nil
}

// collectSecurityInfo 收集安全信息
func (e *InformationGatheringEngine) collectSecurityInfo(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	securityInfo := &core.SecurityInformation{
		AuthMethods:    make([]string, 0),
		LoginForms:     make([]*core.FormInfo, 0),
		KnownVulns:     make([]*core.KnownVuln, 0),
		SecurityIssues: make([]*core.SecurityIssue, 0),
		Metadata:       make(map[string]string),
	}

	// 分析安全头
	if result.Information.TargetInfo != nil {
		securityInfo.SecurityHeaders = e.analyzeSecurityHeaders(result.Information.TargetInfo.Headers)
	}

	result.Information.SecurityInfo = securityInfo
	return nil
}

// analyzeContent 分析内容
func (e *InformationGatheringEngine) analyzeContent(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	contentInfo := &core.ContentInformation{
		Keywords:      make([]string, 0),
		InternalLinks: make([]string, 0),
		ExternalLinks: make([]string, 0),
		Images:        make([]*core.MediaInfo, 0),
		Videos:        make([]*core.MediaInfo, 0),
		Audio:         make([]*core.MediaInfo, 0),
		Scripts:       make([]string, 0),
		Stylesheets:   make([]string, 0),
		MetaTags:      make(map[string]string),
		Metadata:      make(map[string]string),
	}

	result.Information.ContentInfo = contentInfo
	return nil
}

// 辅助方法

// extractTitle 提取页面标题
func (e *InformationGatheringEngine) extractTitle(content string) string {
	re := regexp.MustCompile(`<title[^>]*>([^<]+)</title>`)
	matches := re.FindStringSubmatch(content)
	if len(matches) > 1 {
		return strings.TrimSpace(matches[1])
	}
	return ""
}

// isPortOpen 检查端口是否开放
func (e *InformationGatheringEngine) isPortOpen(host string, port int) bool {
	timeout := 3 * time.Second
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", host, port), timeout)
	if err != nil {
		return false
	}
	conn.Close()
	return true
}

// parsePortRange 解析端口范围
func (e *InformationGatheringEngine) parsePortRange(portRange string) ([]int, error) {
	var ports []int

	parts := strings.Split(portRange, ",")
	for _, part := range parts {
		part = strings.TrimSpace(part)

		if strings.Contains(part, "-") {
			// 端口范围
			rangeParts := strings.Split(part, "-")
			if len(rangeParts) != 2 {
				continue
			}

			start, err1 := strconv.Atoi(strings.TrimSpace(rangeParts[0]))
			end, err2 := strconv.Atoi(strings.TrimSpace(rangeParts[1]))

			if err1 != nil || err2 != nil || start > end {
				continue
			}

			for i := start; i <= end; i++ {
				ports = append(ports, i)
			}
		} else {
			// 单个端口
			port, err := strconv.Atoi(part)
			if err == nil {
				ports = append(ports, port)
			}
		}
	}

	return ports, nil
}

// identifyFromHeaders 从HTTP头识别技术栈
func (e *InformationGatheringEngine) identifyFromHeaders(headers map[string]string, techStack *core.TechnologyStack) {
	// Server头分析
	if server, exists := headers["Server"]; exists {
		if component := e.parseServerHeader(server); component != nil {
			techStack.WebServer = component
		}
	}

	// X-Powered-By头分析
	if poweredBy, exists := headers["X-Powered-By"]; exists {
		if component := e.parsePoweredByHeader(poweredBy); component != nil {
			techStack.Language = component
		}
	}
}

// parseServerHeader 解析Server头
func (e *InformationGatheringEngine) parseServerHeader(server string) *core.ComponentInfo {
	server = strings.ToLower(server)

	if strings.Contains(server, "apache") {
		return &core.ComponentInfo{
			Name:        "Apache",
			Category:    "Web Server",
			Confidence:  0.9,
			Evidence:    []string{fmt.Sprintf("Server: %s", server)},
			Website:     "https://httpd.apache.org/",
			Description: "Apache HTTP Server",
		}
	}

	if strings.Contains(server, "nginx") {
		return &core.ComponentInfo{
			Name:        "Nginx",
			Category:    "Web Server",
			Confidence:  0.9,
			Evidence:    []string{fmt.Sprintf("Server: %s", server)},
			Website:     "https://nginx.org/",
			Description: "Nginx Web Server",
		}
	}

	return nil
}

// parsePoweredByHeader 解析X-Powered-By头
func (e *InformationGatheringEngine) parsePoweredByHeader(poweredBy string) *core.ComponentInfo {
	poweredBy = strings.ToLower(poweredBy)

	if strings.Contains(poweredBy, "php") {
		return &core.ComponentInfo{
			Name:        "PHP",
			Category:    "Programming Language",
			Confidence:  0.95,
			Evidence:    []string{fmt.Sprintf("X-Powered-By: %s", poweredBy)},
			Website:     "https://www.php.net/",
			Description: "PHP Programming Language",
		}
	}

	return nil
}

// identifyService 识别服务
func (e *InformationGatheringEngine) identifyService(host string, port int, service *core.ServiceInfo) {
	// 根据端口识别常见服务
	commonServices := map[int]string{
		21:    "ftp",
		22:    "ssh",
		23:    "telnet",
		25:    "smtp",
		53:    "dns",
		80:    "http",
		110:   "pop3",
		143:   "imap",
		443:   "https",
		993:   "imaps",
		995:   "pop3s",
		1433:  "mssql",
		3306:  "mysql",
		5432:  "postgresql",
		6379:  "redis",
		27017: "mongodb",
	}

	if serviceName, exists := commonServices[port]; exists {
		service.Name = serviceName
		service.Product = serviceName
		service.Confidence = 0.8
	}

	// 尝试获取服务横幅
	if banner := e.getBanner(host, port); banner != "" {
		service.Banner = banner
		service.Fingerprint = banner

		// 从横幅中提取更多信息
		e.parseServiceBanner(banner, service)
	}
}

// getBanner 获取服务横幅
func (e *InformationGatheringEngine) getBanner(host string, port int) string {
	timeout := 5 * time.Second
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", host, port), timeout)
	if err != nil {
		return ""
	}
	defer conn.Close()

	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(timeout))

	// 读取横幅
	buffer := make([]byte, 1024)
	n, err := conn.Read(buffer)
	if err != nil {
		return ""
	}

	return strings.TrimSpace(string(buffer[:n]))
}

// parseServiceBanner 解析服务横幅
func (e *InformationGatheringEngine) parseServiceBanner(banner string, service *core.ServiceInfo) {
	banner = strings.ToLower(banner)

	// SSH服务
	if strings.Contains(banner, "ssh") {
		service.Name = "ssh"
		service.Product = "OpenSSH"
		if matches := regexp.MustCompile(`openssh[_\s]+([\d\.]+)`).FindStringSubmatch(banner); len(matches) > 1 {
			service.Version = matches[1]
		}
	}

	// HTTP服务
	if strings.Contains(banner, "http") {
		service.Name = "http"
		if strings.Contains(banner, "apache") {
			service.Product = "Apache"
			if matches := regexp.MustCompile(`apache/([\d\.]+)`).FindStringSubmatch(banner); len(matches) > 1 {
				service.Version = matches[1]
			}
		} else if strings.Contains(banner, "nginx") {
			service.Product = "Nginx"
			if matches := regexp.MustCompile(`nginx/([\d\.]+)`).FindStringSubmatch(banner); len(matches) > 1 {
				service.Version = matches[1]
			}
		}
	}
}

// analyzeSecurityHeaders 分析安全头
func (e *InformationGatheringEngine) analyzeSecurityHeaders(headers map[string]string) *core.SecurityHeaders {
	secHeaders := &core.SecurityHeaders{}

	// HSTS分析
	if hsts, exists := headers["Strict-Transport-Security"]; exists {
		secHeaders.HSTS = &core.HSTSInfo{
			Enabled:           true,
			IncludeSubdomains: strings.Contains(hsts, "includeSubDomains"),
			Preload:           strings.Contains(hsts, "preload"),
		}

		// 提取max-age
		if matches := regexp.MustCompile(`max-age=(\d+)`).FindStringSubmatch(hsts); len(matches) > 1 {
			if maxAge, err := strconv.Atoi(matches[1]); err == nil {
				secHeaders.HSTS.MaxAge = maxAge
			}
		}
	}

	// CSP分析
	if csp, exists := headers["Content-Security-Policy"]; exists {
		secHeaders.CSP = &core.CSPInfo{
			Enabled:    true,
			Policy:     csp,
			Directives: strings.Split(csp, ";"),
			ReportOnly: false,
		}
	}

	// 其他安全头
	secHeaders.XFrameOptions = headers["X-Frame-Options"]
	secHeaders.XContentTypeOptions = headers["X-Content-Type-Options"]
	secHeaders.XSSProtection = headers["X-XSS-Protection"]
	secHeaders.ReferrerPolicy = headers["Referrer-Policy"]
	secHeaders.PermissionsPolicy = headers["Permissions-Policy"]
	secHeaders.ExpectCT = headers["Expect-CT"]

	return secHeaders
}

// 工厂函数

// NewComponentDetector 创建组件检测器
func NewComponentDetector() *ComponentDetector {
	return &ComponentDetector{
		patterns: make(map[string]*ComponentPattern),
	}
}

// NewServiceDetector 创建服务检测器
func NewServiceDetector() *ServiceDetector {
	return &ServiceDetector{
		signatures: make(map[int]*ServiceSignature),
	}
}

// NewFingerprintEngine 创建指纹识别引擎
func NewFingerprintEngine() *FingerprintEngine {
	return &FingerprintEngine{
		webFingerprints:     make([]*WebFingerprint, 0),
		serviceFingerprints: make([]*ServiceFingerprint, 0),
	}
}
