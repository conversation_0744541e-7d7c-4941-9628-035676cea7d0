package main

import (
	"fmt"
	"log"
	"strings"

	"scanner/internal/scanner/core"
)

// 测试规则引擎功能
func main() {
	fmt.Println("🔧 规则引擎测试")
	fmt.Println(strings.Repeat("=", 50))
	
	// 创建规则引擎
	ruleEngine := core.NewRuleEngine("rules")
	
	// 初始化规则引擎
	fmt.Println("📋 初始化规则引擎...")
	if err := ruleEngine.Initialize(); err != nil {
		log.Fatalf("规则引擎初始化失败: %v", err)
	}
	fmt.Println("✅ 规则引擎初始化成功")
	
	// 列出所有规则
	fmt.Println("\n📊 已加载的规则:")
	rules := ruleEngine.ListRules()
	for i, rule := range rules {
		fmt.Printf("%d. %s (%s) - %s\n", i+1, rule.Name, rule.ID, rule.Description)
		fmt.Printf("   分类: %s, 严重程度: %s, 启用: %t\n", rule.Category, rule.Severity, rule.Enabled)
		fmt.Printf("   条件数: %d, 动作数: %d\n", len(rule.Conditions), len(rule.Actions))
		fmt.Printf("   标签: %v\n", rule.Tags)
		fmt.Println()
	}
	
	// 列出所有规则组
	fmt.Println("📊 已加载的规则组:")
	groups := ruleEngine.ListRuleGroups()
	for i, group := range groups {
		fmt.Printf("%d. %s (%s) - %s\n", i+1, group.Name, group.ID, group.Description)
		fmt.Printf("   包含规则: %v, 启用: %t\n", group.Rules, group.Enabled)
		fmt.Println()
	}
	
	// 测试规则执行
	fmt.Println("🧪 测试规则执行...")
	testRuleExecution(ruleEngine)
	
	fmt.Println("\n🎉 规则引擎测试完成!")
}

// testRuleExecution 测试规则执行
func testRuleExecution(ruleEngine *core.RuleEngine) {
	// 创建测试上下文
	ctx := &core.RuleExecutionContext{
		Target: &core.ScanTarget{
			Type:  core.TargetTypeURL,
			Value: "http://example.com/test?id=1",
		},
		Response: map[string]interface{}{
			"status_code": 200,
			"body":        "mysql_fetch_array() error: Invalid query",
			"headers": map[string]string{
				"content-type": "text/html",
				"server":       "Apache/2.4.41",
			},
		},
		Variables: make(map[string]interface{}),
		Results:   make([]*core.RuleResult, 0),
	}
	
	// 测试SQL注入规则
	fmt.Println("\n🔍 测试SQL注入规则...")
	sqlRuleResults, err := ruleEngine.ExecuteRules(ctx, []string{"sql_injection_basic"})
	if err != nil {
		fmt.Printf("❌ SQL注入规则执行失败: %v\n", err)
	} else {
		for _, result := range sqlRuleResults {
			fmt.Printf("规则 %s: 匹配=%t, 置信度=%.2f\n", result.RuleID, result.Matched, result.Confidence)
			if result.Matched {
				fmt.Printf("  证据: %v\n", result.Evidence)
				fmt.Printf("  变量: %v\n", result.Variables)
			}
			if result.Error != "" {
				fmt.Printf("  错误: %s\n", result.Error)
			}
		}
	}
	
	// 测试XSS规则
	fmt.Println("\n🔍 测试XSS规则...")
	ctx.Response = map[string]interface{}{
		"status_code": 200,
		"body":        "<html><body><script>alert('XSS')</script></body></html>",
		"headers": map[string]string{
			"content-type": "text/html",
		},
	}
	
	xssRuleResults, err := ruleEngine.ExecuteRules(ctx, []string{"xss_reflected"})
	if err != nil {
		fmt.Printf("❌ XSS规则执行失败: %v\n", err)
	} else {
		for _, result := range xssRuleResults {
			fmt.Printf("规则 %s: 匹配=%t, 置信度=%.2f\n", result.RuleID, result.Matched, result.Confidence)
			if result.Matched {
				fmt.Printf("  证据: %v\n", result.Evidence)
			}
		}
	}
	
	// 测试目录遍历规则
	fmt.Println("\n🔍 测试目录遍历规则...")
	ctx.Target.Value = "http://example.com/file?path=../../../etc/passwd"
	ctx.Response = map[string]interface{}{
		"status_code": 200,
		"body":        "root:x:0:0:root:/root:/bin/bash\ndaemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin",
		"headers": map[string]string{
			"content-type": "text/plain",
		},
	}
	
	dirRuleResults, err := ruleEngine.ExecuteRules(ctx, []string{"directory_traversal"})
	if err != nil {
		fmt.Printf("❌ 目录遍历规则执行失败: %v\n", err)
	} else {
		for _, result := range dirRuleResults {
			fmt.Printf("规则 %s: 匹配=%t, 置信度=%.2f\n", result.RuleID, result.Matched, result.Confidence)
			if result.Matched {
				fmt.Printf("  证据: %v\n", result.Evidence)
			}
		}
	}
	
	// 测试规则组执行
	fmt.Println("\n🔍 测试规则组执行...")
	group, err := ruleEngine.GetRuleGroup("web_vulnerabilities")
	if err != nil {
		fmt.Printf("❌ 获取规则组失败: %v\n", err)
	} else {
		fmt.Printf("执行规则组: %s (包含 %d 个规则)\n", group.Name, len(group.Rules))
		
		groupResults, err := ruleEngine.ExecuteRules(ctx, group.Rules)
		if err != nil {
			fmt.Printf("❌ 规则组执行失败: %v\n", err)
		} else {
			matchedCount := 0
			for _, result := range groupResults {
				if result.Matched {
					matchedCount++
				}
			}
			fmt.Printf("规则组执行完成: 总规则数=%d, 匹配规则数=%d\n", len(groupResults), matchedCount)
		}
	}
}
