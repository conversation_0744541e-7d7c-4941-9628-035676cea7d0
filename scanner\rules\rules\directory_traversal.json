{"id": "directory_traversal", "name": "目录遍历检测", "description": "检测目录遍历/路径穿越漏洞", "category": "path_traversal", "severity": "high", "conditions": [{"type": "response_analysis", "field": "response.body", "operator": "regex", "regex": "(?i)(root:.*:0:0:|# Copyright \\(c\\) 1993-2009 Microsoft Corp\\.|localhost)", "case_sensitive": false, "metadata": {"description": "检测系统文件内容"}}, {"type": "request_analysis", "field": "request.url", "operator": "contains", "value": "../", "case_sensitive": false, "metadata": {"description": "检测路径穿越载荷"}}], "actions": [{"type": "create_vulnerability", "target": "directory_traversal", "parameters": {"name": "目录遍历漏洞", "description": "检测到目录遍历漏洞，攻击者可能能够访问系统文件", "impact": "可能导致敏感文件泄露、配置信息暴露", "solution": "验证和过滤文件路径，使用白名单限制可访问文件", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"]}}], "tags": ["directory-traversal", "path-traversal", "file-access"], "references": ["https://owasp.org/www-community/attacks/Path_Traversal", "https://portswigger.net/web-security/file-path-traversal"], "author": "VulnScanner Team", "version": "1.0.0", "enabled": true, "config": {"payloads": ["../../../etc/passwd", "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts", "/etc/passwd", "C:\\windows\\system32\\drivers\\etc\\hosts", "....//....//....//etc/passwd"]}}