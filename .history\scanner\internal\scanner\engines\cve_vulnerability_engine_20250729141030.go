package engines

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"scanner/internal/scanner/core"
)

// CVEVulnerabilityEngine CVE漏洞扫描引擎
// 负责基于CVE数据库的历史漏洞检测
type CVEVulnerabilityEngine struct {
	*core.BaseEngine
	
	// HTTP客户端
	httpClient *http.Client
	
	// 配置
	config *CVEVulnConfig
	
	// CVE数据库
	cveDatabase *CVEDatabase
	
	// 检测器
	cveDetector *CVEDetector
	
	// 规则引擎
	ruleEngine *core.RuleEngine
}

// CVEVulnConfig CVE漏洞扫描配置
type CVEVulnConfig struct {
	// 基础配置
	CVEDatabasePath    string        `json:"cve_database_path"`
	EnabledYears       []int         `json:"enabled_years"`
	SeverityFilter     []string      `json:"severity_filter"`
	CategoryFilter     []string      `json:"category_filter"`
	
	// 扫描配置
	ScanTimeout        time.Duration `json:"scan_timeout"`
	RequestTimeout     time.Duration `json:"request_timeout"`
	MaxConcurrency     int           `json:"max_concurrency"`
	ScanDelay          time.Duration `json:"scan_delay"`
	
	// 检测配置
	EnableFingerprint  bool          `json:"enable_fingerprint"`
	EnableVersionCheck bool          `json:"enable_version_check"`
	EnablePayloadTest  bool          `json:"enable_payload_test"`
	EnableHistoryCheck bool          `json:"enable_history_check"`
	
	// 高级配置
	UserAgent          string        `json:"user_agent"`
	MaxRetries         int           `json:"max_retries"`
	ConfidenceThreshold float64      `json:"confidence_threshold"`
}

// CVEDatabase CVE数据库
type CVEDatabase struct {
	cveRules    map[string]*CVERule
	yearIndex   map[int][]*CVERule
	categoryIndex map[string][]*CVERule
	severityIndex map[string][]*CVERule
	mutex       sync.RWMutex
	loaded      bool
}

// CVERule CVE规则
type CVERule struct {
	ID              string                 `json:"id"`
	Year            int                    `json:"year"`
	Description     string                 `json:"description"`
	CVSS            float64                `json:"cvss"`
	Severity        string                 `json:"severity"`
	Vector          string                 `json:"vector"`
	Category        string                 `json:"category"`
	CWE             []string               `json:"cwe"`
	Framework       []string               `json:"framework"`
	Component       []string               `json:"component"`
	POCAvailable    bool                   `json:"poc_available"`
	POCPath         string                 `json:"poc_path"`
	ExploitCode     string                 `json:"exploit_code"`
	DetectionRules  []string               `json:"detection_rules"`
	Payloads        []string               `json:"payloads"`
	Verification    *CVEVerification       `json:"verification"`
	PublishedDate   time.Time              `json:"published_date"`
	ModifiedDate    time.Time              `json:"modified_date"`
	References      []string               `json:"references"`
	Tags            []string               `json:"tags"`
	ExploitCount    int                    `json:"exploit_count"`
	SuccessRate     float64                `json:"success_rate"`
	LastUsed        time.Time              `json:"last_used"`
}

// CVEVerification CVE验证配置
type CVEVerification struct {
	Method           string   `json:"method"`
	ExpectedResults  []string `json:"expected_results"`
	VerificationSteps []string `json:"verification_steps"`
	Confidence       float64  `json:"confidence"`
}

// CVEDetector CVE检测器
type CVEDetector struct {
	config     *CVEVulnConfig
	httpClient *http.Client
	database   *CVEDatabase
}

// CVEDetectionResult CVE检测结果
type CVEDetectionResult struct {
	CVERule     *CVERule  `json:"cve_rule"`
	Matched     bool      `json:"matched"`
	Confidence  float64   `json:"confidence"`
	Evidence    []string  `json:"evidence"`
	Response    string    `json:"response"`
	Timestamp   time.Time `json:"timestamp"`
}

// NewCVEVulnerabilityEngine 创建CVE漏洞扫描引擎
func NewCVEVulnerabilityEngine() *CVEVulnerabilityEngine {
	baseEngine := core.NewBaseEngine(
		"CVE漏洞扫描引擎",
		core.EngineTypeCVEVulnerability,
		"1.0.0",
		"负责基于CVE数据库的历史漏洞检测，支持从2000年至今的CVE漏洞扫描",
	)
	
	// 创建HTTP客户端
	httpClient := &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,
		},
	}
	
	config := &CVEVulnConfig{
		CVEDatabasePath:     "rules/cve",
		EnabledYears:        generateYearRange(2000, 2025), // 支持2000-2025年的CVE
		SeverityFilter:      []string{"critical", "high", "medium", "low"},
		CategoryFilter:      []string{}, // 空表示所有类别
		ScanTimeout:         10 * time.Minute,
		RequestTimeout:      30 * time.Second,
		MaxConcurrency:      10,
		ScanDelay:           100 * time.Millisecond,
		EnableFingerprint:   true,
		EnableVersionCheck:  true,
		EnablePayloadTest:   true,
		EnableHistoryCheck:  true,
		UserAgent:           "CVE-Scanner/1.0",
		MaxRetries:          3,
		ConfidenceThreshold: 0.7,
	}
	
	// 创建CVE数据库
	cveDatabase := NewCVEDatabase()
	
	// 创建规则引擎
	ruleEngine := core.NewRuleEngine("rules")
	
	engine := &CVEVulnerabilityEngine{
		BaseEngine:  baseEngine,
		httpClient:  httpClient,
		config:      config,
		cveDatabase: cveDatabase,
		cveDetector: NewCVEDetector(config, httpClient, cveDatabase),
		ruleEngine:  ruleEngine,
	}
	
	return engine
}

// generateYearRange 生成年份范围
func generateYearRange(start, end int) []int {
	years := make([]int, 0, end-start+1)
	for year := start; year <= end; year++ {
		years = append(years, year)
	}
	return years
}

// GetSupportedTargets 获取支持的目标类型
func (e *CVEVulnerabilityEngine) GetSupportedTargets() []core.TargetType {
	return []core.TargetType{
		core.TargetTypeURL,
		core.TargetTypeIP,
		core.TargetTypeDomain,
		core.TargetTypeHost,
	}
}

// CanScan 检查是否能扫描指定目标
func (e *CVEVulnerabilityEngine) CanScan(target *core.ScanTarget) bool {
	if !e.IsEnabled() {
		return false
	}
	
	supportedTargets := e.GetSupportedTargets()
	for _, supportedType := range supportedTargets {
		if target.Type == supportedType {
			return true
		}
	}
	return false
}

// Initialize 初始化引擎
func (e *CVEVulnerabilityEngine) Initialize() error {
	if err := e.BaseEngine.Initialize(); err != nil {
		return err
	}
	
	// 加载CVE数据库
	if err := e.cveDatabase.LoadCVEDatabase(e.config.CVEDatabasePath); err != nil {
		return fmt.Errorf("加载CVE数据库失败: %v", err)
	}
	
	// 初始化规则引擎
	if err := e.ruleEngine.Initialize(); err != nil {
		e.LogWarn("规则引擎初始化失败: %v", err)
	}
	
	e.LogInfo("CVE漏洞扫描引擎初始化完成，加载了 %d 个CVE规则", e.cveDatabase.GetCVECount())
	return nil
}

// Scan 执行CVE漏洞扫描
func (e *CVEVulnerabilityEngine) Scan(ctx context.Context, request *core.ScanRequest) (*core.ScanResult, error) {
	// 创建任务上下文
	task, taskCtx := e.CreateTaskContext(request)
	defer func() {
		if task.CancelFunc != nil {
			task.CancelFunc()
		}
	}()
	
	e.LogInfo("开始CVE漏洞扫描: %s", request.Target.Value)
	
	// 创建扫描结果
	result := &core.ScanResult{
		ID:              fmt.Sprintf("cve_%s_%d", request.ID, time.Now().Unix()),
		TaskID:          request.Config.TaskID,
		EngineType:      e.GetType(),
		Target:          request.Target,
		Status:          core.StatusRunning,
		Progress:        0.0,
		StartedAt:       time.Now(),
		Vulnerabilities: make([]*core.Vulnerability, 0),
		Statistics:      &core.ScanStatistics{},
		Warnings:        make([]string, 0),
		Metadata:        make(map[string]interface{}),
	}
	
	// 执行扫描步骤
	steps := []struct {
		name     string
		progress float64
		function func(context.Context, *core.ScanRequest, *core.ScanResult) error
	}{
		{"目标分析", 10, e.analyzeTarget},
		{"指纹识别", 25, e.fingerprintTarget},
		{"版本检测", 40, e.detectVersions},
		{"CVE匹配", 60, e.matchCVEs},
		{"载荷测试", 80, e.testPayloads},
		{"历史检查", 90, e.checkHistory},
		{"结果分析", 100, e.analyzeCVEResults},
	}
	
	for _, step := range steps {
		select {
		case <-taskCtx.Done():
			result.Status = core.StatusCancelled
			e.CompleteTask(request.ID, result, fmt.Errorf("扫描被取消"))
			return result, fmt.Errorf("扫描被取消")
		default:
		}
		
		e.LogInfo("执行步骤: %s", step.name)
		e.UpdateTaskProgress(request.ID, step.progress, step.name)
		
		if err := step.function(taskCtx, request, result); err != nil {
			e.LogError("步骤执行失败 %s: %v", step.name, err)
			result.Warnings = append(result.Warnings, fmt.Sprintf("步骤 %s 执行失败: %v", step.name, err))
		}
		
		// 添加扫描延迟
		time.Sleep(e.config.ScanDelay)
	}
	
	// 完成扫描
	result.Status = core.StatusCompleted
	result.Progress = 100.0
	completedAt := time.Now()
	result.CompletedAt = &completedAt
	result.Duration = time.Since(result.StartedAt)
	
	// 更新统计信息
	e.updateCVEStatistics(result)
	
	e.LogInfo("CVE漏洞扫描完成: %s，发现 %d 个CVE漏洞", request.Target.Value, len(result.Vulnerabilities))
	e.CompleteTask(request.ID, result, nil)
	
	return result, nil
}

// analyzeTarget 分析目标
func (e *CVEVulnerabilityEngine) analyzeTarget(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	target := request.Target
	
	// 记录目标信息
	result.Metadata["target_type"] = target.Type
	result.Metadata["target_value"] = target.Value
	result.Metadata["scan_start_time"] = time.Now()
	
	// 根据目标类型进行不同的分析
	switch target.Type {
	case core.TargetTypeURL:
		// URL目标分析
		if err := e.analyzeURLTarget(ctx, target, result); err != nil {
			return err
		}
		
	case core.TargetTypeIP, core.TargetTypeDomain, core.TargetTypeHost:
		// 网络目标分析
		if err := e.analyzeNetworkTarget(ctx, target, result); err != nil {
			return err
		}
		
	default:
		return fmt.Errorf("不支持的目标类型: %s", target.Type)
	}
	
	e.LogInfo("目标分析完成: %s", target.Value)
	return nil
}

// analyzeURLTarget 分析URL目标
func (e *CVEVulnerabilityEngine) analyzeURLTarget(ctx context.Context, target *core.ScanTarget, result *core.ScanResult) error {
	// 发送HTTP请求获取基础信息
	req, err := http.NewRequestWithContext(ctx, "GET", target.Value, nil)
	if err != nil {
		return err
	}
	req.Header.Set("User-Agent", e.config.UserAgent)
	
	resp, err := e.httpClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	// 读取响应内容
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	
	// 保存响应信息
	result.Metadata["http_status"] = resp.StatusCode
	result.Metadata["http_headers"] = e.convertHeaders(resp.Header)
	result.Metadata["http_body"] = string(body)
	result.Metadata["content_length"] = len(body)
	
	return nil
}

// analyzeNetworkTarget 分析网络目标
func (e *CVEVulnerabilityEngine) analyzeNetworkTarget(ctx context.Context, target *core.ScanTarget, result *core.ScanResult) error {
	// 对于网络目标，尝试HTTP和HTTPS连接
	schemes := []string{"http", "https"}
	ports := []string{"80", "443", "8080", "8443"}
	
	for _, scheme := range schemes {
		for _, port := range ports {
			url := fmt.Sprintf("%s://%s:%s", scheme, target.Value, port)
			
			req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
			if err != nil {
				continue
			}
			req.Header.Set("User-Agent", e.config.UserAgent)
			
			resp, err := e.httpClient.Do(req)
			if err != nil {
				continue
			}
			resp.Body.Close()
			
			// 记录成功的连接
			if result.Metadata["accessible_urls"] == nil {
				result.Metadata["accessible_urls"] = make([]string, 0)
			}
			urls := result.Metadata["accessible_urls"].([]string)
			result.Metadata["accessible_urls"] = append(urls, url)
			
			break // 找到一个可访问的URL就够了
		}
	}
	
	return nil
}

// convertHeaders 转换HTTP头格式
func (e *CVEVulnerabilityEngine) convertHeaders(headers http.Header) map[string]string {
	result := make(map[string]string)
	for name, values := range headers {
		if len(values) > 0 {
			result[strings.ToLower(name)] = values[0]
		}
	}
	return result
}
