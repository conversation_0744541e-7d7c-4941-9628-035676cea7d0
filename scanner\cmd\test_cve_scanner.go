package main

import (
	"fmt"
	"log"
	"strings"
	"time"

	"scanner/internal/scanner/core"
	"scanner/internal/scanner/engines"
)

// 测试CVE漏洞扫描引擎
func main() {
	fmt.Println("🔍 CVE漏洞扫描引擎测试")
	fmt.Println(strings.Repeat("=", 50))
	
	// 创建引擎管理器
	manager := core.NewEngineManager()
	
	// 注册CVE漏洞扫描引擎
	fmt.Println("📋 注册CVE漏洞扫描引擎...")
	cveEngine := engines.NewCVEVulnerabilityEngine()
	if err := manager.RegisterEngine(cveEngine); err != nil {
		log.Fatalf("注册CVE漏洞扫描引擎失败: %v", err)
	}
	fmt.Println("✅ CVE漏洞扫描引擎注册成功")
	
	// 初始化管理器
	fmt.Println("\n🔧 初始化引擎管理器...")
	if err := manager.Initialize(); err != nil {
		log.Fatalf("引擎管理器初始化失败: %v", err)
	}
	fmt.Println("✅ 引擎管理器初始化成功")
	
	// 显示引擎信息
	fmt.Println("\n📊 已注册的扫描引擎:")
	engines := manager.ListEngines()
	for i, engine := range engines {
		fmt.Printf("%d. %s (%s) - %s\n", i+1, engine.Name, engine.Type, engine.Description)
		fmt.Printf("   版本: %s, 状态: %s, 启用: %t\n", engine.Version, engine.Status, engine.Enabled)
		fmt.Println()
	}
	
	// 测试CVE扫描
	testCVEScanning(manager)
	
	// 关闭管理器
	fmt.Println("\n🔒 关闭引擎管理器...")
	if err := manager.Shutdown(); err != nil {
		log.Printf("关闭引擎管理器失败: %v", err)
	} else {
		fmt.Println("✅ 引擎管理器已关闭")
	}
	
	fmt.Println("\n🎉 CVE漏洞扫描引擎测试完成!")
}

// testCVEScanning 测试CVE扫描
func testCVEScanning(manager *core.EngineManager) {
	// 创建扫描配置
	config := &core.ScanConfig{
		TaskID:        "cve_test_task",
		ScanMode:      core.ScanModeStandard,
		Timeout:       3 * time.Minute,
		EnabledChecks: []string{},
		CustomOptions: make(map[string]interface{}),
		CreatedAt:     time.Now(),
	}
	
	// 测试URL目标扫描
	fmt.Println("\n🔍 测试URL目标CVE扫描...")
	testURLCVEScanning(manager, config)
	
	// 测试域名目标扫描
	fmt.Println("\n🔍 测试域名目标CVE扫描...")
	testDomainCVEScanning(manager, config)
}

// testURLCVEScanning 测试URL目标CVE扫描
func testURLCVEScanning(manager *core.EngineManager, config *core.ScanConfig) {
	// 创建URL目标
	target := &core.ScanTarget{
		ID:          "url_cve_target",
		Type:        core.TargetTypeURL,
		Value:       "http://httpbin.org",
		Description: "URL目标CVE扫描测试",
		Metadata:    make(map[string]string),
		CreatedAt:   time.Now(),
	}
	
	// 创建扫描请求
	request := &core.ScanRequest{
		ID:        "url_cve_scan_" + fmt.Sprintf("%d", time.Now().Unix()),
		Target:    target,
		Config:    config,
		Priority:  1,
		CreatedAt: time.Now(),
	}
	
	// 提交扫描任务
	taskInfo, err := manager.SubmitScanRequest(request)
	if err != nil {
		fmt.Printf("❌ 提交URL CVE扫描任务失败: %v\n", err)
		return
	}
	
	fmt.Printf("✅ URL CVE扫描任务已提交: %s\n", taskInfo.ID)
	
	// 监控任务进度
	monitorCVETask(manager, taskInfo.ID, "URL CVE扫描")
}

// testDomainCVEScanning 测试域名目标CVE扫描
func testDomainCVEScanning(manager *core.EngineManager, config *core.ScanConfig) {
	// 创建域名目标
	target := &core.ScanTarget{
		ID:          "domain_cve_target",
		Type:        core.TargetTypeDomain,
		Value:       "httpbin.org",
		Description: "域名目标CVE扫描测试",
		Metadata:    make(map[string]string),
		CreatedAt:   time.Now(),
	}
	
	// 创建扫描请求
	request := &core.ScanRequest{
		ID:        "domain_cve_scan_" + fmt.Sprintf("%d", time.Now().Unix()),
		Target:    target,
		Config:    config,
		Priority:  1,
		CreatedAt: time.Now(),
	}
	
	// 提交扫描任务
	taskInfo, err := manager.SubmitScanRequest(request)
	if err != nil {
		fmt.Printf("❌ 提交域名CVE扫描任务失败: %v\n", err)
		return
	}
	
	fmt.Printf("✅ 域名CVE扫描任务已提交: %s\n", taskInfo.ID)
	
	// 监控任务进度
	monitorCVETask(manager, taskInfo.ID, "域名CVE扫描")
}

// monitorCVETask 监控CVE扫描任务
func monitorCVETask(manager *core.EngineManager, taskID, taskName string) {
	fmt.Printf("📊 监控%s任务进度: %s\n", taskName, taskID)
	
	maxWaitTime := 3 * time.Minute
	checkInterval := 3 * time.Second
	startTime := time.Now()
	
	for {
		// 检查超时
		if time.Since(startTime) > maxWaitTime {
			fmt.Printf("⏰ %s任务监控超时\n", taskName)
			break
		}
		
		// 获取任务状态
		status, err := manager.GetTaskStatus(taskID)
		if err != nil {
			fmt.Printf("❌ 获取任务状态失败: %v\n", err)
			break
		}
		
		fmt.Printf("   状态: %s, 进度: %.1f%%\n", status.Status, status.Progress)
		
		// 检查任务是否完成
		if status.Status == core.StatusCompleted || status.Status == core.StatusFailed || status.Status == core.StatusCancelled {
			fmt.Printf("✅ %s任务完成!\n", taskName)
			
			// 显示扫描结果摘要
			if result := status.Result; result != nil {
				displayCVEScanResult(result, taskName)
			}
			break
		}
		
		time.Sleep(checkInterval)
	}
}

// displayCVEScanResult 显示CVE扫描结果
func displayCVEScanResult(result *core.ScanResult, taskName string) {
	fmt.Printf("\n📋 %s结果摘要:\n", taskName)
	fmt.Printf("   任务ID: %s\n", result.TaskID)
	fmt.Printf("   引擎类型: %s\n", result.EngineType)
	fmt.Printf("   扫描目标: %s\n", result.Target.Value)
	fmt.Printf("   开始时间: %s\n", result.StartedAt.Format("2006-01-02 15:04:05"))
	if result.CompletedAt != nil {
		fmt.Printf("   完成时间: %s\n", result.CompletedAt.Format("2006-01-02 15:04:05"))
	}
	fmt.Printf("   扫描耗时: %s\n", result.Duration)
	fmt.Printf("   发现CVE漏洞: %d个\n", len(result.Vulnerabilities))
	
	// 显示发现的CVE漏洞
	if len(result.Vulnerabilities) > 0 {
		fmt.Printf("\n🔍 发现的CVE漏洞:\n")
		for i, vuln := range result.Vulnerabilities {
			fmt.Printf("   %d. %s (%s)\n", i+1, vuln.Name, vuln.Severity)
			fmt.Printf("      CVE: %s\n", vuln.CVE)
			fmt.Printf("      CVSS: %.1f\n", vuln.CVSS)
			fmt.Printf("      描述: %s\n", vuln.Description)
			fmt.Printf("      置信度: %.2f\n", vuln.Confidence)
			if len(vuln.Tags) > 0 {
				fmt.Printf("      标签: %v\n", vuln.Tags)
			}
			fmt.Println()
		}
	}
	
	// 显示扫描统计
	if result.Statistics != nil {
		fmt.Printf("📊 扫描统计:\n")
		fmt.Printf("   总目标数: %d\n", result.Statistics.TotalTargets)
		fmt.Printf("   已扫描目标: %d\n", result.Statistics.ScannedTargets)
		fmt.Printf("   发送请求数: %d\n", result.Statistics.RequestsSent)
		fmt.Printf("   接收响应数: %d\n", result.Statistics.ResponsesReceived)
		if result.Statistics.TotalVulns > 0 {
			fmt.Printf("   CVE漏洞统计: 严重=%d, 高危=%d, 中危=%d, 低危=%d, 信息=%d\n",
				result.Statistics.CriticalVulns,
				result.Statistics.HighVulns,
				result.Statistics.MediumVulns,
				result.Statistics.LowVulns,
				result.Statistics.InfoVulns)
		}
	}
	
	// 显示扫描元数据
	if len(result.Metadata) > 0 {
		fmt.Printf("\n📋 扫描元数据:\n")
		for key, value := range result.Metadata {
			switch key {
			case "target_type":
				fmt.Printf("   目标类型: %v\n", value)
			case "detected_versions":
				if versions, ok := value.(map[string]string); ok && len(versions) > 0 {
					fmt.Printf("   检测到的版本:\n")
					for component, version := range versions {
						fmt.Printf("     - %s: %s\n", component, version)
					}
				}
			case "matched_cves":
				if cves, ok := value.([]interface{}); ok {
					fmt.Printf("   匹配的CVE数量: %d\n", len(cves))
				}
			case "tested_cves":
				if cves, ok := value.([]interface{}); ok {
					fmt.Printf("   测试的CVE数量: %d\n", len(cves))
				}
			case "scan_summary":
				if summary, ok := value.(map[string]interface{}); ok {
					fmt.Printf("   扫描摘要:\n")
					for k, v := range summary {
						fmt.Printf("     %s: %v\n", k, v)
					}
				}
			}
		}
	}
	
	// 显示警告信息
	if len(result.Warnings) > 0 {
		fmt.Printf("\n⚠️  警告信息:\n")
		for _, warning := range result.Warnings {
			fmt.Printf("   - %s\n", warning)
		}
	}
}
