{"id": "mysql_weak_password", "name": "MySQL弱密码检测", "description": "检测MySQL服务是否使用弱密码或默认密码", "category": "weak_password", "severity": "medium", "conditions": [{"type": "service_analysis", "field": "service.name", "operator": "equals", "value": "mysql", "case_sensitive": false, "metadata": {"description": "检测MySQL服务"}}, {"type": "service_analysis", "field": "service.port", "operator": "equals", "value": 3306, "metadata": {"description": "检测MySQL默认端口"}}, {"type": "network_test", "field": "mysql.handshake", "operator": "contains", "value": "mysql", "case_sensitive": false, "metadata": {"description": "检测MySQL握手包"}}], "actions": [{"type": "create_vulnerability", "target": "weak_password", "parameters": {"name": "MySQL弱密码漏洞", "description": "MySQL服务可能使用弱密码或默认密码", "impact": "攻击者可通过暴力破解获取数据库访问权限，导致数据泄露", "solution": "使用强密码，限制数据库访问IP，启用SSL连接", "references": ["https://dev.mysql.com/doc/refman/8.0/en/password-security.html", "https://dev.mysql.com/doc/refman/8.0/en/security-guidelines.html"]}}], "tags": ["mysql", "weak-password", "database", "authentication"], "references": ["https://dev.mysql.com/doc/refman/8.0/en/password-security.html", "https://dev.mysql.com/doc/refman/8.0/en/security-guidelines.html", "https://attack.mitre.org/techniques/T1110/"], "author": "VulnScanner Team", "version": "1.0.0", "enabled": true, "config": {"timeout": 5, "weak_passwords": ["", "root", "admin", "123456", "password", "mysql"], "test_users": ["root", "admin", "mysql"], "max_attempts": 3}}