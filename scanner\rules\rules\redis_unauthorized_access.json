{"id": "redis_unauthorized_access", "name": "Redis未授权访问检测", "description": "检测Redis服务是否存在未授权访问漏洞", "category": "unauthorized_access", "severity": "high", "conditions": [{"type": "service_analysis", "field": "service.name", "operator": "equals", "value": "redis", "case_sensitive": false, "metadata": {"description": "检测Redis服务"}}, {"type": "service_analysis", "field": "service.port", "operator": "equals", "value": 6379, "metadata": {"description": "检测Redis默认端口"}}, {"type": "network_test", "field": "redis.info_command", "operator": "contains", "value": "redis_version", "case_sensitive": false, "metadata": {"description": "测试Redis INFO命令是否可执行"}}], "actions": [{"type": "create_vulnerability", "target": "unauthorized_access", "parameters": {"name": "Redis未授权访问漏洞", "description": "Redis服务未配置认证，存在未授权访问风险", "impact": "攻击者可通过Redis命令执行任意操作，包括数据读取、写入、删除等", "solution": "配置Redis认证密码，限制访问IP，禁用危险命令", "references": ["https://redis.io/topics/security", "https://www.cvedetails.com/cve/CVE-2015-8080/"]}}, {"type": "log", "parameters": {"message": "检测到Redis未授权访问漏洞"}}], "tags": ["redis", "unauthorized-access", "database", "nosql"], "references": ["https://redis.io/topics/security", "https://www.cvedetails.com/cve/CVE-2015-8080/", "https://github.com/vulhub/vulhub/tree/master/redis/CVE-2015-8080"], "author": "VulnScanner Team", "version": "1.0.0", "enabled": true, "config": {"timeout": 10, "test_commands": ["INFO", "CONFIG GET *"], "confidence_threshold": 0.9}}