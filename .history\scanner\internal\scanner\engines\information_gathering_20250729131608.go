package engines

import (
	"context"
	"crypto/tls"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"sync"
	"time"

	"scanner/internal/scanner/types"
	"scanner/pkg/logger"
)

// InformationGatheringEngine 信息收集引擎
type InformationGatheringEngine struct {
	client     *http.Client
	timeout    time.Duration
	userAgent  string
	maxRetries int
}

// NewInformationGatheringEngine 创建信息收集引擎
func NewInformationGatheringEngine() *InformationGatheringEngine {
	return &InformationGatheringEngine{
		client: &http.Client{
			Timeout: 30 * time.Second,
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: true,
				},
				DisableKeepAlives: true,
			},
		},
		timeout:    30 * time.Second,
		userAgent:  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
		maxRetries: 3,
	}
}

// GatherInformation 执行完整信息收集 - 严格按照需求文档进行全面信息收集
func (e *InformationGatheringEngine) GatherInformation(ctx context.Context, target string, progress chan<- *types.ScanProgress) (*types.InformationGatheringResult, error) {
	result := &types.InformationGatheringResult{
		PortServices: make([]*types.PortService, 0),
		Statistics:   &types.GatheringStatistics{},
	}

	startTime := time.Now()

	// 阶段1: 目标信息收集 - 域名解析、IP地址、DNS信息 (0-15%)
	e.sendProgress(progress, "信息收集", 5, "收集目标基础信息：域名解析、IP地址、DNS信息")
	targetInfo, err := e.collectEnhancedTargetInformation(ctx, target)
	if err != nil {
		return nil, fmt.Errorf("目标信息收集失败: %v", err)
	}
	result.TargetInfo = targetInfo

	// 阶段2: 端口服务扫描 - 开放端口、运行服务、版本识别 (15-30%)
	e.sendProgress(progress, "信息收集", 20, "端口服务扫描：开放端口、运行服务、版本识别")
	portServices, err := e.scanEnhancedPortsAndServices(ctx, targetInfo.IP)
	if err != nil {
		return nil, fmt.Errorf("端口服务扫描失败: %v", err)
	}
	result.PortServices = portServices

	// 阶段3: Web组件识别 - 技术栈、框架、CMS、JavaScript库 (30-45%)
	e.sendProgress(progress, "信息收集", 35, "Web组件识别：技术栈、框架、CMS、JavaScript库")
	webComponents, err := e.identifyEnhancedWebComponents(ctx, target)
	if err != nil {
		return nil, fmt.Errorf("Web组件识别失败: %v", err)
	}
	result.WebComponents = webComponents

	// 阶段4: 系统信息收集 - 操作系统、硬件信息、网络配置 (45-60%)
	e.sendProgress(progress, "信息收集", 50, "系统信息收集：操作系统、硬件信息、网络配置")
	systemInfo, err := e.collectEnhancedSystemInformation(ctx, target)
	if err != nil {
		return nil, fmt.Errorf("系统信息收集失败: %v", err)
	}
	result.SystemInfo = systemInfo

	// 阶段5: 敏感信息发现 - 配置文件、备份文件、源码泄露 (60-75%)
	e.sendProgress(progress, "信息收集", 65, "敏感信息发现：配置文件、备份文件、源码泄露")
	sensitiveInfo, err := e.discoverSensitiveInformation(ctx, target)
	if err != nil {
		logger.Warnf("敏感信息发现失败: %v", err)
		// 敏感信息发现失败不影响整体流程
	}
	result.SensitiveInfo = sensitiveInfo

	// 阶段6: 防护措施探测 - WAF识别、主机防护策略、网络防护策略 (75-90%)
	e.sendProgress(progress, "信息收集", 80, "防护措施探测：WAF识别、主机防护策略、网络防护策略")
	protectionInfo, err := e.detectProtectionMeasures(ctx, target)
	if err != nil {
		logger.Warnf("防护措施探测失败: %v", err)
		// 防护措施探测失败不影响整体流程
	}
	result.ProtectionInfo = protectionInfo

	// 阶段7: 指纹识别 - HTTP头分析、页面内容分析、错误页面分析、Cookie分析 (90-100%)
	e.sendProgress(progress, "信息收集", 95, "指纹识别：HTTP头分析、页面内容分析、错误页面分析、Cookie分析")
	fingerprintInfo, err := e.performComprehensiveFingerprintAnalysis(ctx, target)
	if err != nil {
		logger.Warnf("指纹识别失败: %v", err)
		// 指纹识别失败不影响整体流程
	}
	result.FingerprintInfo = fingerprintInfo

	// 更新统计信息
	result.Statistics.Duration = time.Since(startTime)
	result.Statistics.ServicesFound = len(result.PortServices)
	result.Statistics.ComponentsFound = e.countComponents(result.WebComponents)

	e.sendProgress(progress, "信息收集", 100, "信息收集完成")
	return result, nil
}

// collectEnhancedTargetInformation 收集增强的目标信息 - 域名解析、IP地址、DNS信息
func (e *InformationGatheringEngine) collectEnhancedTargetInformation(ctx context.Context, target string) (*types.TargetInformation, error) {
	targetInfo := &types.TargetInformation{
		URL:     target,
		Headers: make(map[string]string),
	}

	// 解析URL获取域名
	parsedURL, err := url.Parse(target)
	if err != nil {
		return nil, fmt.Errorf("URL解析失败: %v", err)
	}
	targetInfo.Domain = parsedURL.Hostname()

	// 增强的DNS解析 - 获取所有IP地址
	ips, err := net.LookupIP(targetInfo.Domain)
	if err != nil {
		return nil, fmt.Errorf("DNS解析失败: %v", err)
	}

	var ipv4Addresses, ipv6Addresses []string
	for _, ip := range ips {
		if ip.To4() != nil {
			ipv4Addresses = append(ipv4Addresses, ip.String())
		} else {
			ipv6Addresses = append(ipv6Addresses, ip.String())
		}
	}

	// 设置主要IP地址
	if len(ipv4Addresses) > 0 {
		targetInfo.IP = ipv4Addresses[0]
		targetInfo.IPType = "IPv4"
	} else if len(ipv6Addresses) > 0 {
		targetInfo.IP = ipv6Addresses[0]
		targetInfo.IPType = "IPv6"
	}

	// 收集增强的DNS信息
	dnsInfo, err := e.collectEnhancedDNSInformation(targetInfo.Domain)
	if err == nil {
		targetInfo.DNS = dnsInfo
		// 将所有IP地址添加到DNS信息中
		targetInfo.DNS.ARecords = ipv4Addresses
		targetInfo.DNS.AAAARecords = ipv6Addresses
	}

	// 发送HTTP请求获取响应信息
	req, err := http.NewRequestWithContext(ctx, "GET", target, nil)
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}
	req.Header.Set("User-Agent", e.userAgent)

	resp, err := e.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 收集响应头信息
	for name, values := range resp.Header {
		if len(values) > 0 {
			targetInfo.Headers[name] = values[0]
		}
	}
	targetInfo.StatusCode = resp.StatusCode

	// 提取页面标题
	title, err := e.extractPageTitle(resp)
	if err == nil {
		targetInfo.Title = title
	}

	// 收集SSL/TLS证书信息（如果是HTTPS）
	if parsedURL.Scheme == "https" {
		certInfo, err := e.collectSSLCertificateInfo(targetInfo.Domain)
		if err == nil {
			targetInfo.SSL = certInfo
		}
	}

	return targetInfo, nil
}

// collectTargetInformation 收集目标基础信息（保持向后兼容）
func (e *InformationGatheringEngine) collectTargetInformation(ctx context.Context, target string) (*types.TargetInformation, error) {
	targetInfo := &types.TargetInformation{
		URL:     target,
		Headers: make(map[string]string),
	}

	// 解析URL获取域名
	parsedURL, err := url.Parse(target)
	if err != nil {
		return nil, fmt.Errorf("URL解析失败: %v", err)
	}
	targetInfo.Domain = parsedURL.Hostname()

	// DNS解析获取IP
	ips, err := net.LookupIP(targetInfo.Domain)
	if err != nil {
		return nil, fmt.Errorf("DNS解析失败: %v", err)
	}
	if len(ips) > 0 {
		targetInfo.IP = ips[0].String()
		if ips[0].To4() != nil {
			targetInfo.IPType = "IPv4"
		} else {
			targetInfo.IPType = "IPv6"
		}
	}

	// 收集DNS信息
	dnsInfo, err := e.collectDNSInformation(targetInfo.Domain)
	if err == nil {
		targetInfo.DNS = dnsInfo
	}

	// 发送HTTP请求获取响应信息
	req, err := http.NewRequestWithContext(ctx, "GET", target, nil)
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %v", err)
	}
	req.Header.Set("User-Agent", e.userAgent)

	resp, err := e.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 收集响应头信息
	for name, values := range resp.Header {
		if len(values) > 0 {
			targetInfo.Headers[name] = values[0]
		}
	}
	targetInfo.StatusCode = resp.StatusCode

	// 提取页面标题
	title, err := e.extractPageTitle(resp)
	if err == nil {
		targetInfo.Title = title
	}

	// 检查SSL信息
	if parsedURL.Scheme == "https" {
		sslInfo, err := e.collectSSLInformation(targetInfo.Domain)
		if err == nil {
			targetInfo.SSL = sslInfo
		}
	}

	// 地理位置信息（简化实现）
	location, err := e.getGeoLocation(targetInfo.IP)
	if err == nil {
		targetInfo.Location = location
	}

	return targetInfo, nil
}

// scanPortsAndServices 扫描端口和服务
func (e *InformationGatheringEngine) scanPortsAndServices(ctx context.Context, ip string) ([]*types.PortService, error) {
	var services []*types.PortService

	// 常见端口列表
	commonPorts := []int{
		21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995,
		1433, 3306, 3389, 5432, 5984, 6379, 8080, 8443, 9200,
	}

	for _, port := range commonPorts {
		select {
		case <-ctx.Done():
			return services, ctx.Err()
		default:
		}

		service, err := e.scanSinglePort(ctx, ip, port)
		if err == nil && service != nil {
			services = append(services, service)
		}
	}

	return services, nil
}

// scanSinglePort 扫描单个端口
func (e *InformationGatheringEngine) scanSinglePort(ctx context.Context, ip string, port int) (*types.PortService, error) {
	address := fmt.Sprintf("%s:%d", ip, port)

	// 设置连接超时
	conn, err := net.DialTimeout("tcp", address, 3*time.Second)
	if err != nil {
		return nil, err // 端口关闭或不可达
	}
	defer conn.Close()

	service := &types.PortService{
		Port:        port,
		Protocol:    "TCP",
		State:       "open",
		Fingerprint: make(map[string]string),
	}

	// 服务识别
	serviceName := e.identifyService(port)
	service.Service = serviceName

	// Banner抓取
	banner, err := e.grabBanner(conn, port)
	if err == nil {
		service.Banner = banner
		service.Version = e.extractVersionFromBanner(banner, serviceName)
	}

	// 安全性检查
	service.IsSecure = e.isSecurePort(port)

	return service, nil
}

// identifyWebComponents 识别Web组件
func (e *InformationGatheringEngine) identifyWebComponents(ctx context.Context, target string) (*types.WebComponentInfo, error) {
	components := &types.WebComponentInfo{
		Libraries:  make([]*types.ComponentVersion, 0),
		Middleware: make([]*types.ComponentVersion, 0),
	}

	// 发送HTTP请求
	req, err := http.NewRequestWithContext(ctx, "GET", target, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("User-Agent", e.userAgent)

	resp, err := e.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 从响应头识别组件
	e.identifyFromHeaders(resp.Header, components)

	// 从响应体识别组件
	body := make([]byte, 8192) // 只读取前8KB
	n, _ := resp.Body.Read(body)
	bodyContent := string(body[:n])

	e.identifyFromContent(bodyContent, components)

	return components, nil
}

// identifyFromHeaders 从HTTP头识别组件
func (e *InformationGatheringEngine) identifyFromHeaders(headers http.Header, components *types.WebComponentInfo) {
	// Web服务器识别
	if server := headers.Get("Server"); server != "" {
		components.WebServer = e.parseServerHeader(server)
	}

	// 编程语言识别
	if poweredBy := headers.Get("X-Powered-By"); poweredBy != "" {
		components.Language = e.parseLanguageHeader(poweredBy)
	}

	// 框架识别
	if framework := headers.Get("X-Framework"); framework != "" {
		components.Framework = e.parseFrameworkHeader(framework)
	}

	// ASP.NET版本
	if aspNet := headers.Get("X-AspNet-Version"); aspNet != "" {
		components.Framework = &types.ComponentVersion{
			Name:         "ASP.NET",
			Version:      aspNet,
			Confidence:   0.9,
			DetectMethod: "HTTP Header",
			Evidence:     []string{fmt.Sprintf("X-AspNet-Version: %s", aspNet)},
		}
	}
}

// identifyFromContent 从页面内容识别组件
func (e *InformationGatheringEngine) identifyFromContent(content string, components *types.WebComponentInfo) {
	// JavaScript库识别
	jsLibraries := e.identifyJavaScriptLibraries(content)
	if components.Frontend == nil {
		components.Frontend = &types.FrontendTechnology{}
	}
	components.Frontend.JavaScript = jsLibraries

	// CSS框架识别
	cssFrameworks := e.identifyCSSFrameworks(content)
	components.Frontend.CSS = cssFrameworks

	// CMS识别 - 简化实现
	if strings.Contains(strings.ToLower(content), "wp-content") || strings.Contains(strings.ToLower(content), "wordpress") {
		components.CMS = &types.ComponentVersion{
			Name:         "WordPress",
			DetectMethod: "content_analysis",
			Evidence:     []string{"WordPress content detected"},
			Confidence:   0.9,
		}
	} else if strings.Contains(strings.ToLower(content), "drupal") {
		components.CMS = &types.ComponentVersion{
			Name:         "Drupal",
			DetectMethod: "content_analysis",
			Evidence:     []string{"Drupal content detected"},
			Confidence:   0.8,
		}
	}
}

// 辅助方法
func (e *InformationGatheringEngine) sendProgress(progress chan<- *types.ScanProgress, stage string, percent int, message string) {
	if progress != nil {
		select {
		case progress <- &types.ScanProgress{
			Stage:    stage,
			Progress: percent,
			Message:  message,
		}:
		default:
		}
	}
}

func (e *InformationGatheringEngine) identifyService(port int) string {
	serviceMap := map[int]string{
		21:   "ftp",
		22:   "ssh",
		23:   "telnet",
		25:   "smtp",
		53:   "dns",
		80:   "http",
		110:  "pop3",
		143:  "imap",
		443:  "https",
		993:  "imaps",
		995:  "pop3s",
		1433: "mssql",
		3306: "mysql",
		3389: "rdp",
		5432: "postgresql",
		5984: "couchdb",
		6379: "redis",
		8080: "http-alt",
		8443: "https-alt",
		9200: "elasticsearch",
	}

	if service, exists := serviceMap[port]; exists {
		return service
	}
	return "unknown"
}

func (e *InformationGatheringEngine) isSecurePort(port int) bool {
	securePorts := map[int]bool{
		22:  true, // SSH
		443: true, // HTTPS
		993: true, // IMAPS
		995: true, // POP3S
	}
	return securePorts[port]
}

func (e *InformationGatheringEngine) countComponents(components *types.WebComponentInfo) int {
	count := 0
	if components.WebServer != nil {
		count++
	}
	if components.Language != nil {
		count++
	}
	if components.Framework != nil {
		count++
	}
	if components.CMS != nil {
		count++
	}
	if components.Database != nil {
		count++
	}
	count += len(components.Libraries)
	count += len(components.Middleware)
	return count
}

// collectDNSInformation 收集DNS信息
func (e *InformationGatheringEngine) collectDNSInformation(domain string) (*types.DNSInformation, error) {
	dnsInfo := &types.DNSInformation{}

	// A记录
	aRecords, err := net.LookupIP(domain)
	if err == nil {
		for _, ip := range aRecords {
			if ip.To4() != nil {
				dnsInfo.ARecords = append(dnsInfo.ARecords, ip.String())
			} else {
				dnsInfo.AAAARecords = append(dnsInfo.AAAARecords, ip.String())
			}
		}
	}

	// CNAME记录
	cname, err := net.LookupCNAME(domain)
	if err == nil && cname != domain+"." {
		dnsInfo.CNAMERecords = append(dnsInfo.CNAMERecords, cname)
	}

	// MX记录
	mxRecords, err := net.LookupMX(domain)
	if err == nil {
		for _, mx := range mxRecords {
			dnsInfo.MXRecords = append(dnsInfo.MXRecords, fmt.Sprintf("%d %s", mx.Pref, mx.Host))
		}
	}

	// TXT记录
	txtRecords, err := net.LookupTXT(domain)
	if err == nil {
		dnsInfo.TXTRecords = txtRecords
	}

	// NS记录
	nsRecords, err := net.LookupNS(domain)
	if err == nil {
		for _, ns := range nsRecords {
			dnsInfo.NSRecords = append(dnsInfo.NSRecords, ns.Host)
		}
	}

	return dnsInfo, nil
}

// collectSSLInformation 收集SSL信息
func (e *InformationGatheringEngine) collectSSLInformation(domain string) (*types.SSLInformation, error) {
	conn, err := tls.Dial("tcp", domain+":443", &tls.Config{
		InsecureSkipVerify: true,
	})
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	state := conn.ConnectionState()
	sslInfo := &types.SSLInformation{
		Enabled: true,
		Version: e.getTLSVersionString(state.Version),
		Cipher:  tls.CipherSuiteName(state.CipherSuite),
	}

	// 证书信息
	if len(state.PeerCertificates) > 0 {
		cert := state.PeerCertificates[0]
		sslInfo.Certificate = &types.CertInfo{
			Subject:    cert.Subject.String(),
			Issuer:     cert.Issuer.String(),
			NotBefore:  cert.NotBefore,
			NotAfter:   cert.NotAfter,
			IsExpired:  time.Now().After(cert.NotAfter),
			IsWildcard: strings.HasPrefix(cert.Subject.CommonName, "*."),
			SANs:       cert.DNSNames,
		}
	}

	return sslInfo, nil
}

// getTLSVersionString 获取TLS版本字符串
func (e *InformationGatheringEngine) getTLSVersionString(version uint16) string {
	switch version {
	case tls.VersionTLS10:
		return "TLS 1.0"
	case tls.VersionTLS11:
		return "TLS 1.1"
	case tls.VersionTLS12:
		return "TLS 1.2"
	case tls.VersionTLS13:
		return "TLS 1.3"
	default:
		return "Unknown"
	}
}

// extractPageTitle 提取页面标题
func (e *InformationGatheringEngine) extractPageTitle(resp *http.Response) (string, error) {
	body := make([]byte, 8192)
	n, _ := resp.Body.Read(body)
	content := string(body[:n])

	titleRegex := regexp.MustCompile(`<title[^>]*>([^<]+)</title>`)
	matches := titleRegex.FindStringSubmatch(content)
	if len(matches) > 1 {
		return strings.TrimSpace(matches[1]), nil
	}
	return "", fmt.Errorf("title not found")
}

// getGeoLocation 获取地理位置信息（简化实现）
func (e *InformationGatheringEngine) getGeoLocation(ip string) (*types.GeoLocation, error) {
	// 这里应该调用地理位置API，简化实现返回默认值
	return &types.GeoLocation{
		Country: "Unknown",
		Region:  "Unknown",
		City:    "Unknown",
		ISP:     "Unknown",
		ASN:     "Unknown",
	}, nil
}

// grabBanner 抓取服务Banner
func (e *InformationGatheringEngine) grabBanner(conn net.Conn, port int) (string, error) {
	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))

	buffer := make([]byte, 1024)
	n, err := conn.Read(buffer)
	if err != nil {
		return "", err
	}

	banner := strings.TrimSpace(string(buffer[:n]))
	return banner, nil
}

// extractVersionFromBanner 从Banner提取版本信息
func (e *InformationGatheringEngine) extractVersionFromBanner(banner, service string) string {
	// 版本提取正则表达式
	versionPatterns := map[string]*regexp.Regexp{
		"ssh":   regexp.MustCompile(`OpenSSH[_\s]+([\d\.]+)`),
		"http":  regexp.MustCompile(`Apache/([\d\.]+)`),
		"https": regexp.MustCompile(`nginx/([\d\.]+)`),
		"ftp":   regexp.MustCompile(`vsftpd\s+([\d\.]+)`),
		"mysql": regexp.MustCompile(`MySQL\s+([\d\.]+)`),
		"mssql": regexp.MustCompile(`Microsoft SQL Server\s+([\d\.]+)`),
	}

	if pattern, exists := versionPatterns[service]; exists {
		matches := pattern.FindStringSubmatch(banner)
		if len(matches) > 1 {
			return matches[1]
		}
	}

	// 通用版本提取
	generalPattern := regexp.MustCompile(`([\d]+\.[\d]+\.?[\d]*\.?[\d]*)`)
	matches := generalPattern.FindStringSubmatch(banner)
	if len(matches) > 1 {
		return matches[1]
	}

	return ""
}

// parseServerHeader 解析Server头
func (e *InformationGatheringEngine) parseServerHeader(server string) *types.ComponentVersion {
	// Apache/2.4.41 (Ubuntu)
	apacheRegex := regexp.MustCompile(`Apache/([\d\.]+)`)
	if matches := apacheRegex.FindStringSubmatch(server); len(matches) > 1 {
		return &types.ComponentVersion{
			Name:         "Apache",
			Version:      matches[1],
			Confidence:   0.95,
			DetectMethod: "HTTP Header",
			Evidence:     []string{fmt.Sprintf("Server: %s", server)},
		}
	}

	// nginx/1.18.0
	nginxRegex := regexp.MustCompile(`nginx/([\d\.]+)`)
	if matches := nginxRegex.FindStringSubmatch(server); len(matches) > 1 {
		return &types.ComponentVersion{
			Name:         "Nginx",
			Version:      matches[1],
			Confidence:   0.95,
			DetectMethod: "HTTP Header",
			Evidence:     []string{fmt.Sprintf("Server: %s", server)},
		}
	}

	// Microsoft-IIS/10.0
	iisRegex := regexp.MustCompile(`Microsoft-IIS/([\d\.]+)`)
	if matches := iisRegex.FindStringSubmatch(server); len(matches) > 1 {
		return &types.ComponentVersion{
			Name:         "IIS",
			Version:      matches[1],
			Confidence:   0.95,
			DetectMethod: "HTTP Header",
			Evidence:     []string{fmt.Sprintf("Server: %s", server)},
		}
	}

	return &types.ComponentVersion{
		Name:         "Unknown",
		Version:      "",
		Confidence:   0.5,
		DetectMethod: "HTTP Header",
		Evidence:     []string{fmt.Sprintf("Server: %s", server)},
	}
}

// parseLanguageHeader 解析编程语言头
func (e *InformationGatheringEngine) parseLanguageHeader(poweredBy string) *types.ComponentVersion {
	// PHP/7.4.3
	phpRegex := regexp.MustCompile(`PHP/([\d\.]+)`)
	if matches := phpRegex.FindStringSubmatch(poweredBy); len(matches) > 1 {
		return &types.ComponentVersion{
			Name:         "PHP",
			Version:      matches[1],
			Confidence:   0.9,
			DetectMethod: "HTTP Header",
			Evidence:     []string{fmt.Sprintf("X-Powered-By: %s", poweredBy)},
		}
	}

	// ASP.NET
	if strings.Contains(strings.ToLower(poweredBy), "asp.net") {
		return &types.ComponentVersion{
			Name:         "ASP.NET",
			Version:      "",
			Confidence:   0.9,
			DetectMethod: "HTTP Header",
			Evidence:     []string{fmt.Sprintf("X-Powered-By: %s", poweredBy)},
		}
	}

	return nil
}

// parseFrameworkHeader 解析框架头
func (e *InformationGatheringEngine) parseFrameworkHeader(framework string) *types.ComponentVersion {
	return &types.ComponentVersion{
		Name:         framework,
		Version:      "",
		Confidence:   0.8,
		DetectMethod: "HTTP Header",
		Evidence:     []string{fmt.Sprintf("X-Framework: %s", framework)},
	}
}

// identifyJavaScriptLibraries 识别JavaScript库
func (e *InformationGatheringEngine) identifyJavaScriptLibraries(content string) []*types.ComponentVersion {
	var libraries []*types.ComponentVersion

	// jQuery识别
	jqueryRegex := regexp.MustCompile(`jquery[.-]?([\d\.]+)`)
	if matches := jqueryRegex.FindStringSubmatch(content); len(matches) > 1 {
		libraries = append(libraries, &types.ComponentVersion{
			Name:         "jQuery",
			Version:      matches[1],
			Confidence:   0.8,
			DetectMethod: "Content Analysis",
			Evidence:     []string{"JavaScript library detected in content"},
		})
	}

	// React识别
	reactRegex := regexp.MustCompile(`react[.-]?([\d\.]+)`)
	if matches := reactRegex.FindStringSubmatch(content); len(matches) > 1 {
		libraries = append(libraries, &types.ComponentVersion{
			Name:         "React",
			Version:      matches[1],
			Confidence:   0.8,
			DetectMethod: "Content Analysis",
			Evidence:     []string{"React library detected in content"},
		})
	}

	// Vue.js识别
	vueRegex := regexp.MustCompile(`vue[.-]?([\d\.]+)`)
	if matches := vueRegex.FindStringSubmatch(content); len(matches) > 1 {
		libraries = append(libraries, &types.ComponentVersion{
			Name:         "Vue.js",
			Version:      matches[1],
			Confidence:   0.8,
			DetectMethod: "Content Analysis",
			Evidence:     []string{"Vue.js library detected in content"},
		})
	}

	return libraries
}

// identifyCSSFrameworks 识别CSS框架
func (e *InformationGatheringEngine) identifyCSSFrameworks(content string) []*types.ComponentVersion {
	var frameworks []*types.ComponentVersion

	// Bootstrap识别
	if strings.Contains(content, "bootstrap") {
		bootstrapRegex := regexp.MustCompile(`bootstrap[.-]?([\d\.]+)`)
		version := ""
		if matches := bootstrapRegex.FindStringSubmatch(content); len(matches) > 1 {
			version = matches[1]
		}
		frameworks = append(frameworks, &types.ComponentVersion{
			Name:         "Bootstrap",
			Version:      version,
			Confidence:   0.7,
			DetectMethod: "Content Analysis",
			Evidence:     []string{"Bootstrap CSS framework detected"},
		})
	}

	// Foundation识别
	if strings.Contains(content, "foundation") {
		frameworks = append(frameworks, &types.ComponentVersion{
			Name:         "Foundation",
			Version:      "",
			Confidence:   0.7,
			DetectMethod: "Content Analysis",
			Evidence:     []string{"Foundation CSS framework detected"},
		})
	}

	return frameworks
}

// collectSystemInformation 收集系统信息
func (e *InformationGatheringEngine) collectSystemInformation(ctx context.Context, target string) (*types.SystemInformation, error) {
	systemInfo := &types.SystemInformation{}

	// 通过HTTP头推断操作系统
	req, err := http.NewRequestWithContext(ctx, "GET", target, nil)
	if err != nil {
		return systemInfo, err
	}
	req.Header.Set("User-Agent", e.userAgent)

	resp, err := e.client.Do(req)
	if err != nil {
		return systemInfo, err
	}
	defer resp.Body.Close()

	// 从Server头推断操作系统
	server := resp.Header.Get("Server")
	if server != "" {
		os := e.inferOSFromServer(server)
		if os != nil {
			systemInfo.OS = os
		}
	}

	return systemInfo, nil
}

// inferOSFromServer 从Server头推断操作系统
func (e *InformationGatheringEngine) inferOSFromServer(server string) *types.ComponentVersion {
	server = strings.ToLower(server)

	if strings.Contains(server, "ubuntu") {
		return &types.ComponentVersion{
			Name:         "Ubuntu",
			Version:      "",
			Confidence:   0.7,
			DetectMethod: "HTTP Header Inference",
			Evidence:     []string{fmt.Sprintf("Server: %s", server)},
		}
	}

	if strings.Contains(server, "centos") {
		return &types.ComponentVersion{
			Name:         "CentOS",
			Version:      "",
			Confidence:   0.7,
			DetectMethod: "HTTP Header Inference",
			Evidence:     []string{fmt.Sprintf("Server: %s", server)},
		}
	}

	if strings.Contains(server, "windows") || strings.Contains(server, "iis") {
		return &types.ComponentVersion{
			Name:         "Windows",
			Version:      "",
			Confidence:   0.6,
			DetectMethod: "HTTP Header Inference",
			Evidence:     []string{fmt.Sprintf("Server: %s", server)},
		}
	}

	if strings.Contains(server, "unix") {
		return &types.ComponentVersion{
			Name:         "Unix",
			Version:      "",
			Confidence:   0.5,
			DetectMethod: "HTTP Header Inference",
			Evidence:     []string{fmt.Sprintf("Server: %s", server)},
		}
	}

	return nil
}

// analyzeNetworkTopology 分析网络拓扑
func (e *InformationGatheringEngine) analyzeNetworkTopology(ctx context.Context, ip string) (*types.NetworkTopology, error) {
	topology := &types.NetworkTopology{
		Subnets:     make([]string, 0),
		Gateways:    make([]string, 0),
		NameServers: make([]string, 0),
		Routes:      make([]string, 0),
	}

	// 分析IP所在子网
	if ip != "" {
		subnet := e.inferSubnet(ip)
		if subnet != "" {
			topology.Subnets = append(topology.Subnets, subnet)
		}
	}

	return topology, nil
}

// inferSubnet 推断子网
func (e *InformationGatheringEngine) inferSubnet(ip string) string {
	// 简化实现：根据IP推断可能的子网
	parts := strings.Split(ip, ".")
	if len(parts) == 4 {
		// 假设是/24子网
		return fmt.Sprintf("%s.%s.%s.0/24", parts[0], parts[1], parts[2])
	}
	return ""
}

// collectEnhancedDNSInformation 收集增强的DNS信息
func (e *InformationGatheringEngine) collectEnhancedDNSInformation(domain string) (*types.DNSInformation, error) {
	dnsInfo := &types.DNSInformation{}

	// A记录 - 已在上层处理，这里保持兼容
	aRecords, err := net.LookupIP(domain)
	if err == nil {
		for _, ip := range aRecords {
			if ip.To4() != nil {
				dnsInfo.ARecords = append(dnsInfo.ARecords, ip.String())
			} else {
				dnsInfo.AAAARecords = append(dnsInfo.AAAARecords, ip.String())
			}
		}
	}

	// CNAME记录
	cname, err := net.LookupCNAME(domain)
	if err == nil && cname != domain+"." {
		dnsInfo.CNAMERecords = append(dnsInfo.CNAMERecords, strings.TrimSuffix(cname, "."))
	}

	// MX记录
	mxRecords, err := net.LookupMX(domain)
	if err == nil {
		for _, mx := range mxRecords {
			dnsInfo.MXRecords = append(dnsInfo.MXRecords, fmt.Sprintf("%d %s", mx.Pref, strings.TrimSuffix(mx.Host, ".")))
		}
	}

	// NS记录
	nsRecords, err := net.LookupNS(domain)
	if err == nil {
		for _, ns := range nsRecords {
			dnsInfo.NSRecords = append(dnsInfo.NSRecords, strings.TrimSuffix(ns.Host, "."))
		}
	}

	// TXT记录
	txtRecords, err := net.LookupTXT(domain)
	if err == nil {
		dnsInfo.TXTRecords = txtRecords
	}

	return dnsInfo, nil
}

// collectSSLCertificateInfo 收集SSL证书信息
func (e *InformationGatheringEngine) collectSSLCertificateInfo(domain string) (*types.SSLInformation, error) {
	sslInfo := &types.SSLInformation{
		Enabled: true,
	}

	// 建立TLS连接获取证书信息
	conn, err := tls.Dial("tcp", domain+":443", &tls.Config{
		InsecureSkipVerify: true,
	})
	if err != nil {
		return nil, fmt.Errorf("TLS连接失败: %v", err)
	}
	defer conn.Close()

	// 获取连接状态
	state := conn.ConnectionState()
	sslInfo.Version = e.getTLSVersionString(state.Version)
	sslInfo.Cipher = tls.CipherSuiteName(state.CipherSuite)

	// 获取证书信息
	if len(state.PeerCertificates) > 0 {
		cert := state.PeerCertificates[0]
		sslInfo.Certificate = &types.CertInfo{
			Subject:    cert.Subject.String(),
			Issuer:     cert.Issuer.String(),
			NotBefore:  cert.NotBefore,
			NotAfter:   cert.NotAfter,
			IsExpired:  cert.NotAfter.Before(time.Now()),
			IsWildcard: strings.HasPrefix(cert.Subject.CommonName, "*."),
			SANs:       cert.DNSNames,
		}
	}

	return sslInfo, nil
}

// 统计方法

// countSensitiveFiles 统计敏感文件数量
func (e *InformationGatheringEngine) countSensitiveFiles(sensitiveInfo *types.SensitiveInformation) int {
	if sensitiveInfo == nil {
		return 0
	}
	return sensitiveInfo.TotalFiles
}

// countProtectionMeasures 统计防护措施数量
func (e *InformationGatheringEngine) countProtectionMeasures(protectionInfo *types.ProtectionInformation) int {
	if protectionInfo == nil {
		return 0
	}

	count := 0
	if protectionInfo.WAF != nil && protectionInfo.WAF.Detected {
		count++
	}
	if protectionInfo.SecurityHeaders != nil {
		if protectionInfo.SecurityHeaders.HSTS != nil && protectionInfo.SecurityHeaders.HSTS.Enabled {
			count++
		}
		if protectionInfo.SecurityHeaders.CSP != nil && protectionInfo.SecurityHeaders.CSP.Enabled {
			count++
		}
		if protectionInfo.SecurityHeaders.XFrameOptions != nil && protectionInfo.SecurityHeaders.XFrameOptions.Enabled {
			count++
		}
	}

	return count
}

// getServiceProduct 获取服务产品信息
func (e *InformationGatheringEngine) getServiceProduct(serviceName, banner string) string {
	if banner == "" {
		return ""
	}

	banner = strings.ToLower(banner)

	switch serviceName {
	case "http", "https":
		if strings.Contains(banner, "apache") {
			return "Apache HTTP Server"
		} else if strings.Contains(banner, "nginx") {
			return "Nginx"
		} else if strings.Contains(banner, "iis") {
			return "Microsoft IIS"
		}
	case "ssh":
		if strings.Contains(banner, "openssh") {
			return "OpenSSH"
		}
	case "ftp":
		if strings.Contains(banner, "vsftpd") {
			return "vsftpd"
		} else if strings.Contains(banner, "proftpd") {
			return "ProFTPD"
		}
	case "mysql":
		return "MySQL Server"
	case "postgres":
		return "PostgreSQL"
	}

	return ""
}

// getServiceExtraInfo 获取服务额外信息
func (e *InformationGatheringEngine) getServiceExtraInfo(port int, banner string) string {
	info := make([]string, 0)

	if banner != "" {
		banner = strings.ToLower(banner)

		// 检查SSL/TLS支持
		if strings.Contains(banner, "ssl") || strings.Contains(banner, "tls") {
			info = append(info, "SSL/TLS支持")
		}

		// 检查认证方式
		if strings.Contains(banner, "auth") {
			info = append(info, "需要认证")
		}

		// 检查版本信息
		if strings.Contains(banner, "version") {
			info = append(info, "版本信息可见")
		}
	}

	// 根据端口添加默认信息
	switch port {
	case 21:
		info = append(info, "FTP服务")
	case 22:
		info = append(info, "SSH服务")
	case 23:
		info = append(info, "Telnet服务")
	case 25:
		info = append(info, "SMTP服务")
	case 53:
		info = append(info, "DNS服务")
	case 80:
		info = append(info, "HTTP服务")
	case 443:
		info = append(info, "HTTPS服务")
	case 3306:
		info = append(info, "MySQL数据库")
	case 5432:
		info = append(info, "PostgreSQL数据库")
	case 6379:
		info = append(info, "Redis缓存")
	case 27017:
		info = append(info, "MongoDB数据库")
	}

	return strings.Join(info, ", ")
}

// scanEnhancedPortsAndServices 增强的端口服务扫描 - 开放端口、运行服务、版本识别
func (e *InformationGatheringEngine) scanEnhancedPortsAndServices(ctx context.Context, ip string) ([]*types.PortService, error) {
	var services []*types.PortService

	// 扩展的常见端口列表，包含更多服务
	commonPorts := []int{
		// Web服务
		80, 443, 8080, 8443, 8000, 8888, 9000, 3000,
		// 数据库
		3306, 5432, 1433, 1521, 27017, 6379, 5984, 9200,
		// 系统服务
		21, 22, 23, 25, 53, 110, 143, 993, 995, 3389,
		// 应用服务
		1080, 1433, 2049, 2181, 3000, 5000, 5432, 6379,
		7001, 8009, 8080, 8161, 8888, 9000, 9200, 11211,
		// 其他常见端口
		135, 139, 445, 1024, 1025, 1026, 1027, 1028, 1029,
		1433, 1434, 1521, 1723, 2049, 2121, 2375, 2376,
		3000, 3001, 3128, 3306, 3389, 4444, 5000, 5432,
		5672, 5984, 6379, 7000, 7001, 8000, 8080, 8443,
		8888, 9000, 9200, 9300, 11211, 27017, 50070,
	}

	// 并发扫描端口
	semaphore := make(chan struct{}, 50) // 限制并发数
	var wg sync.WaitGroup
	var mu sync.Mutex

	for _, port := range commonPorts {
		select {
		case <-ctx.Done():
			return services, ctx.Err()
		default:
		}

		wg.Add(1)
		go func(p int) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			service, err := e.scanEnhancedSinglePort(ctx, ip, p)
			if err == nil && service != nil {
				mu.Lock()
				services = append(services, service)
				mu.Unlock()
			}
		}(port)
	}

	wg.Wait()
	return services, nil
}

// scanEnhancedSinglePort 增强的单端口扫描，包含版本识别
func (e *InformationGatheringEngine) scanEnhancedSinglePort(ctx context.Context, ip string, port int) (*types.PortService, error) {
	// 设置连接超时
	timeout := 3 * time.Second
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", ip, port), timeout)
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	service := &types.PortService{
		Port:     port,
		Protocol: "tcp",
		State:    "open",
	}

	// 服务识别
	serviceName := e.identifyService(port)
	service.Service = serviceName

	// 版本识别 - 发送探测数据并分析响应
	version, banner := e.identifyServiceVersion(conn, port, serviceName)
	service.Version = version
	service.Banner = banner

	// 安全性检查
	service.IsSecure = e.isSecurePort(port)

	// 设置指纹信息
	if service.Fingerprint == nil {
		service.Fingerprint = make(map[string]string)
	}
	if banner != "" {
		service.Fingerprint["banner"] = banner
	}
	service.Fingerprint["product"] = e.getServiceProduct(serviceName, banner)
	service.Fingerprint["extra_info"] = e.getServiceExtraInfo(port, banner)

	return service, nil
}

// identifyServiceVersion 识别服务版本
func (e *InformationGatheringEngine) identifyServiceVersion(conn net.Conn, port int, serviceName string) (string, string) {
	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))

	var probe []byte
	var banner string

	// 根据服务类型发送不同的探测数据
	switch serviceName {
	case "http", "https":
		probe = []byte("GET / HTTP/1.1\r\nHost: localhost\r\nUser-Agent: Scanner/1.0\r\n\r\n")
	case "ftp":
		// FTP通常会主动发送banner
		probe = []byte("")
	case "ssh":
		// SSH会发送版本信息
		probe = []byte("")
	case "smtp":
		probe = []byte("EHLO localhost\r\n")
	case "pop3":
		probe = []byte("USER test\r\n")
	case "imap":
		probe = []byte("A001 CAPABILITY\r\n")
	case "mysql":
		// MySQL会发送初始握手包
		probe = []byte("")
	default:
		// 通用探测
		probe = []byte("\r\n")
	}

	// 发送探测数据
	if len(probe) > 0 {
		conn.Write(probe)
	}

	// 读取响应
	buffer := make([]byte, 1024)
	n, err := conn.Read(buffer)
	if err == nil && n > 0 {
		banner = string(buffer[:n])
		// 清理banner，移除不可打印字符
		banner = strings.Map(func(r rune) rune {
			if r >= 32 && r <= 126 {
				return r
			}
			return ' '
		}, banner)
		banner = strings.TrimSpace(banner)
	}

	// 从banner中提取版本信息
	version := e.extractVersionFromBanner(banner, serviceName)

	return version, banner
}

// extractVersionFromBanner 从banner中提取版本信息
func (e *InformationGatheringEngine) extractVersionFromBanner(banner, serviceName string) string {
	if banner == "" {
		return ""
	}

	// 常见的版本模式
	versionPatterns := map[string][]string{
		"apache": {
			`Apache/([0-9]+\.[0-9]+\.[0-9]+)`,
			`Apache/([0-9]+\.[0-9]+)`,
		},
		"nginx": {
			`nginx/([0-9]+\.[0-9]+\.[0-9]+)`,
			`nginx/([0-9]+\.[0-9]+)`,
		},
		"mysql": {
			`([0-9]+\.[0-9]+\.[0-9]+)`,
		},
		"ssh": {
			`OpenSSH_([0-9]+\.[0-9]+)`,
			`SSH-[0-9]+\.[0-9]+-OpenSSH_([0-9]+\.[0-9]+)`,
		},
		"ftp": {
			`vsftpd ([0-9]+\.[0-9]+\.[0-9]+)`,
			`ProFTPD ([0-9]+\.[0-9]+\.[0-9]+)`,
		},
	}

	// 尝试匹配版本模式
	if patterns, exists := versionPatterns[serviceName]; exists {
		for _, pattern := range patterns {
			re := regexp.MustCompile(pattern)
			if matches := re.FindStringSubmatch(banner); len(matches) > 1 {
				return matches[1]
			}
		}
	}

	// 通用版本匹配
	generalPatterns := []string{
		`([0-9]+\.[0-9]+\.[0-9]+)`,
		`([0-9]+\.[0-9]+)`,
		`v([0-9]+\.[0-9]+\.[0-9]+)`,
		`version ([0-9]+\.[0-9]+)`,
	}

	for _, pattern := range generalPatterns {
		re := regexp.MustCompile(pattern)
		if matches := re.FindStringSubmatch(banner); len(matches) > 1 {
			return matches[1]
		}
	}

	return ""
}
