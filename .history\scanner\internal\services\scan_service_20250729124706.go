package services

import (
	"encoding/json"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"scanner/internal/models"

	"gorm.io/gorm"
)

// ScanService 扫描服务
type ScanService struct {
	db *gorm.DB
}

// NewScanService 创建新的扫描服务
func NewScanService(db *gorm.DB) *ScanService {
	return &ScanService{
		db: db,
	}
}

// CreateTask 创建扫描任务
func (s *ScanService) CreateTask(task *models.ScanTask) error {
	if err := s.db.Create(task).Error; err != nil {
		return fmt.Errorf("创建扫描任务失败: %v", err)
	}
	return nil
}

// GetTaskByID 根据ID获取扫描任务
func (s *ScanService) GetTaskByID(id uint) (*models.ScanTask, error) {
	var task models.ScanTask
	if err := s.db.First(&task, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("扫描任务不存在")
		}
		return nil, fmt.Errorf("获取扫描任务失败: %v", err)
	}
	return &task, nil
}

// GetTasks 分页获取扫描任务列表
func (s *ScanService) GetTasks(page, size int, scanType, status, keyword string) ([]*models.ScanTask, int64, error) {
	var tasks []*models.ScanTask
	var total int64

	query := s.db.Model(&models.ScanTask{})

	// 添加过滤条件
	if scanType != "" {
		query = query.Where("type = ?", scanType)
	}
	if status != "" {
		query = query.Where("status = ?", status)
	}
	if keyword != "" {
		query = query.Where("name LIKE ? OR description LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取任务总数失败: %v", err)
	}

	// 分页查询
	offset := (page - 1) * size
	if err := query.Order("created_at DESC").
		Offset(offset).Limit(size).
		Find(&tasks).Error; err != nil {
		return nil, 0, fmt.Errorf("获取任务列表失败: %v", err)
	}

	return tasks, total, nil
}

// UpdateTask 更新扫描任务
func (s *ScanService) UpdateTask(task *models.ScanTask) error {
	if err := s.db.Save(task).Error; err != nil {
		return fmt.Errorf("更新扫描任务失败: %v", err)
	}
	return nil
}

// UpdateTaskStatus 更新任务状态
func (s *ScanService) UpdateTaskStatus(taskID uint, status string) error {
	// 更新任务状态
	updates := map[string]interface{}{
		"status": status,
	}

	// 如果是完成状态，设置进度为100%
	if status == "completed" {
		updates["progress"] = 100
		// 注意：ScanTask模型中没有completed_at字段，所以不更新该字段
	} else if status == "failed" {
		// 失败状态不需要更新completed_at字段
	}

	if err := s.db.Model(&models.ScanTask{}).Where("id = ?", taskID).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("更新任务状态失败: %v", err)
	}

	return nil
}

// DeleteTask 删除扫描任务
func (s *ScanService) DeleteTask(id uint) error {
	// 先删除相关的漏洞记录
	if err := s.db.Where("task_id = ?", id).Delete(&models.Vulnerability{}).Error; err != nil {
		return fmt.Errorf("删除相关漏洞记录失败: %v", err)
	}

	// 删除扫描进度记录
	if err := s.db.Where("task_id = ?", id).Delete(&models.ScanProgress{}).Error; err != nil {
		return fmt.Errorf("删除扫描进度记录失败: %v", err)
	}

	// 删除扫描日志记录
	if err := s.db.Where("task_id = ?", id).Delete(&models.ScanLog{}).Error; err != nil {
		return fmt.Errorf("删除扫描日志记录失败: %v", err)
	}

	// 删除信息收集日志记录
	if err := s.db.Where("task_id = ?", id).Delete(&models.InfoGatheringLog{}).Error; err != nil {
		return fmt.Errorf("删除信息收集日志记录失败: %v", err)
	}

	// 删除扫描任务
	if err := s.db.Delete(&models.ScanTask{}, id).Error; err != nil {
		return fmt.Errorf("删除扫描任务失败: %v", err)
	}

	return nil
}

// GetTaskVulnerabilities 获取扫描任务的漏洞列表
func (s *ScanService) GetTaskVulnerabilities(taskID uint, page, size int, severity, vulnType string) ([]*models.Vulnerability, int64, error) {
	var vulns []*models.Vulnerability
	var total int64

	query := s.db.Model(&models.Vulnerability{}).Where("task_id = ?", taskID)

	// 添加严重程度过滤
	if severity != "" {
		query = query.Where("severity = ?", severity)
	}

	// 添加漏洞类型过滤
	if vulnType != "" {
		query = query.Where("type = ?", vulnType)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("获取漏洞总数失败: %v", err)
	}

	// 分页查询
	offset := (page - 1) * size
	if err := query.Order("created_at DESC").
		Offset(offset).Limit(size).
		Find(&vulns).Error; err != nil {
		return nil, 0, fmt.Errorf("获取漏洞列表失败: %v", err)
	}

	return vulns, total, nil
}

// CreateVulnerability 创建漏洞记录
func (s *ScanService) CreateVulnerability(vuln *models.Vulnerability) error {
	if err := s.db.Create(vuln).Error; err != nil {
		return fmt.Errorf("创建漏洞记录失败: %v", err)
	}
	return nil
}

// UpdateTaskProgress 更新任务进度
func (s *ScanService) UpdateTaskProgress(taskID uint, progress int, stage, message string) error {
	// 准备更新字段
	updates := map[string]interface{}{
		"progress": progress,
	}

	// 如果进度达到100%，自动更新状态为completed
	if progress >= 100 {
		updates["status"] = "completed"
		updates["progress"] = 100 // 确保进度为100
		// 注意：ScanTask模型中没有completed_at字段，所以不更新该字段
	}

	// 更新任务进度和状态
	if err := s.db.Model(&models.ScanTask{}).Where("id = ?", taskID).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("更新任务进度失败: %v", err)
	}

	// 创建进度记录
	progressRecord := &models.ScanProgress{
		TaskID:   taskID,
		Stage:    stage,
		Progress: progress,
		Message:  message,
	}

	if err := s.db.Create(progressRecord).Error; err != nil {
		return fmt.Errorf("创建进度记录失败: %v", err)
	}

	return nil
}

// GetTaskProgress 获取任务进度历史
func (s *ScanService) GetTaskProgress(taskID uint) ([]*models.ScanProgress, error) {
	var progress []*models.ScanProgress

	if err := s.db.Where("task_id = ?", taskID).
		Order("created_at ASC").
		Find(&progress).Error; err != nil {
		return nil, fmt.Errorf("获取任务进度失败: %v", err)
	}

	return progress, nil
}

// UpdateTaskStatistics 更新任务统计信息
func (s *ScanService) UpdateTaskStatistics(taskID uint, stats map[string]interface{}) error {
	if err := s.db.Model(&models.ScanTask{}).Where("id = ?", taskID).Updates(stats).Error; err != nil {
		return fmt.Errorf("更新任务统计信息失败: %v", err)
	}
	return nil
}

// GetTasksByStatus 根据状态获取任务列表
func (s *ScanService) GetTasksByStatus(status string) ([]*models.ScanTask, error) {
	var tasks []*models.ScanTask

	if err := s.db.Where("status = ?", status).
		Order("created_at DESC").
		Find(&tasks).Error; err != nil {
		return nil, fmt.Errorf("获取任务列表失败: %v", err)
	}

	return tasks, nil
}

// GetRunningTasks 获取正在运行的任务
func (s *ScanService) GetRunningTasks() ([]*models.ScanTask, error) {
	return s.GetTasksByStatus("running")
}

// GetPendingTasks 获取等待执行的任务
func (s *ScanService) GetPendingTasks() ([]*models.ScanTask, error) {
	return s.GetTasksByStatus("pending")
}

// GetTaskStatistics 获取任务统计信息
func (s *ScanService) GetTaskStatistics() (map[string]int64, error) {
	stats := make(map[string]int64)

	// 统计各状态任务数量
	statuses := []string{"pending", "running", "completed", "failed", "stopped"}
	for _, status := range statuses {
		var count int64
		if err := s.db.Model(&models.ScanTask{}).Where("status = ?", status).Count(&count).Error; err != nil {
			return nil, fmt.Errorf("统计任务数量失败: %v", err)
		}
		stats[status] = count
	}

	// 统计总任务数
	var totalTasks int64
	if err := s.db.Model(&models.ScanTask{}).Count(&totalTasks).Error; err != nil {
		return nil, fmt.Errorf("统计总任务数失败: %v", err)
	}
	stats["total"] = totalTasks

	// 统计总漏洞数
	var totalVulns int64
	if err := s.db.Model(&models.Vulnerability{}).Count(&totalVulns).Error; err != nil {
		return nil, fmt.Errorf("统计总漏洞数失败: %v", err)
	}
	stats["total_vulnerabilities"] = totalVulns

	return stats, nil
}

// TaskStats 任务统计结构
type TaskStats struct {
	TotalTasks     int64 `json:"total_tasks"`
	RunningTasks   int64 `json:"running_tasks"`
	CompletedTasks int64 `json:"completed_tasks"`
	FailedTasks    int64 `json:"failed_tasks"`
	PendingTasks   int64 `json:"pending_tasks"`
	TotalVulns     int64 `json:"total_vulns"`
}

// ScanTaskStatistics 扫描任务详细统计结构
type ScanTaskStatistics struct {
	// 基本信息
	TaskID   uint   `json:"task_id"`
	TaskType string `json:"task_type"`
	TaskName string `json:"task_name"`

	// 目标统计（适用于资产发现任务）
	TotalTargets     int `json:"total_targets"`     // 扫描目标总数
	UniqueHosts      int `json:"unique_hosts"`      // 唯一主机数
	OpenPorts        int `json:"open_ports"`        // 开放端口数
	WebServices      int `json:"web_services"`      // Web服务数
	DiscoveredAssets int `json:"discovered_assets"` // 发现的资产数

	// 漏洞统计（适用于漏洞扫描任务）
	TotalVulns    int `json:"total_vulns"`    // 漏洞总数
	CriticalVulns int `json:"critical_vulns"` // 严重漏洞数
	HighVulns     int `json:"high_vulns"`     // 高危漏洞数
	MediumVulns   int `json:"medium_vulns"`   // 中危漏洞数
	LowVulns      int `json:"low_vulns"`      // 低危漏洞数
	InfoVulns     int `json:"info_vulns"`     // 信息漏洞数

	// 扫描统计
	TotalRequests   int `json:"total_requests"`   // 总请求数
	SuccessRequests int `json:"success_requests"` // 成功请求数
	FailedRequests  int `json:"failed_requests"`  // 失败请求数
	ScanDuration    int `json:"scan_duration"`    // 扫描时长（秒）

	// 时间信息
	StartTime *time.Time `json:"start_time"` // 开始时间
	EndTime   *time.Time `json:"end_time"`   // 结束时间
}

// GetTaskStats 获取任务统计信息（结构化返回）
func (s *ScanService) GetTaskStats() (*TaskStats, error) {
	stats := &TaskStats{}

	// 统计总任务数
	if err := s.db.Model(&models.ScanTask{}).Count(&stats.TotalTasks).Error; err != nil {
		return nil, fmt.Errorf("统计总任务数失败: %v", err)
	}

	// 统计运行中任务数
	if err := s.db.Model(&models.ScanTask{}).Where("status = ?", "running").Count(&stats.RunningTasks).Error; err != nil {
		return nil, fmt.Errorf("统计运行中任务数失败: %v", err)
	}

	// 统计已完成任务数
	if err := s.db.Model(&models.ScanTask{}).Where("status = ?", "completed").Count(&stats.CompletedTasks).Error; err != nil {
		return nil, fmt.Errorf("统计已完成任务数失败: %v", err)
	}

	// 统计失败任务数
	if err := s.db.Model(&models.ScanTask{}).Where("status = ?", "failed").Count(&stats.FailedTasks).Error; err != nil {
		return nil, fmt.Errorf("统计失败任务数失败: %v", err)
	}

	// 统计等待中任务数
	if err := s.db.Model(&models.ScanTask{}).Where("status = ?", "pending").Count(&stats.PendingTasks).Error; err != nil {
		return nil, fmt.Errorf("统计等待中任务数失败: %v", err)
	}

	// 统计总漏洞数
	if err := s.db.Model(&models.Vulnerability{}).Count(&stats.TotalVulns).Error; err != nil {
		return nil, fmt.Errorf("统计总漏洞数失败: %v", err)
	}

	return stats, nil
}

// GetTaskDetailedStatistics 获取扫描任务的详细统计信息
func (s *ScanService) GetTaskDetailedStatistics(taskID uint) (*ScanTaskStatistics, error) {
	// 获取任务基本信息
	task, err := s.GetTaskByID(taskID)
	if err != nil {
		return nil, fmt.Errorf("获取任务信息失败: %v", err)
	}

	stats := &ScanTaskStatistics{
		TaskID:       task.ID,
		TaskType:     task.Type,
		TaskName:     task.Name,
		StartTime:    task.StartTime,
		EndTime:      task.EndTime,
		ScanDuration: task.Duration,
	}

	// 根据任务类型计算不同的统计信息
	switch {
	case strings.Contains(task.Type, "discovery"):
		// 资产发现任务统计 (包括 network_discovery, subdomain_discovery 等)
		if err := s.calculateDiscoveryStatistics(taskID, stats); err != nil {
			return nil, fmt.Errorf("计算资产发现统计失败: %v", err)
		}
	case task.Type == "web" || task.Type == "network" || task.Type == "host" || task.Type == "api" || task.Type == "compliance":
		// 漏洞扫描任务统计
		if err := s.calculateVulnerabilityStatistics(taskID, stats); err != nil {
			return nil, fmt.Errorf("计算漏洞扫描统计失败: %v", err)
		}
	}

	// 从任务记录中获取扫描统计
	stats.TotalTargets = task.TotalTargets
	stats.TotalRequests = task.TotalRequests
	stats.SuccessRequests = task.SuccessRequests
	stats.FailedRequests = task.FailedRequests

	return stats, nil
}

// calculateDiscoveryStatistics 计算资产发现任务的统计信息
func (s *ScanService) calculateDiscoveryStatistics(taskID uint, stats *ScanTaskStatistics) error {
	// 获取任务信息
	var task models.ScanTask
	if err := s.db.Where("id = ?", taskID).First(&task).Error; err != nil {
		return fmt.Errorf("获取任务信息失败: %v", err)
	}

	// 从扫描日志中获取准确的发现资产数量
	// 优先从统计日志中获取，如果没有则从任务时间范围查询
	discoveredAssetsFromLog := 0
	hasStatsLog := false

	// 先尝试从统计日志中获取准确数字
	var logs []models.ScanLog
	if err := s.db.Where("task_id = ? AND stage = ? AND message LIKE ?", taskID, "统计信息", "%发现资产:%").Find(&logs).Error; err == nil {
		for _, log := range logs {
			if strings.Contains(log.Message, "发现资产:") {
				// 解析统计结果日志，例如："统计结果 - 扫描目标: 1, 唯一主机: 1, 开放端口: 3, Web服务: 2, 发现资产: 3"
				parts := strings.Split(log.Message, ",")
				for _, part := range parts {
					part = strings.TrimSpace(part)
					if strings.Contains(part, "发现资产:") {
						if numStr := extractNumber(part); numStr != "" {
							if num, err := strconv.Atoi(numStr); err == nil {
								discoveredAssetsFromLog = num
								hasStatsLog = true
								break
							}
						}
					}
				}
				if hasStatsLog {
					break
				}
			}
		}
	}

	if hasStatsLog {
		// 使用从日志中解析的准确数字
		stats.DiscoveredAssets = discoveredAssetsFromLog
	} else {
		// 如果没有统计日志，使用时间范围查询作为后备方案
		var discoveredAssets int64
		query := s.db.Model(&models.Asset{})

		// 使用更精确的时间范围：任务创建时间到任务更新时间
		query = query.Where("created_at >= ?", task.CreatedAt)
		if task.UpdatedAt.After(task.CreatedAt) {
			query = query.Where("created_at <= ?", task.UpdatedAt)
		} else {
			query = query.Where("created_at <= ?", time.Now())
		}

		if err := query.Count(&discoveredAssets).Error; err != nil {
			return fmt.Errorf("统计发现资产数失败: %v", err)
		}
		stats.DiscoveredAssets = int(discoveredAssets)
	}

	// 从扫描日志中分析目标统计信息
	var allLogs []models.ScanLog
	if err := s.db.Where("task_id = ?", taskID).Find(&allLogs).Error; err != nil {
		return fmt.Errorf("获取扫描日志失败: %v", err)
	}

	// 分析日志内容，提取统计信息
	uniqueHosts := make(map[string]bool)
	openPorts := make(map[string]bool)
	webServices := 0

	for _, log := range allLogs {
		// 从日志中提取主机信息
		if log.Target != "" {
			uniqueHosts[log.Target] = true
		}

		// 从日志消息中提取端口和服务信息
		if log.Level == "info" && log.Message != "" {
			// 解析不同类型的日志消息
			if strings.Contains(log.Message, "发现开放端口") {
				// 从消息中提取端口号，例如："发现开放端口: 80"
				if strings.Contains(log.Message, ":") {
					parts := strings.Split(log.Message, ":")
					if len(parts) > 1 {
						port := strings.TrimSpace(parts[1])
						openPorts[log.Target+":"+port] = true
					}
				}
			}
			if strings.Contains(log.Message, "Web服务") || strings.Contains(log.Message, "web") {
				webServices++
			}
			if strings.Contains(log.Message, "发现新子域名") || strings.Contains(log.Message, "发现新资产") {
				// 从目标中提取主机信息
				if log.Target != "" {
					uniqueHosts[log.Target] = true
				}
			}
		}
	}

	// 从统计日志中获取准确的统计信息
	for _, log := range allLogs {
		if log.Stage == "统计信息" && strings.Contains(log.Message, "统计结果") {
			// 解析统计结果日志，例如："统计结果 - 扫描目标: 2, 唯一主机: 3, 开放端口: 5, Web服务: 2, 发现资产: 4"
			if strings.Contains(log.Message, "唯一主机:") {
				// 使用正则表达式或字符串解析提取数字
				parts := strings.Split(log.Message, ",")
				for _, part := range parts {
					part = strings.TrimSpace(part)
					if strings.Contains(part, "唯一主机:") {
						if numStr := extractNumber(part); numStr != "" {
							if num, err := strconv.Atoi(numStr); err == nil {
								stats.UniqueHosts = num
							}
						}
					}
					if strings.Contains(part, "开放端口:") {
						if numStr := extractNumber(part); numStr != "" {
							if num, err := strconv.Atoi(numStr); err == nil {
								stats.OpenPorts = num
							}
						}
					}
					if strings.Contains(part, "Web服务:") {
						if numStr := extractNumber(part); numStr != "" {
							if num, err := strconv.Atoi(numStr); err == nil {
								stats.WebServices = num
							}
						}
					}
				}
			}
			break // 找到统计日志后退出
		}
	}

	// 如果没有从统计日志中获取到数据，使用分析的结果
	if stats.UniqueHosts == 0 {
		stats.UniqueHosts = len(uniqueHosts)
	}
	if stats.OpenPorts == 0 {
		stats.OpenPorts = len(openPorts)
	}
	if stats.WebServices == 0 {
		stats.WebServices = webServices
	}

	return nil
}

// calculateVulnerabilityStatistics 计算漏洞扫描任务的统计信息
func (s *ScanService) calculateVulnerabilityStatistics(taskID uint, stats *ScanTaskStatistics) error {
	// 统计该任务的漏洞数量
	var vulnerabilities []models.Vulnerability
	if err := s.db.Where("task_id = ?", taskID).Find(&vulnerabilities).Error; err != nil {
		return fmt.Errorf("获取漏洞列表失败: %v", err)
	}

	// 用于统计目标信息
	uniqueHosts := make(map[string]bool)
	uniquePorts := make(map[string]bool)
	webServiceCount := 0

	// 按严重程度分类统计，同时统计目标信息
	for _, vuln := range vulnerabilities {
		stats.TotalVulns++

		// 统计漏洞严重程度（不区分大小写）
		severity := strings.ToLower(vuln.Severity)
		switch severity {
		case "critical":
			stats.CriticalVulns++
		case "high":
			stats.HighVulns++
		case "medium":
			stats.MediumVulns++
		case "low":
			stats.LowVulns++
		case "info", "information":
			stats.InfoVulns++
		}

		// 统计目标信息
		// 从URL中提取主机信息
		if vuln.URL != "" {
			if u, err := url.Parse(vuln.URL); err == nil && u.Host != "" {
				uniqueHosts[u.Host] = true

				// 检查是否为Web服务
				if u.Scheme == "http" || u.Scheme == "https" {
					webServiceCount++
				}
			}
		}

		// 统计端口信息
		if vuln.Port > 0 {
			// 尝试从URL中获取主机，如果没有则使用默认
			host := "unknown"
			if vuln.URL != "" {
				if u, err := url.Parse(vuln.URL); err == nil && u.Host != "" {
					host = u.Hostname() // 只获取主机名，不包含端口
				}
			}
			portKey := fmt.Sprintf("%s:%d", host, vuln.Port)
			uniquePorts[portKey] = true
		}
	}

	// 设置目标统计信息
	stats.UniqueHosts = len(uniqueHosts)
	stats.OpenPorts = len(uniquePorts)
	stats.WebServices = webServiceCount

	// 如果没有从漏洞中获取到目标信息，可以考虑从扫描日志中获取
	// 但目前我们先使用基本的统计逻辑
	if stats.UniqueHosts == 0 && stats.OpenPorts == 0 {
		// 对于没有漏洞的任务，可以从任务配置中获取基本信息
		// 这里可以后续扩展
	}

	return nil
}

// extractNumber 从字符串中提取数字
func extractNumber(s string) string {
	var result strings.Builder
	for _, char := range s {
		if char >= '0' && char <= '9' {
			result.WriteRune(char)
		}
	}
	return result.String()
}

// GetTaskTargetInfo 获取扫描任务的目标信息详情
func (s *ScanService) GetTaskTargetInfo(taskID uint) (interface{}, error) {
	// 获取任务基本信息
	task, err := s.GetTaskByID(taskID)
	if err != nil {
		return nil, fmt.Errorf("获取任务信息失败: %v", err)
	}

	// 尝试从数据库获取信息收集数据
	if task.InfoGatheringData != "" {
		var infoData map[string]interface{}
		if err := json.Unmarshal([]byte(task.InfoGatheringData), &infoData); err == nil {
			return infoData, nil
		}
	}

	// 如果没有信息收集数据，返回错误让调用方生成模拟数据
	return nil, fmt.Errorf("任务 %d 暂无目标信息数据", taskID)
}
