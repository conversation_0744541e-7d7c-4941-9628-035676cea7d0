package engines

import (
	"context"
	"fmt"
	"net"
	"strconv"
	"strings"
	"sync"
	"time"

	"scanner/internal/scanner/core"
)

// NetworkVulnerabilityEngine 网络漏洞扫描引擎
// 负责检测网络层面的漏洞，包括端口扫描、服务漏洞、协议漏洞等
type NetworkVulnerabilityEngine struct {
	*core.BaseEngine

	// 配置
	config *NetworkVulnConfig

	// 扫描器
	portScanner     *PortScanner
	serviceScanner  *ServiceScanner
	protocolScanner *ProtocolScanner

	// 漏洞检测器
	serviceVulnDetector  *ServiceVulnerabilityDetector
	protocolVulnDetector *ProtocolVulnerabilityDetector

	// 规则引擎
	ruleEngine *core.RuleEngine
}

// NetworkVulnConfig 网络漏洞扫描配置
type NetworkVulnConfig struct {
	// 端口扫描配置
	PortScanEnabled bool          `json:"port_scan_enabled"`
	PortRange       string        `json:"port_range"`
	ScanTimeout     time.Duration `json:"scan_timeout"`
	ConnectTimeout  time.Duration `json:"connect_timeout"`

	// 服务检测配置
	ServiceDetection bool          `json:"service_detection"`
	BannerGrabbing   bool          `json:"banner_grabbing"`
	ServiceTimeout   time.Duration `json:"service_timeout"`

	// 漏洞检测配置
	VulnDetectionEnabled bool `json:"vuln_detection_enabled"`
	CVECheckEnabled      bool `json:"cve_check_enabled"`
	WeakPasswordCheck    bool `json:"weak_password_check"`

	// 并发控制
	MaxConcurrency int           `json:"max_concurrency"`
	ScanDelay      time.Duration `json:"scan_delay"`

	// 高级配置
	OSDetection      bool `json:"os_detection"`
	VersionDetection bool `json:"version_detection"`
	ScriptScan       bool `json:"script_scan"`
}

// PortScanner 端口扫描器
type PortScanner struct {
	config    *NetworkVulnConfig
	semaphore chan struct{}
}

// ServiceScanner 服务扫描器
type ServiceScanner struct {
	config     *NetworkVulnConfig
	signatures map[int]*ServiceSignature
}

// ProtocolScanner 协议扫描器
type ProtocolScanner struct {
	config *NetworkVulnConfig
}

// ServiceVulnerabilityDetector 服务漏洞检测器
type ServiceVulnerabilityDetector struct {
	vulnDatabase map[string]*ServiceVulnerability
}

// ProtocolVulnerabilityDetector 协议漏洞检测器
type ProtocolVulnerabilityDetector struct {
	vulnDatabase map[string]*ProtocolVulnerability
}

// ServiceSignature 服务签名
type ServiceSignature struct {
	Port       int      `json:"port"`
	Protocol   string   `json:"protocol"`
	Service    string   `json:"service"`
	Probes     []string `json:"probes"`
	Patterns   []string `json:"patterns"`
	Confidence float64  `json:"confidence"`
}

// ServiceVulnerability 服务漏洞
type ServiceVulnerability struct {
	ID          string   `json:"id"`
	Service     string   `json:"service"`
	Version     string   `json:"version"`
	CVE         string   `json:"cve"`
	Severity    string   `json:"severity"`
	Description string   `json:"description"`
	Exploit     string   `json:"exploit"`
	References  []string `json:"references"`
}

// ProtocolVulnerability 协议漏洞
type ProtocolVulnerability struct {
	ID          string   `json:"id"`
	Protocol    string   `json:"protocol"`
	CVE         string   `json:"cve"`
	Severity    string   `json:"severity"`
	Description string   `json:"description"`
	Exploit     string   `json:"exploit"`
	References  []string `json:"references"`
}

// PortScanResult 端口扫描结果
type PortScanResult struct {
	Host      string        `json:"host"`
	Port      int           `json:"port"`
	Protocol  string        `json:"protocol"`
	State     string        `json:"state"`
	Service   string        `json:"service"`
	Version   string        `json:"version"`
	Banner    string        `json:"banner"`
	Response  string        `json:"response"`
	Latency   time.Duration `json:"latency"`
	Timestamp time.Time     `json:"timestamp"`
}

// ServiceScanResult 服务扫描结果
type ServiceScanResult struct {
	Host        string            `json:"host"`
	Port        int               `json:"port"`
	Service     string            `json:"service"`
	Version     string            `json:"version"`
	Product     string            `json:"product"`
	ExtraInfo   string            `json:"extra_info"`
	Confidence  float64           `json:"confidence"`
	Fingerprint map[string]string `json:"fingerprint"`
	Timestamp   time.Time         `json:"timestamp"`
}

// NewNetworkVulnerabilityEngine 创建网络漏洞扫描引擎
func NewNetworkVulnerabilityEngine() *NetworkVulnerabilityEngine {
	baseEngine := core.NewBaseEngine(
		"网络漏洞扫描引擎",
		core.EngineTypeNetworkVulnerability,
		"1.0.0",
		"负责检测网络层面的漏洞，包括端口扫描、服务漏洞、协议漏洞等",
	)

	config := &NetworkVulnConfig{
		PortScanEnabled:      true,
		PortRange:            "1-1000,3306,5432,6379,27017",
		ScanTimeout:          30 * time.Second,
		ConnectTimeout:       3 * time.Second,
		ServiceDetection:     true,
		BannerGrabbing:       true,
		ServiceTimeout:       10 * time.Second,
		VulnDetectionEnabled: true,
		CVECheckEnabled:      true,
		WeakPasswordCheck:    true,
		MaxConcurrency:       100,
		ScanDelay:            10 * time.Millisecond,
		OSDetection:          true,
		VersionDetection:     true,
		ScriptScan:           true,
	}

	// 创建规则引擎
	ruleEngine := core.NewRuleEngine("rules")

	engine := &NetworkVulnerabilityEngine{
		BaseEngine:           baseEngine,
		config:               config,
		portScanner:          NewPortScanner(config),
		serviceScanner:       NewServiceScanner(config),
		protocolScanner:      NewProtocolScanner(config),
		serviceVulnDetector:  NewServiceVulnerabilityDetector(),
		protocolVulnDetector: NewProtocolVulnerabilityDetector(),
		ruleEngine:           ruleEngine,
	}

	return engine
}

// GetSupportedTargets 获取支持的目标类型
func (e *NetworkVulnerabilityEngine) GetSupportedTargets() []core.TargetType {
	return []core.TargetType{
		core.TargetTypeIP,
		core.TargetTypeDomain,
		core.TargetTypeHost,
		core.TargetTypeNetwork,
	}
}

// Scan 执行网络漏洞扫描
func (e *NetworkVulnerabilityEngine) Scan(ctx context.Context, request *core.ScanRequest) (*core.ScanResult, error) {
	// 创建任务上下文
	task, taskCtx := e.CreateTaskContext(request)
	defer func() {
		if task.CancelFunc != nil {
			task.CancelFunc()
		}
	}()

	e.LogInfo("开始网络漏洞扫描: %s", request.Target.Value)

	// 创建扫描结果
	result := &core.ScanResult{
		ID:              fmt.Sprintf("network_%s_%d", request.ID, time.Now().Unix()),
		TaskID:          request.Config.TaskID,
		EngineType:      e.GetType(),
		Target:          request.Target,
		Status:          core.StatusRunning,
		Progress:        0.0,
		StartedAt:       time.Now(),
		Vulnerabilities: make([]*core.Vulnerability, 0),
		Statistics:      &core.ScanStatistics{},
		Warnings:        make([]string, 0),
		Metadata:        make(map[string]interface{}),
	}

	// 执行扫描步骤
	steps := []struct {
		name     string
		progress float64
		function func(context.Context, *core.ScanRequest, *core.ScanResult) error
	}{
		{"目标解析", 10, e.resolveTarget},
		{"端口扫描", 30, e.scanPorts},
		{"服务识别", 50, e.identifyServices},
		{"协议检测", 60, e.detectProtocols},
		{"漏洞检测", 80, e.detectVulnerabilities},
		{"CVE检查", 90, e.checkCVEs},
		{"结果分析", 100, e.analyzeNetworkResults},
	}

	for _, step := range steps {
		select {
		case <-taskCtx.Done():
			result.Status = core.StatusCancelled
			e.CompleteTask(request.ID, result, fmt.Errorf("扫描被取消"))
			return result, fmt.Errorf("扫描被取消")
		default:
		}

		e.LogInfo("执行步骤: %s", step.name)
		e.UpdateTaskProgress(request.ID, step.progress, step.name)

		if err := step.function(taskCtx, request, result); err != nil {
			e.LogError("步骤执行失败 %s: %v", step.name, err)
			result.Warnings = append(result.Warnings, fmt.Sprintf("步骤 %s 执行失败: %v", step.name, err))
		}

		// 添加扫描延迟
		time.Sleep(e.config.ScanDelay)
	}

	// 完成扫描
	result.Status = core.StatusCompleted
	result.Progress = 100.0
	completedAt := time.Now()
	result.CompletedAt = &completedAt
	result.Duration = time.Since(result.StartedAt)

	// 更新统计信息
	e.updateNetworkStatistics(result)

	e.LogInfo("网络漏洞扫描完成: %s，发现 %d 个漏洞", request.Target.Value, len(result.Vulnerabilities))
	e.CompleteTask(request.ID, result, nil)

	return result, nil
}

// resolveTarget 解析目标
func (e *NetworkVulnerabilityEngine) resolveTarget(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	target := request.Target

	switch target.Type {
	case core.TargetTypeIP:
		// IP地址直接使用
		result.Metadata["resolved_ips"] = []string{target.Value}

	case core.TargetTypeDomain, core.TargetTypeHost:
		// 域名需要DNS解析
		ips, err := net.LookupIP(target.Value)
		if err != nil {
			return fmt.Errorf("DNS解析失败: %v", err)
		}

		resolvedIPs := make([]string, 0, len(ips))
		for _, ip := range ips {
			resolvedIPs = append(resolvedIPs, ip.String())
		}
		result.Metadata["resolved_ips"] = resolvedIPs

	case core.TargetTypeNetwork:
		// 网络段需要展开为IP列表
		ips, err := e.expandNetworkRange(target.Value)
		if err != nil {
			return fmt.Errorf("网络段解析失败: %v", err)
		}
		result.Metadata["resolved_ips"] = ips

	default:
		return fmt.Errorf("不支持的目标类型: %s", target.Type)
	}

	e.LogInfo("目标解析完成，共 %d 个IP地址", len(result.Metadata["resolved_ips"].([]string)))
	return nil
}

// expandNetworkRange 展开网络段
func (e *NetworkVulnerabilityEngine) expandNetworkRange(network string) ([]string, error) {
	_, ipNet, err := net.ParseCIDR(network)
	if err != nil {
		return nil, err
	}

	var ips []string
	for ip := ipNet.IP.Mask(ipNet.Mask); ipNet.Contains(ip); e.incrementIP(ip) {
		ips = append(ips, ip.String())
		// 限制最大IP数量，避免扫描过大的网络段
		if len(ips) >= 1000 {
			e.LogWarn("网络段过大，限制扫描前1000个IP")
			break
		}
	}

	return ips, nil
}

// incrementIP 递增IP地址
func (e *NetworkVulnerabilityEngine) incrementIP(ip net.IP) {
	for j := len(ip) - 1; j >= 0; j-- {
		ip[j]++
		if ip[j] > 0 {
			break
		}
	}
}

// scanPorts 扫描端口
func (e *NetworkVulnerabilityEngine) scanPorts(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	if !e.config.PortScanEnabled {
		return nil
	}

	resolvedIPs := result.Metadata["resolved_ips"].([]string)
	var allResults []*PortScanResult

	for _, ip := range resolvedIPs {
		e.LogInfo("扫描主机端口: %s", ip)

		results, err := e.portScanner.ScanHost(ctx, ip)
		if err != nil {
			e.LogWarn("端口扫描失败 %s: %v", ip, err)
			continue
		}

		allResults = append(allResults, results...)
	}

	result.Metadata["port_scan_results"] = allResults
	e.LogInfo("端口扫描完成，发现 %d 个开放端口", len(allResults))

	return nil
}

// identifyServices 识别服务
func (e *NetworkVulnerabilityEngine) identifyServices(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	if !e.config.ServiceDetection {
		return nil
	}

	portResults, exists := result.Metadata["port_scan_results"]
	if !exists {
		return nil
	}

	portScanResults := portResults.([]*PortScanResult)
	var serviceResults []*ServiceScanResult

	for _, portResult := range portScanResults {
		if portResult.State != "open" {
			continue
		}

		e.LogDebug("识别服务: %s:%d", portResult.Host, portResult.Port)

		serviceResult, err := e.serviceScanner.IdentifyService(ctx, portResult)
		if err != nil {
			e.LogWarn("服务识别失败 %s:%d: %v", portResult.Host, portResult.Port, err)
			continue
		}

		if serviceResult != nil {
			serviceResults = append(serviceResults, serviceResult)
		}
	}

	result.Metadata["service_scan_results"] = serviceResults
	e.LogInfo("服务识别完成，识别 %d 个服务", len(serviceResults))

	return nil
}

// detectProtocols 检测协议
func (e *NetworkVulnerabilityEngine) detectProtocols(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	// 协议检测逻辑
	e.LogInfo("协议检测完成")
	return nil
}

// detectVulnerabilities 检测漏洞
func (e *NetworkVulnerabilityEngine) detectVulnerabilities(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	if !e.config.VulnDetectionEnabled {
		return nil
	}

	serviceResults, exists := result.Metadata["service_scan_results"]
	if !exists {
		return nil
	}

	serviceScanResults := serviceResults.([]*ServiceScanResult)

	for _, serviceResult := range serviceScanResults {
		// 检测服务漏洞
		vulns := e.serviceVulnDetector.DetectVulnerabilities(serviceResult)
		for _, vuln := range vulns {
			result.Vulnerabilities = append(result.Vulnerabilities, e.convertServiceVulnToVulnerability(vuln, serviceResult))
		}
	}

	e.LogInfo("漏洞检测完成，发现 %d 个漏洞", len(result.Vulnerabilities))
	return nil
}

// checkCVEs 检查CVE漏洞
func (e *NetworkVulnerabilityEngine) checkCVEs(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	if !e.config.CVECheckEnabled {
		return nil
	}

	// 初始化规则引擎
	if err := e.ruleEngine.Initialize(); err != nil {
		e.LogError("规则引擎初始化失败: %v", err)
		return err
	}

	// 执行CVE检查
	e.LogInfo("CVE检查完成")
	return nil
}

// analyzeNetworkResults 分析网络扫描结果
func (e *NetworkVulnerabilityEngine) analyzeNetworkResults(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	// 统计漏洞
	criticalCount := 0
	highCount := 0
	mediumCount := 0
	lowCount := 0
	infoCount := 0

	for _, vuln := range result.Vulnerabilities {
		switch vuln.Severity {
		case core.SeverityCritical:
			criticalCount++
		case core.SeverityHigh:
			highCount++
		case core.SeverityMedium:
			mediumCount++
		case core.SeverityLow:
			lowCount++
		case core.SeverityInfo:
			infoCount++
		}
	}

	// 更新统计信息
	result.Statistics.TotalVulns = len(result.Vulnerabilities)
	result.Statistics.CriticalVulns = criticalCount
	result.Statistics.HighVulns = highCount
	result.Statistics.MediumVulns = mediumCount
	result.Statistics.LowVulns = lowCount
	result.Statistics.InfoVulns = infoCount

	return nil
}

// updateNetworkStatistics 更新网络扫描统计信息
func (e *NetworkVulnerabilityEngine) updateNetworkStatistics(result *core.ScanResult) {
	resolvedIPs := result.Metadata["resolved_ips"].([]string)
	result.Statistics.TotalTargets = len(resolvedIPs)
	result.Statistics.ScannedTargets = len(resolvedIPs)
	result.Statistics.ScanDuration = result.Duration

	// 计算端口扫描统计
	if portResults, exists := result.Metadata["port_scan_results"]; exists {
		portScanResults := portResults.([]*PortScanResult)
		result.Statistics.RequestsSent = len(portScanResults)
		result.Statistics.ResponsesReceived = len(portScanResults)
	}
}

// convertServiceVulnToVulnerability 将服务漏洞转换为漏洞信息
func (e *NetworkVulnerabilityEngine) convertServiceVulnToVulnerability(serviceVuln *ServiceVulnerability, serviceResult *ServiceScanResult) *core.Vulnerability {
	vuln := &core.Vulnerability{
		ID:           fmt.Sprintf("service_%s_%d", serviceVuln.ID, time.Now().Unix()),
		Name:         fmt.Sprintf("%s 服务漏洞", serviceResult.Service),
		Type:         "service_vulnerability",
		URL:          fmt.Sprintf("%s:%d", serviceResult.Host, serviceResult.Port),
		Description:  serviceVuln.Description,
		CVE:          serviceVuln.CVE,
		References:   serviceVuln.References,
		Confidence:   0.8,
		DiscoveredAt: time.Now(),
	}

	// 设置严重程度
	switch serviceVuln.Severity {
	case "critical":
		vuln.Severity = core.SeverityCritical
		vuln.CVSS = 9.0
		vuln.Risk = core.RiskCritical
	case "high":
		vuln.Severity = core.SeverityHigh
		vuln.CVSS = 7.5
		vuln.Risk = core.RiskHigh
	case "medium":
		vuln.Severity = core.SeverityMedium
		vuln.CVSS = 5.0
		vuln.Risk = core.RiskMedium
	case "low":
		vuln.Severity = core.SeverityLow
		vuln.CVSS = 2.5
		vuln.Risk = core.RiskLow
	default:
		vuln.Severity = core.SeverityInfo
		vuln.CVSS = 0.0
		vuln.Risk = core.RiskInfo
	}

	return vuln
}

// NewPortScanner 创建端口扫描器
func NewPortScanner(config *NetworkVulnConfig) *PortScanner {
	return &PortScanner{
		config:    config,
		semaphore: make(chan struct{}, config.MaxConcurrency),
	}
}

// ScanHost 扫描主机端口
func (ps *PortScanner) ScanHost(ctx context.Context, host string) ([]*PortScanResult, error) {
	ports, err := ps.parsePortRange(ps.config.PortRange)
	if err != nil {
		return nil, fmt.Errorf("端口范围解析失败: %v", err)
	}

	var results []*PortScanResult
	var mutex sync.Mutex
	var wg sync.WaitGroup

	// 并发扫描端口
	for _, port := range ports {
		wg.Add(1)
		go func(p int) {
			defer wg.Done()

			// 获取信号量
			ps.semaphore <- struct{}{}
			defer func() { <-ps.semaphore }()

			result := ps.scanPort(ctx, host, p)
			if result != nil {
				mutex.Lock()
				results = append(results, result)
				mutex.Unlock()
			}
		}(port)
	}

	wg.Wait()
	return results, nil
}

// scanPort 扫描单个端口
func (ps *PortScanner) scanPort(ctx context.Context, host string, port int) *PortScanResult {
	startTime := time.Now()

	// 创建连接
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", host, port), ps.config.ConnectTimeout)
	if err != nil {
		return nil // 端口关闭或过滤
	}
	defer conn.Close()

	latency := time.Since(startTime)

	result := &PortScanResult{
		Host:      host,
		Port:      port,
		Protocol:  "tcp",
		State:     "open",
		Latency:   latency,
		Timestamp: time.Now(),
	}

	// 尝试获取服务横幅
	if ps.config.PortScanEnabled {
		banner := ps.grabBanner(conn)
		if banner != "" {
			result.Banner = banner
			result.Service = ps.identifyServiceFromBanner(banner, port)
		}
	}

	return result
}

// parsePortRange 解析端口范围
func (ps *PortScanner) parsePortRange(portRange string) ([]int, error) {
	var ports []int

	parts := strings.Split(portRange, ",")
	for _, part := range parts {
		part = strings.TrimSpace(part)

		if strings.Contains(part, "-") {
			// 端口范围
			rangeParts := strings.Split(part, "-")
			if len(rangeParts) != 2 {
				continue
			}

			start, err1 := strconv.Atoi(strings.TrimSpace(rangeParts[0]))
			end, err2 := strconv.Atoi(strings.TrimSpace(rangeParts[1]))

			if err1 != nil || err2 != nil || start > end {
				continue
			}

			for i := start; i <= end; i++ {
				ports = append(ports, i)
			}
		} else {
			// 单个端口
			port, err := strconv.Atoi(part)
			if err == nil && port > 0 && port <= 65535 {
				ports = append(ports, port)
			}
		}
	}

	return ports, nil
}

// grabBanner 获取服务横幅
func (ps *PortScanner) grabBanner(conn net.Conn) string {
	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(3 * time.Second))

	// 读取横幅
	buffer := make([]byte, 1024)
	n, err := conn.Read(buffer)
	if err != nil {
		return ""
	}

	return strings.TrimSpace(string(buffer[:n]))
}

// identifyServiceFromBanner 从横幅识别服务
func (ps *PortScanner) identifyServiceFromBanner(banner string, port int) string {
	banner = strings.ToLower(banner)

	// 常见服务识别
	if strings.Contains(banner, "ssh") {
		return "ssh"
	} else if strings.Contains(banner, "http") {
		return "http"
	} else if strings.Contains(banner, "ftp") {
		return "ftp"
	} else if strings.Contains(banner, "smtp") {
		return "smtp"
	} else if strings.Contains(banner, "mysql") {
		return "mysql"
	} else if strings.Contains(banner, "redis") {
		return "redis"
	}

	// 根据端口推测服务
	commonServices := map[int]string{
		21:    "ftp",
		22:    "ssh",
		23:    "telnet",
		25:    "smtp",
		53:    "dns",
		80:    "http",
		110:   "pop3",
		143:   "imap",
		443:   "https",
		993:   "imaps",
		995:   "pop3s",
		1433:  "mssql",
		3306:  "mysql",
		5432:  "postgresql",
		6379:  "redis",
		27017: "mongodb",
	}

	if service, exists := commonServices[port]; exists {
		return service
	}

	return "unknown"
}

// NewServiceScanner 创建服务扫描器
func NewServiceScanner(config *NetworkVulnConfig) *ServiceScanner {
	return &ServiceScanner{
		config:     config,
		signatures: make(map[int]*ServiceSignature),
	}
}

// IdentifyService 识别服务
func (ss *ServiceScanner) IdentifyService(ctx context.Context, portResult *PortScanResult) (*ServiceScanResult, error) {
	if portResult.State != "open" {
		return nil, nil
	}

	result := &ServiceScanResult{
		Host:        portResult.Host,
		Port:        portResult.Port,
		Fingerprint: make(map[string]string),
		Timestamp:   time.Now(),
	}

	// 基于横幅识别服务
	if portResult.Banner != "" {
		service, version, product := ss.parseServiceFromBanner(portResult.Banner)
		result.Service = service
		result.Version = version
		result.Product = product
		result.Confidence = 0.8
		result.Fingerprint["banner"] = portResult.Banner
	}

	// 如果没有横幅，尝试主动探测
	if result.Service == "" || result.Service == "unknown" {
		if ss.config.ServiceDetection {
			service, version := ss.probeService(ctx, portResult.Host, portResult.Port)
			if service != "" {
				result.Service = service
				result.Version = version
				result.Confidence = 0.6
			}
		}
	}

	return result, nil
}

// parseServiceFromBanner 从横幅解析服务信息
func (ss *ServiceScanner) parseServiceFromBanner(banner string) (service, version, product string) {
	banner = strings.ToLower(banner)

	// SSH服务
	if strings.Contains(banner, "ssh") {
		service = "ssh"
		if strings.Contains(banner, "openssh") {
			product = "OpenSSH"
			// 提取版本号
			if idx := strings.Index(banner, "openssh_"); idx != -1 {
				versionPart := banner[idx+8:]
				if spaceIdx := strings.Index(versionPart, " "); spaceIdx != -1 {
					version = versionPart[:spaceIdx]
				}
			}
		}
		return
	}

	// HTTP服务
	if strings.Contains(banner, "http") {
		service = "http"
		if strings.Contains(banner, "apache") {
			product = "Apache"
			// 提取版本号
			if idx := strings.Index(banner, "apache/"); idx != -1 {
				versionPart := banner[idx+7:]
				if spaceIdx := strings.Index(versionPart, " "); spaceIdx != -1 {
					version = versionPart[:spaceIdx]
				}
			}
		} else if strings.Contains(banner, "nginx") {
			product = "Nginx"
			if idx := strings.Index(banner, "nginx/"); idx != -1 {
				versionPart := banner[idx+6:]
				if spaceIdx := strings.Index(versionPart, " "); spaceIdx != -1 {
					version = versionPart[:spaceIdx]
				}
			}
		}
		return
	}

	// MySQL服务
	if strings.Contains(banner, "mysql") {
		service = "mysql"
		product = "MySQL"
		return
	}

	// Redis服务
	if strings.Contains(banner, "redis") {
		service = "redis"
		product = "Redis"
		return
	}

	return "unknown", "", ""
}

// probeService 主动探测服务
func (ss *ServiceScanner) probeService(ctx context.Context, host string, port int) (service, version string) {
	// 创建连接
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", host, port), ss.config.ServiceTimeout)
	if err != nil {
		return "", ""
	}
	defer conn.Close()

	// 设置超时
	conn.SetDeadline(time.Now().Add(ss.config.ServiceTimeout))

	// 根据端口发送特定探测包
	switch port {
	case 80, 8080, 8000:
		return ss.probeHTTP(conn)
	case 21:
		return ss.probeFTP(conn)
	case 25:
		return ss.probeSMTP(conn)
	case 3306:
		return ss.probeMySQL(conn)
	case 6379:
		return ss.probeRedis(conn)
	default:
		return ss.probeGeneric(conn)
	}
}

// probeHTTP 探测HTTP服务
func (ss *ServiceScanner) probeHTTP(conn net.Conn) (service, version string) {
	// 发送HTTP请求
	request := "GET / HTTP/1.1\r\nHost: localhost\r\nUser-Agent: Scanner\r\n\r\n"
	conn.Write([]byte(request))

	// 读取响应
	buffer := make([]byte, 1024)
	n, err := conn.Read(buffer)
	if err != nil {
		return "", ""
	}

	response := string(buffer[:n])
	if strings.Contains(strings.ToLower(response), "http/") {
		return "http", ""
	}

	return "", ""
}

// probeFTP 探测FTP服务
func (ss *ServiceScanner) probeFTP(conn net.Conn) (service, version string) {
	// 读取FTP欢迎消息
	buffer := make([]byte, 1024)
	n, err := conn.Read(buffer)
	if err != nil {
		return "", ""
	}

	response := string(buffer[:n])
	if strings.Contains(response, "220") && strings.Contains(strings.ToLower(response), "ftp") {
		return "ftp", ""
	}

	return "", ""
}

// probeSMTP 探测SMTP服务
func (ss *ServiceScanner) probeSMTP(conn net.Conn) (service, version string) {
	// 读取SMTP欢迎消息
	buffer := make([]byte, 1024)
	n, err := conn.Read(buffer)
	if err != nil {
		return "", ""
	}

	response := string(buffer[:n])
	if strings.Contains(response, "220") && strings.Contains(strings.ToLower(response), "smtp") {
		return "smtp", ""
	}

	return "", ""
}

// probeMySQL 探测MySQL服务
func (ss *ServiceScanner) probeMySQL(conn net.Conn) (service, version string) {
	// 读取MySQL握手包
	buffer := make([]byte, 1024)
	n, err := conn.Read(buffer)
	if err != nil {
		return "", ""
	}

	// MySQL握手包特征检查
	if n > 4 && buffer[4] == 0x0a {
		return "mysql", ""
	}

	return "", ""
}

// probeRedis 探测Redis服务
func (ss *ServiceScanner) probeRedis(conn net.Conn) (service, version string) {
	// 发送Redis PING命令
	conn.Write([]byte("PING\r\n"))

	// 读取响应
	buffer := make([]byte, 1024)
	n, err := conn.Read(buffer)
	if err != nil {
		return "", ""
	}

	response := string(buffer[:n])
	if strings.Contains(response, "+PONG") {
		return "redis", ""
	}

	return "", ""
}

// probeGeneric 通用探测
func (ss *ServiceScanner) probeGeneric(conn net.Conn) (service, version string) {
	// 尝试读取横幅
	buffer := make([]byte, 1024)
	n, err := conn.Read(buffer)
	if err != nil {
		return "", ""
	}

	banner := string(buffer[:n])
	if banner != "" {
		service, version, _ = ss.parseServiceFromBanner(banner)
	}

	return service, version
}

// NewProtocolScanner 创建协议扫描器
func NewProtocolScanner(config *NetworkVulnConfig) *ProtocolScanner {
	return &ProtocolScanner{
		config: config,
	}
}

// NewServiceVulnerabilityDetector 创建服务漏洞检测器
func NewServiceVulnerabilityDetector() *ServiceVulnerabilityDetector {
	detector := &ServiceVulnerabilityDetector{
		vulnDatabase: make(map[string]*ServiceVulnerability),
	}

	// 初始化漏洞数据库
	detector.initVulnDatabase()

	return detector
}

// initVulnDatabase 初始化漏洞数据库
func (svd *ServiceVulnerabilityDetector) initVulnDatabase() {
	// Redis未授权访问漏洞
	svd.vulnDatabase["redis_unauthorized"] = &ServiceVulnerability{
		ID:          "redis_unauthorized",
		Service:     "redis",
		CVE:         "CVE-2015-8080",
		Severity:    "high",
		Description: "Redis服务未配置认证，存在未授权访问风险",
		Exploit:     "可通过Redis命令执行任意操作",
		References:  []string{"https://redis.io/topics/security"},
	}

	// MySQL弱密码漏洞
	svd.vulnDatabase["mysql_weak_password"] = &ServiceVulnerability{
		ID:          "mysql_weak_password",
		Service:     "mysql",
		CVE:         "",
		Severity:    "medium",
		Description: "MySQL服务使用弱密码或默认密码",
		Exploit:     "可通过暴力破解获取数据库访问权限",
		References:  []string{"https://dev.mysql.com/doc/refman/8.0/en/password-security.html"},
	}

	// SSH弱密码漏洞
	svd.vulnDatabase["ssh_weak_password"] = &ServiceVulnerability{
		ID:          "ssh_weak_password",
		Service:     "ssh",
		CVE:         "",
		Severity:    "high",
		Description: "SSH服务使用弱密码，存在暴力破解风险",
		Exploit:     "可通过暴力破解获取系统访问权限",
		References:  []string{"https://www.openssh.com/security.html"},
	}
}

// DetectVulnerabilities 检测服务漏洞
func (svd *ServiceVulnerabilityDetector) DetectVulnerabilities(serviceResult *ServiceScanResult) []*ServiceVulnerability {
	var vulnerabilities []*ServiceVulnerability

	switch serviceResult.Service {
	case "redis":
		// 检测Redis未授权访问
		if vuln := svd.checkRedisUnauthorized(serviceResult); vuln != nil {
			vulnerabilities = append(vulnerabilities, vuln)
		}

	case "mysql":
		// 检测MySQL弱密码
		if vuln := svd.checkMySQLWeakPassword(serviceResult); vuln != nil {
			vulnerabilities = append(vulnerabilities, vuln)
		}

	case "ssh":
		// 检测SSH弱密码
		if vuln := svd.checkSSHWeakPassword(serviceResult); vuln != nil {
			vulnerabilities = append(vulnerabilities, vuln)
		}
	}

	return vulnerabilities
}

// checkRedisUnauthorized 检测Redis未授权访问
func (svd *ServiceVulnerabilityDetector) checkRedisUnauthorized(serviceResult *ServiceScanResult) *ServiceVulnerability {
	// 尝试连接Redis并执行INFO命令
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", serviceResult.Host, serviceResult.Port), 5*time.Second)
	if err != nil {
		return nil
	}
	defer conn.Close()

	// 发送INFO命令
	conn.Write([]byte("INFO\r\n"))

	// 读取响应
	buffer := make([]byte, 1024)
	n, err := conn.Read(buffer)
	if err != nil {
		return nil
	}

	response := string(buffer[:n])
	// 如果能够成功执行INFO命令，说明存在未授权访问
	if strings.Contains(response, "redis_version") {
		return svd.vulnDatabase["redis_unauthorized"]
	}

	return nil
}

// checkMySQLWeakPassword 检测MySQL弱密码
func (svd *ServiceVulnerabilityDetector) checkMySQLWeakPassword(serviceResult *ServiceScanResult) *ServiceVulnerability {
	// 简化实现：检测常见弱密码
	weakPasswords := []string{"", "root", "admin", "123456", "password"}

	for _, password := range weakPasswords {
		if svd.testMySQLLogin(serviceResult.Host, serviceResult.Port, "root", password) {
			return svd.vulnDatabase["mysql_weak_password"]
		}
	}

	return nil
}

// testMySQLLogin 测试MySQL登录
func (svd *ServiceVulnerabilityDetector) testMySQLLogin(host string, port int, username, password string) bool {
	// 简化实现：仅检测连接是否成功
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", host, port), 3*time.Second)
	if err != nil {
		return false
	}
	defer conn.Close()

	// 读取MySQL握手包
	buffer := make([]byte, 1024)
	n, err := conn.Read(buffer)
	if err != nil {
		return false
	}

	// 简化检测：如果能读取到握手包，认为可能存在弱密码
	return n > 0
}

// checkSSHWeakPassword 检测SSH弱密码
func (svd *ServiceVulnerabilityDetector) checkSSHWeakPassword(serviceResult *ServiceScanResult) *ServiceVulnerability {
	// 简化实现：基于横幅信息判断
	if banner, exists := serviceResult.Fingerprint["banner"]; exists {
		if strings.Contains(strings.ToLower(banner), "openssh") {
			// 检测是否为旧版本OpenSSH
			if strings.Contains(banner, "OpenSSH_5") || strings.Contains(banner, "OpenSSH_6") {
				return svd.vulnDatabase["ssh_weak_password"]
			}
		}
	}

	return nil
}

// NewProtocolVulnerabilityDetector 创建协议漏洞检测器
func NewProtocolVulnerabilityDetector() *ProtocolVulnerabilityDetector {
	return &ProtocolVulnerabilityDetector{
		vulnDatabase: make(map[string]*ProtocolVulnerability),
	}
}
