package core

import (
	"time"
)

// TargetInformation 目标基础信息
type TargetInformation struct {
	// 基础信息
	URL         string            `json:"url"`         // 目标URL
	Domain      string            `json:"domain"`      // 域名
	IP          string            `json:"ip"`          // IP地址
	Port        int               `json:"port"`        // 端口
	Protocol    string            `json:"protocol"`    // 协议
	
	// 响应信息
	StatusCode  int               `json:"status_code"` // HTTP状态码
	Title       string            `json:"title"`       // 页面标题
	Server      string            `json:"server"`      // 服务器信息
	ContentType string            `json:"content_type"` // 内容类型
	
	// 时间信息
	ResponseTime time.Duration    `json:"response_time"` // 响应时间
	LastSeen     time.Time        `json:"last_seen"`     // 最后访问时间
	
	// 元数据
	Headers     map[string]string `json:"headers"`     // HTTP头
	Cookies     []*CookieInfo     `json:"cookies"`     // Cookie信息
	Metadata    map[string]string `json:"metadata"`    // 其他元数据
}

// TechnologyStack 技术栈信息
type TechnologyStack struct {
	// Web服务器
	WebServer     *ComponentInfo    `json:"web_server"`     // Web服务器
	
	// 编程语言和框架
	Language      *ComponentInfo    `json:"language"`       // 编程语言
	Framework     *ComponentInfo    `json:"framework"`      // 框架
	CMS           *ComponentInfo    `json:"cms"`            // 内容管理系统
	
	// 数据库
	Database      *ComponentInfo    `json:"database"`       // 数据库
	
	// 前端技术
	JavaScript    []*ComponentInfo  `json:"javascript"`     // JavaScript库
	CSS           []*ComponentInfo  `json:"css"`            // CSS框架
	
	// 中间件和服务
	Middleware    []*ComponentInfo  `json:"middleware"`     // 中间件
	CDN           *ComponentInfo    `json:"cdn"`            // CDN服务
	CloudService  *ComponentInfo    `json:"cloud_service"`  // 云服务
	
	// 安全组件
	WAF           *ComponentInfo    `json:"waf"`            // Web应用防火墙
	LoadBalancer  *ComponentInfo    `json:"load_balancer"`  // 负载均衡器
	
	// 其他组件
	Analytics     []*ComponentInfo  `json:"analytics"`      // 分析工具
	Advertising   []*ComponentInfo  `json:"advertising"`    // 广告服务
	Widgets       []*ComponentInfo  `json:"widgets"`        // 小部件
}

// ComponentInfo 组件信息
type ComponentInfo struct {
	Name         string            `json:"name"`         // 组件名称
	Version      string            `json:"version"`      // 版本号
	Category     string            `json:"category"`     // 组件类别
	Confidence   float64           `json:"confidence"`   // 识别置信度
	Evidence     []string          `json:"evidence"`     // 识别证据
	Website      string            `json:"website"`      // 官方网站
	Description  string            `json:"description"`  // 组件描述
	Icon         string            `json:"icon"`         // 图标URL
	CPE          string            `json:"cpe"`          // CPE标识
	Metadata     map[string]string `json:"metadata"`     // 元数据
}

// ServiceInfo 服务信息
type ServiceInfo struct {
	// 基础信息
	Name        string            `json:"name"`        // 服务名称
	Port        int               `json:"port"`        // 端口号
	Protocol    string            `json:"protocol"`    // 协议
	State       string            `json:"state"`       // 状态
	
	// 版本信息
	Product     string            `json:"product"`     // 产品名称
	Version     string            `json:"version"`     // 版本号
	ExtraInfo   string            `json:"extra_info"`  // 额外信息
	
	// 指纹信息
	Banner      string            `json:"banner"`      // 服务横幅
	Fingerprint string            `json:"fingerprint"` // 服务指纹
	
	// 安全信息
	IsSecure    bool              `json:"is_secure"`   // 是否安全
	HasAuth     bool              `json:"has_auth"`    // 是否需要认证
	
	// 元数据
	Confidence  float64           `json:"confidence"`  // 识别置信度
	Methods     []string          `json:"methods"`     // 支持的方法
	Metadata    map[string]string `json:"metadata"`    // 元数据
}

// NetworkInformation 网络信息
type NetworkInformation struct {
	// IP信息
	IPAddress    string            `json:"ip_address"`    // IP地址
	IPVersion    string            `json:"ip_version"`    // IP版本
	ASN          string            `json:"asn"`           // ASN号码
	ISP          string            `json:"isp"`           // ISP提供商
	Organization string            `json:"organization"`  // 组织
	Country      string            `json:"country"`       // 国家
	Region       string            `json:"region"`        // 地区
	City         string            `json:"city"`          // 城市
	
	// 网络拓扑
	Gateway      string            `json:"gateway"`       // 网关
	Netmask      string            `json:"netmask"`       // 子网掩码
	Network      string            `json:"network"`       // 网络段
	
	// 端口信息
	OpenPorts    []int             `json:"open_ports"`    // 开放端口
	ClosedPorts  []int             `json:"closed_ports"`  // 关闭端口
	FilteredPorts []int            `json:"filtered_ports"` // 过滤端口
	
	// 路由信息
	Traceroute   []*HopInfo        `json:"traceroute"`    // 路由跟踪
	RTT          time.Duration     `json:"rtt"`           // 往返时间
	
	// 元数据
	Metadata     map[string]string `json:"metadata"`      // 元数据
}

// HopInfo 跳跃信息
type HopInfo struct {
	Hop      int           `json:"hop"`      // 跳数
	IP       string        `json:"ip"`       // IP地址
	Hostname string        `json:"hostname"` // 主机名
	RTT      time.Duration `json:"rtt"`      // 往返时间
}

// DNSInformation DNS信息
type DNSInformation struct {
	// 基础记录
	ARecords     []string          `json:"a_records"`     // A记录
	AAAARecords  []string          `json:"aaaa_records"`  // AAAA记录
	CNAMERecords []string          `json:"cname_records"` // CNAME记录
	MXRecords    []*MXRecord       `json:"mx_records"`    // MX记录
	NSRecords    []string          `json:"ns_records"`    // NS记录
	TXTRecords   []string          `json:"txt_records"`   // TXT记录
	SOARecord    *SOARecord        `json:"soa_record"`    // SOA记录
	
	// 安全记录
	SPFRecord    string            `json:"spf_record"`    // SPF记录
	DMARCRecord  string            `json:"dmarc_record"`  // DMARC记录
	DKIMRecords  []string          `json:"dkim_records"`  // DKIM记录
	
	// 子域名
	Subdomains   []string          `json:"subdomains"`    // 子域名列表
	
	// 元数据
	Metadata     map[string]string `json:"metadata"`      // 元数据
}

// MXRecord MX记录
type MXRecord struct {
	Priority int    `json:"priority"` // 优先级
	Host     string `json:"host"`     // 主机名
}

// SOARecord SOA记录
type SOARecord struct {
	PrimaryNS   string `json:"primary_ns"`   // 主域名服务器
	AdminEmail  string `json:"admin_email"`  // 管理员邮箱
	Serial      uint32 `json:"serial"`       // 序列号
	Refresh     uint32 `json:"refresh"`      // 刷新间隔
	Retry       uint32 `json:"retry"`        // 重试间隔
	Expire      uint32 `json:"expire"`       // 过期时间
	MinTTL      uint32 `json:"min_ttl"`      // 最小TTL
}

// SecurityInformation 安全信息
type SecurityInformation struct {
	// 安全头
	SecurityHeaders  *SecurityHeaders  `json:"security_headers"`  // 安全头
	
	// 认证信息
	AuthMethods      []string          `json:"auth_methods"`      // 认证方法
	LoginForms       []*FormInfo       `json:"login_forms"`       // 登录表单
	
	// 防护措施
	WAFDetected      bool              `json:"waf_detected"`      // WAF检测
	WAFInfo          *WAFInfo          `json:"waf_info"`          // WAF信息
	RateLimiting     bool              `json:"rate_limiting"`     // 速率限制
	
	// 漏洞信息
	KnownVulns       []*KnownVuln      `json:"known_vulns"`       // 已知漏洞
	SecurityIssues   []*SecurityIssue  `json:"security_issues"`   // 安全问题
	
	// 合规性
	ComplianceStatus *ComplianceStatus `json:"compliance_status"` // 合规状态
	
	// 元数据
	Metadata         map[string]string `json:"metadata"`          // 元数据
}

// SecurityHeaders 安全头信息
type SecurityHeaders struct {
	HSTS                 *HSTSInfo         `json:"hsts"`                   // HSTS
	CSP                  *CSPInfo          `json:"csp"`                    // CSP
	XFrameOptions        string            `json:"x_frame_options"`        // X-Frame-Options
	XContentTypeOptions  string            `json:"x_content_type_options"` // X-Content-Type-Options
	XSSProtection        string            `json:"xss_protection"`         // X-XSS-Protection
	ReferrerPolicy       string            `json:"referrer_policy"`        // Referrer-Policy
	PermissionsPolicy    string            `json:"permissions_policy"`     // Permissions-Policy
	ExpectCT             string            `json:"expect_ct"`              // Expect-CT
}

// HSTSInfo HSTS信息
type HSTSInfo struct {
	Enabled         bool   `json:"enabled"`          // 是否启用
	MaxAge          int    `json:"max_age"`          // 最大年龄
	IncludeSubdomains bool `json:"include_subdomains"` // 包含子域名
	Preload         bool   `json:"preload"`          // 预加载
}

// CSPInfo CSP信息
type CSPInfo struct {
	Enabled     bool     `json:"enabled"`     // 是否启用
	Policy      string   `json:"policy"`      // 策略内容
	Directives  []string `json:"directives"`  // 指令列表
	ReportOnly  bool     `json:"report_only"` // 仅报告模式
}

// FormInfo 表单信息
type FormInfo struct {
	Action   string            `json:"action"`   // 表单动作
	Method   string            `json:"method"`   // 提交方法
	Fields   []*FieldInfo      `json:"fields"`   // 表单字段
	HasCSRF  bool              `json:"has_csrf"` // 是否有CSRF保护
	Metadata map[string]string `json:"metadata"` // 元数据
}

// FieldInfo 字段信息
type FieldInfo struct {
	Name        string `json:"name"`        // 字段名称
	Type        string `json:"type"`        // 字段类型
	Value       string `json:"value"`       // 默认值
	Required    bool   `json:"required"`    // 是否必填
	Placeholder string `json:"placeholder"` // 占位符
}

// WAFInfo WAF信息
type WAFInfo struct {
	Vendor      string  `json:"vendor"`      // 厂商
	Product     string  `json:"product"`     // 产品
	Version     string  `json:"version"`     // 版本
	Confidence  float64 `json:"confidence"`  // 置信度
	Evidence    []string `json:"evidence"`   // 证据
}

// KnownVuln 已知漏洞
type KnownVuln struct {
	CVE         string  `json:"cve"`         // CVE编号
	CVSS        float64 `json:"cvss"`        // CVSS评分
	Severity    string  `json:"severity"`    // 严重程度
	Description string  `json:"description"` // 描述
	References  []string `json:"references"` // 参考链接
}

// SecurityIssue 安全问题
type SecurityIssue struct {
	Type        string  `json:"type"`        // 问题类型
	Severity    string  `json:"severity"`    // 严重程度
	Description string  `json:"description"` // 描述
	Location    string  `json:"location"`    // 位置
	Evidence    string  `json:"evidence"`    // 证据
	Solution    string  `json:"solution"`    // 解决方案
}

// ComplianceStatus 合规状态
type ComplianceStatus struct {
	OWASP       *ComplianceResult `json:"owasp"`       // OWASP合规
	PCI         *ComplianceResult `json:"pci"`         // PCI合规
	GDPR        *ComplianceResult `json:"gdpr"`        // GDPR合规
	ISO27001    *ComplianceResult `json:"iso27001"`    // ISO27001合规
}

// ComplianceResult 合规结果
type ComplianceResult struct {
	Status      string   `json:"status"`      // 合规状态
	Score       float64  `json:"score"`       // 合规评分
	PassedTests int      `json:"passed_tests"` // 通过测试数
	TotalTests  int      `json:"total_tests"`  // 总测试数
	Issues      []string `json:"issues"`      // 问题列表
}

// CertificateInfo 证书信息
type CertificateInfo struct {
	// 基础信息
	Subject         string    `json:"subject"`          // 主题
	Issuer          string    `json:"issuer"`           // 颁发者
	SerialNumber    string    `json:"serial_number"`    // 序列号
	
	// 时间信息
	NotBefore       time.Time `json:"not_before"`       // 生效时间
	NotAfter        time.Time `json:"not_after"`        // 过期时间
	
	// 算法信息
	SignatureAlgorithm string `json:"signature_algorithm"` // 签名算法
	PublicKeyAlgorithm string `json:"public_key_algorithm"` // 公钥算法
	KeySize            int    `json:"key_size"`            // 密钥长度
	
	// 扩展信息
	SANs            []string `json:"sans"`             // 主题备用名称
	KeyUsage        []string `json:"key_usage"`        // 密钥用途
	ExtKeyUsage     []string `json:"ext_key_usage"`    // 扩展密钥用途
	
	// 验证信息
	IsValid         bool     `json:"is_valid"`         // 是否有效
	IsSelfSigned    bool     `json:"is_self_signed"`   // 是否自签名
	IsExpired       bool     `json:"is_expired"`       // 是否过期
	TrustChain      []string `json:"trust_chain"`      // 信任链
	
	// 元数据
	Metadata        map[string]string `json:"metadata"` // 元数据
}

// ContentInformation 内容信息
type ContentInformation struct {
	// 页面信息
	Title           string            `json:"title"`           // 页面标题
	Description     string            `json:"description"`     // 页面描述
	Keywords        []string          `json:"keywords"`        // 关键词
	Language        string            `json:"language"`        // 语言
	
	// 内容统计
	ContentLength   int64             `json:"content_length"`  // 内容长度
	WordCount       int               `json:"word_count"`      // 单词数
	LinkCount       int               `json:"link_count"`      // 链接数
	ImageCount      int               `json:"image_count"`     // 图片数
	
	// 链接信息
	InternalLinks   []string          `json:"internal_links"`  // 内部链接
	ExternalLinks   []string          `json:"external_links"`  // 外部链接
	
	// 媒体信息
	Images          []*MediaInfo      `json:"images"`          // 图片信息
	Videos          []*MediaInfo      `json:"videos"`          // 视频信息
	Audio           []*MediaInfo      `json:"audio"`           // 音频信息
	
	// 脚本和样式
	Scripts         []string          `json:"scripts"`         // 脚本文件
	Stylesheets     []string          `json:"stylesheets"`     // 样式表
	
	// 元数据
	MetaTags        map[string]string `json:"meta_tags"`       // Meta标签
	Metadata        map[string]string `json:"metadata"`        // 其他元数据
}

// MediaInfo 媒体信息
type MediaInfo struct {
	URL         string `json:"url"`         // 媒体URL
	Type        string `json:"type"`        // 媒体类型
	Size        int64  `json:"size"`        // 文件大小
	Alt         string `json:"alt"`         // 替代文本
	Title       string `json:"title"`       // 标题
	Width       int    `json:"width"`       // 宽度
	Height      int    `json:"height"`      // 高度
}

// FileInfo 文件信息
type FileInfo struct {
	// 基础信息
	Path        string            `json:"path"`        // 文件路径
	Name        string            `json:"name"`        // 文件名
	Extension   string            `json:"extension"`   // 文件扩展名
	Size        int64             `json:"size"`        // 文件大小
	
	// 内容信息
	ContentType string            `json:"content_type"` // 内容类型
	Encoding    string            `json:"encoding"`     // 编码
	Hash        string            `json:"hash"`         // 文件哈希
	
	// 安全信息
	IsSensitive bool              `json:"is_sensitive"` // 是否敏感
	IsBackup    bool              `json:"is_backup"`    // 是否备份文件
	IsConfig    bool              `json:"is_config"`    // 是否配置文件
	
	// 时间信息
	LastModified time.Time        `json:"last_modified"` // 最后修改时间
	
	// 元数据
	Metadata    map[string]string `json:"metadata"`     // 元数据
}

// CookieInfo Cookie信息
type CookieInfo struct {
	Name     string `json:"name"`     // Cookie名称
	Value    string `json:"value"`    // Cookie值
	Domain   string `json:"domain"`   // 域名
	Path     string `json:"path"`     // 路径
	Expires  string `json:"expires"`  // 过期时间
	MaxAge   int    `json:"max_age"`  // 最大年龄
	Secure   bool   `json:"secure"`   // 安全标志
	HttpOnly bool   `json:"http_only"` // HttpOnly标志
	SameSite string `json:"same_site"` // SameSite属性
}

// InfoStatistics 信息统计
type InfoStatistics struct {
	// 收集统计
	TotalRequests    int           `json:"total_requests"`    // 总请求数
	SuccessRequests  int           `json:"success_requests"`  // 成功请求数
	FailedRequests   int           `json:"failed_requests"`   // 失败请求数
	
	// 发现统计
	ComponentsFound  int           `json:"components_found"`  // 发现组件数
	ServicesFound    int           `json:"services_found"`    // 发现服务数
	FilesFound       int           `json:"files_found"`       // 发现文件数
	
	// 时间统计
	CollectionTime   time.Duration `json:"collection_time"`   // 收集耗时
	AnalysisTime     time.Duration `json:"analysis_time"`     // 分析耗时
	TotalTime        time.Duration `json:"total_time"`        // 总耗时
}
