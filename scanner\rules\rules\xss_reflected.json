{"id": "xss_reflected", "name": "反射型XSS检测", "description": "检测反射型跨站脚本攻击漏洞", "category": "xss", "severity": "medium", "conditions": [{"type": "response_analysis", "field": "response.body", "operator": "contains", "value": "<script>alert('XSS')</script>", "case_sensitive": false, "metadata": {"description": "检测XSS载荷是否被反射到响应中"}}, {"type": "response_analysis", "field": "response.headers.content-type", "operator": "contains", "value": "text/html", "case_sensitive": false, "metadata": {"description": "确保响应是HTML内容"}}], "actions": [{"type": "create_vulnerability", "target": "xss", "parameters": {"name": "反射型XSS漏洞", "description": "检测到反射型跨站脚本攻击漏洞", "impact": "可能导致用户会话劫持、钓鱼攻击或恶意代码执行", "solution": "对用户输入进行适当的编码和验证，使用CSP头", "references": ["https://owasp.org/www-community/attacks/xss/"]}}], "tags": ["xss", "reflected", "client-side"], "references": ["https://owasp.org/www-community/attacks/xss/", "https://portswigger.net/web-security/cross-site-scripting"], "author": "VulnScanner Team", "version": "1.0.0", "enabled": true, "config": {"payloads": ["<script>alert('XSS')</script>", "<img src=x onerror=alert('XSS')>", "<svg onload=alert('XSS')>", "javascript:alert('XSS')"]}}