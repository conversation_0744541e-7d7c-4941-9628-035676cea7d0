package types

import "time"

// 防护措施相关结构体定义

// FirewallInfo 防火墙信息
type FirewallInfo struct {
	Detected    bool     `json:"detected"`     // 是否检测到防火墙
	Type        string   `json:"type"`         // 防火墙类型
	Vendor      string   `json:"vendor"`       // 厂商
	Version     string   `json:"version"`      // 版本
	Rules       []string `json:"rules"`        // 规则
	Evidence    []string `json:"evidence"`     // 检测证据
	Confidence  float64  `json:"confidence"`   // 置信度
}

// AntiVirusInfo 杀毒软件信息
type AntiVirusInfo struct {
	Detected    bool     `json:"detected"`     // 是否检测到杀毒软件
	Product     string   `json:"product"`      // 产品名称
	Vendor      string   `json:"vendor"`       // 厂商
	Version     string   `json:"version"`      // 版本
	UpdateTime  string   `json:"update_time"`  // 更新时间
	Evidence    []string `json:"evidence"`     // 检测证据
	Confidence  float64  `json:"confidence"`   // 置信度
}

// HIDSInfo 主机入侵检测系统信息
type HIDSInfo struct {
	Detected    bool     `json:"detected"`     // 是否检测到HIDS
	Product     string   `json:"product"`      // 产品名称
	Vendor      string   `json:"vendor"`       // 厂商
	Version     string   `json:"version"`      // 版本
	Features    []string `json:"features"`     // 功能特性
	Evidence    []string `json:"evidence"`     // 检测证据
	Confidence  float64  `json:"confidence"`   // 置信度
}

// ProcessMonitorInfo 进程监控信息
type ProcessMonitorInfo struct {
	Detected    bool     `json:"detected"`     // 是否检测到进程监控
	Product     string   `json:"product"`      // 产品名称
	Vendor      string   `json:"vendor"`       // 厂商
	MonitorType string   `json:"monitor_type"` // 监控类型
	Evidence    []string `json:"evidence"`     // 检测证据
	Confidence  float64  `json:"confidence"`   // 置信度
}

// FileMonitorInfo 文件监控信息
type FileMonitorInfo struct {
	Detected    bool     `json:"detected"`     // 是否检测到文件监控
	Product     string   `json:"product"`      // 产品名称
	Vendor      string   `json:"vendor"`       // 厂商
	MonitorType string   `json:"monitor_type"` // 监控类型
	Evidence    []string `json:"evidence"`     // 检测证据
	Confidence  float64  `json:"confidence"`   // 置信度
}

// IDSInfo 入侵检测系统信息
type IDSInfo struct {
	Detected    bool     `json:"detected"`     // 是否检测到IDS
	Product     string   `json:"product"`      // 产品名称
	Vendor      string   `json:"vendor"`       // 厂商
	Version     string   `json:"version"`      // 版本
	DeployMode  string   `json:"deploy_mode"`  // 部署模式
	Evidence    []string `json:"evidence"`     // 检测证据
	Confidence  float64  `json:"confidence"`   // 置信度
}

// IPSInfo 入侵防护系统信息
type IPSInfo struct {
	Detected    bool     `json:"detected"`     // 是否检测到IPS
	Product     string   `json:"product"`      // 产品名称
	Vendor      string   `json:"vendor"`       // 厂商
	Version     string   `json:"version"`      // 版本
	DeployMode  string   `json:"deploy_mode"`  // 部署模式
	Evidence    []string `json:"evidence"`     // 检测证据
	Confidence  float64  `json:"confidence"`   // 置信度
}

// DDoSProtectionInfo DDoS防护信息
type DDoSProtectionInfo struct {
	Detected    bool     `json:"detected"`     // 是否检测到DDoS防护
	Product     string   `json:"product"`      // 产品名称
	Vendor      string   `json:"vendor"`       // 厂商
	Type        string   `json:"type"`         // 防护类型
	Capacity    string   `json:"capacity"`     // 防护能力
	Evidence    []string `json:"evidence"`     // 检测证据
	Confidence  float64  `json:"confidence"`   // 置信度
}

// LoadBalancerInfo 负载均衡信息
type LoadBalancerInfo struct {
	Detected    bool     `json:"detected"`     // 是否检测到负载均衡
	Product     string   `json:"product"`      // 产品名称
	Vendor      string   `json:"vendor"`       // 厂商
	Algorithm   string   `json:"algorithm"`    // 负载均衡算法
	Nodes       []string `json:"nodes"`        // 节点信息
	Evidence    []string `json:"evidence"`     // 检测证据
	Confidence  float64  `json:"confidence"`   // 置信度
}

// CDNInfo CDN信息
type CDNInfo struct {
	Detected    bool     `json:"detected"`     // 是否检测到CDN
	Provider    string   `json:"provider"`     // CDN提供商
	Nodes       []string `json:"nodes"`        // CDN节点
	Features    []string `json:"features"`     // 功能特性
	Evidence    []string `json:"evidence"`     // 检测证据
	Confidence  float64  `json:"confidence"`   // 置信度
}

// 安全头相关结构体

// HSTSInfo HSTS信息
type HSTSInfo struct {
	Enabled     bool   `json:"enabled"`      // 是否启用HSTS
	MaxAge      int    `json:"max_age"`      // 最大年龄
	Subdomains  bool   `json:"subdomains"`   // 是否包含子域名
	Preload     bool   `json:"preload"`      // 是否预加载
	HeaderValue string `json:"header_value"` // 头部值
}

// CSPInfo CSP信息
type CSPInfo struct {
	Enabled     bool     `json:"enabled"`      // 是否启用CSP
	Directives  []string `json:"directives"`   // 指令列表
	Violations  []string `json:"violations"`   // 违规项
	Strictness  string   `json:"strictness"`   // 严格程度
	HeaderValue string   `json:"header_value"` // 头部值
}

// XFrameOptionsInfo X-Frame-Options信息
type XFrameOptionsInfo struct {
	Enabled     bool   `json:"enabled"`      // 是否启用
	Value       string `json:"value"`        // 值 (DENY/SAMEORIGIN/ALLOW-FROM)
	HeaderValue string `json:"header_value"` // 头部值
}

// XContentTypeInfo X-Content-Type-Options信息
type XContentTypeInfo struct {
	Enabled     bool   `json:"enabled"`      // 是否启用
	Value       string `json:"value"`        // 值 (nosniff)
	HeaderValue string `json:"header_value"` // 头部值
}

// XSSProtectionInfo X-XSS-Protection信息
type XSSProtectionInfo struct {
	Enabled     bool   `json:"enabled"`      // 是否启用
	Mode        string `json:"mode"`         // 模式
	Block       bool   `json:"block"`        // 是否阻止
	HeaderValue string `json:"header_value"` // 头部值
}

// ReferrerPolicyInfo Referrer-Policy信息
type ReferrerPolicyInfo struct {
	Enabled     bool   `json:"enabled"`      // 是否启用
	Policy      string `json:"policy"`       // 策略
	HeaderValue string `json:"header_value"` // 头部值
}

// 指纹识别相关结构体

// HTTPHeaderAnalysis HTTP头分析结果
type HTTPHeaderAnalysis struct {
	ServerHeader    *ServerHeaderInfo    `json:"server_header"`    // Server头分析
	PoweredByHeader *PoweredByHeaderInfo `json:"powered_by_header"` // X-Powered-By头分析
	CustomHeaders   []*CustomHeaderInfo  `json:"custom_headers"`   // 自定义头分析
	SecurityHeaders *SecurityHeadersInfo `json:"security_headers"` // 安全头分析
	TotalHeaders    int                  `json:"total_headers"`    // 总头数量
	Confidence      float64              `json:"confidence"`       // 置信度
}

// ServerHeaderInfo Server头信息
type ServerHeaderInfo struct {
	Value       string   `json:"value"`       // 头部值
	WebServer   string   `json:"web_server"`  // Web服务器
	Version     string   `json:"version"`     // 版本
	OS          string   `json:"os"`          // 操作系统
	Modules     []string `json:"modules"`     // 模块
	Confidence  float64  `json:"confidence"`  // 置信度
}

// PoweredByHeaderInfo X-Powered-By头信息
type PoweredByHeaderInfo struct {
	Value       string  `json:"value"`       // 头部值
	Technology  string  `json:"technology"`  // 技术
	Version     string  `json:"version"`     // 版本
	Framework   string  `json:"framework"`   // 框架
	Confidence  float64 `json:"confidence"`  // 置信度
}

// CustomHeaderInfo 自定义头信息
type CustomHeaderInfo struct {
	Name        string  `json:"name"`        // 头名称
	Value       string  `json:"value"`       // 头值
	Technology  string  `json:"technology"`  // 相关技术
	Description string  `json:"description"` // 描述
	Confidence  float64 `json:"confidence"`  // 置信度
}

// PageContentAnalysis 页面内容分析结果
type PageContentAnalysis struct {
	HTMLTags        []*HTMLTagInfo        `json:"html_tags"`        // HTML标签分析
	JavaScriptLibs  []*JavaScriptLibInfo  `json:"javascript_libs"`  // JavaScript库分析
	CSSFrameworks   []*CSSFrameworkInfo   `json:"css_frameworks"`   // CSS框架分析
	MetaTags        []*MetaTagInfo        `json:"meta_tags"`        // Meta标签分析
	Comments        []*CommentInfo        `json:"comments"`         // 注释分析
	TotalElements   int                   `json:"total_elements"`   // 总元素数量
	Confidence      float64               `json:"confidence"`       // 置信度
}

// HTMLTagInfo HTML标签信息
type HTMLTagInfo struct {
	Tag         string  `json:"tag"`         // 标签名
	Attributes  map[string]string `json:"attributes"`  // 属性
	Technology  string  `json:"technology"`  // 相关技术
	Framework   string  `json:"framework"`   // 框架
	Confidence  float64 `json:"confidence"`  // 置信度
}

// JavaScriptLibInfo JavaScript库信息
type JavaScriptLibInfo struct {
	Name        string  `json:"name"`        // 库名称
	Version     string  `json:"version"`     // 版本
	Source      string  `json:"source"`      // 来源
	CDN         bool    `json:"cdn"`         // 是否CDN
	Confidence  float64 `json:"confidence"`  // 置信度
}

// CSSFrameworkInfo CSS框架信息
type CSSFrameworkInfo struct {
	Name        string  `json:"name"`        // 框架名称
	Version     string  `json:"version"`     // 版本
	Source      string  `json:"source"`      // 来源
	CDN         bool    `json:"cdn"`         // 是否CDN
	Confidence  float64 `json:"confidence"`  // 置信度
}

// MetaTagInfo Meta标签信息
type MetaTagInfo struct {
	Name        string  `json:"name"`        // 标签名
	Content     string  `json:"content"`     // 内容
	Technology  string  `json:"technology"`  // 相关技术
	Generator   string  `json:"generator"`   // 生成器
	Confidence  float64 `json:"confidence"`  // 置信度
}

// CommentInfo 注释信息
type CommentInfo struct {
	Content     string  `json:"content"`     // 注释内容
	Type        string  `json:"type"`        // 注释类型
	Technology  string  `json:"technology"`  // 相关技术
	Sensitive   bool    `json:"sensitive"`   // 是否敏感
	Confidence  float64 `json:"confidence"`  // 置信度
}

// ErrorPageAnalysis 错误页面分析结果
type ErrorPageAnalysis struct {
	ErrorPages      []*ErrorPageInfo      `json:"error_pages"`      // 错误页面
	DefaultPages    []*DefaultPageInfo    `json:"default_pages"`    // 默认页面
	DebugInfo       []*DebugInfoItem      `json:"debug_info"`       // 调试信息
	TotalErrors     int                   `json:"total_errors"`     // 总错误数
	Confidence      float64               `json:"confidence"`       // 置信度
}

// ErrorPageInfo 错误页面信息
type ErrorPageInfo struct {
	StatusCode  int     `json:"status_code"`  // 状态码
	URL         string  `json:"url"`          // URL
	Title       string  `json:"title"`        // 标题
	Content     string  `json:"content"`      // 内容
	Technology  string  `json:"technology"`   // 相关技术
	WebServer   string  `json:"web_server"`   // Web服务器
	Confidence  float64 `json:"confidence"`   // 置信度
}

// DefaultPageInfo 默认页面信息
type DefaultPageInfo struct {
	URL         string  `json:"url"`          // URL
	Type        string  `json:"type"`         // 页面类型
	Technology  string  `json:"technology"`   // 相关技术
	WebServer   string  `json:"web_server"`   // Web服务器
	Confidence  float64 `json:"confidence"`   // 置信度
}

// DebugInfoItem 调试信息项
type DebugInfoItem struct {
	Type        string  `json:"type"`         // 调试信息类型
	Content     string  `json:"content"`      // 内容
	Location    string  `json:"location"`     // 位置
	Sensitivity string  `json:"sensitivity"`  // 敏感程度
	Confidence  float64 `json:"confidence"`   // 置信度
}

// CookieAnalysis Cookie分析结果
type CookieAnalysis struct {
	SessionCookies  []*SessionCookieInfo  `json:"session_cookies"`  // 会话Cookie
	SecurityCookies []*SecurityCookieInfo `json:"security_cookies"` // 安全Cookie
	TrackingCookies []*TrackingCookieInfo `json:"tracking_cookies"` // 跟踪Cookie
	TechCookies     []*TechCookieInfo     `json:"tech_cookies"`     // 技术Cookie
	TotalCookies    int                   `json:"total_cookies"`    // 总Cookie数
	Confidence      float64               `json:"confidence"`       // 置信度
}

// SessionCookieInfo 会话Cookie信息
type SessionCookieInfo struct {
	Name        string  `json:"name"`        // Cookie名称
	Value       string  `json:"value"`       // Cookie值
	Technology  string  `json:"technology"`  // 相关技术
	Framework   string  `json:"framework"`   // 框架
	Secure      bool    `json:"secure"`      // 是否安全
	HttpOnly    bool    `json:"http_only"`   // 是否HttpOnly
	SameSite    string  `json:"same_site"`   // SameSite属性
	Confidence  float64 `json:"confidence"`  // 置信度
}

// SecurityCookieInfo 安全Cookie信息
type SecurityCookieInfo struct {
	Name        string  `json:"name"`        // Cookie名称
	Purpose     string  `json:"purpose"`     // 用途
	Security    string  `json:"security"`    // 安全级别
	Confidence  float64 `json:"confidence"`  // 置信度
}

// TrackingCookieInfo 跟踪Cookie信息
type TrackingCookieInfo struct {
	Name        string  `json:"name"`        // Cookie名称
	Provider    string  `json:"provider"`    // 提供商
	Purpose     string  `json:"purpose"`     // 用途
	Confidence  float64 `json:"confidence"`  // 置信度
}

// TechCookieInfo 技术Cookie信息
type TechCookieInfo struct {
	Name        string  `json:"name"`        // Cookie名称
	Technology  string  `json:"technology"`  // 技术
	Framework   string  `json:"framework"`   // 框架
	Version     string  `json:"version"`     // 版本
	Confidence  float64 `json:"confidence"`  // 置信度
}

// TechStackFingerprint 技术栈指纹
type TechStackFingerprint struct {
	Category    string            `json:"category"`    // 分类
	Technology  string            `json:"technology"`  // 技术
	Version     string            `json:"version"`     // 版本
	Confidence  float64           `json:"confidence"`  // 置信度
	Evidence    []string          `json:"evidence"`    // 证据
	Source      string            `json:"source"`      // 来源
	Metadata    map[string]string `json:"metadata"`    // 元数据
}
