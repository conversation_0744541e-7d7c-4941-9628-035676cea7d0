package main

import (
	"fmt"
	"log"
	"strings"
	"time"

	"scanner/internal/scanner/core"
	"scanner/internal/scanner/engines"
)

// 测试新的扫描引擎架构
func main() {
	fmt.Println("🚀 漏洞扫描引擎架构测试")
	fmt.Println(strings.Repeat("=", 50))
	
	// 创建引擎管理器
	manager := core.NewEngineManager()
	
	// 注册扫描引擎
	fmt.Println("📋 注册扫描引擎...")
	
	// 注册信息收集引擎
	infoEngine := engines.NewInformationGatheringEngine()
	if err := manager.RegisterEngine(infoEngine); err != nil {
		log.Fatalf("注册信息收集引擎失败: %v", err)
	}
	fmt.Println("✅ 信息收集引擎注册成功")
	
	// 注册Web漏洞扫描引擎
	webEngine := engines.NewWebVulnerabilityEngine()
	if err := manager.RegisterEngine(webEngine); err != nil {
		log.Fatalf("注册Web漏洞扫描引擎失败: %v", err)
	}
	fmt.Println("✅ Web漏洞扫描引擎注册成功")
	
	// 初始化管理器
	fmt.Println("\n🔧 初始化引擎管理器...")
	if err := manager.Initialize(); err != nil {
		log.Fatalf("初始化管理器失败: %v", err)
	}
	fmt.Println("✅ 引擎管理器初始化成功")
	
	// 列出所有引擎
	fmt.Println("\n📊 已注册的扫描引擎:")
	engines := manager.ListEngines()
	for i, engine := range engines {
		fmt.Printf("%d. %s (%s) - %s\n", i+1, engine.Name, engine.Type, engine.Description)
		fmt.Printf("   版本: %s, 状态: %s, 启用: %t\n", engine.Version, engine.Status, engine.Enabled)
	}
	
	// 创建测试目标
	fmt.Println("\n🎯 创建扫描目标...")
	target := &core.ScanTarget{
		ID:          "test_target_1",
		Type:        core.TargetTypeURL,
		Value:       "http://httpbin.org",
		Description: "测试目标 - httpbin.org",
		Metadata:    make(map[string]string),
		CreatedAt:   time.Now(),
	}
	fmt.Printf("✅ 目标创建成功: %s (%s)\n", target.Value, target.Type)
	
	// 创建扫描配置
	fmt.Println("\n⚙️ 创建扫描配置...")
	config := &core.ScanConfig{
		TaskID:      "test_task_1",
		Timeout:     5 * time.Minute,
		Concurrency: 5,
		Depth:       2,
		ScanMode:    core.ScanModeStandard,
		UserAgent:   "VulnScanner-Test/1.0",
		CreatedAt:   time.Now(),
	}
	fmt.Printf("✅ 配置创建成功: 模式=%s, 超时=%v\n", config.ScanMode, config.Timeout)
	
	// 测试信息收集扫描
	fmt.Println("\n🔍 测试信息收集扫描...")
	testInformationGathering(manager, target, config)
	
	// 显示管理器统计信息
	fmt.Println("\n📈 管理器统计信息:")
	stats := manager.GetStatistics()
	fmt.Printf("总引擎数: %d, 启用引擎数: %d, 运行引擎数: %d\n", 
		stats.TotalEngines, stats.EnabledEngines, stats.RunningEngines)
	fmt.Printf("总任务数: %d, 完成任务数: %d, 失败任务数: %d, 活跃任务数: %d\n",
		stats.TotalTasks, stats.CompletedTasks, stats.FailedTasks, stats.ActiveTasks)
	
	// 关闭管理器
	fmt.Println("\n🔒 关闭引擎管理器...")
	if err := manager.Shutdown(); err != nil {
		log.Printf("关闭管理器失败: %v", err)
	} else {
		fmt.Println("✅ 引擎管理器已关闭")
	}
	
	fmt.Println("\n🎉 测试完成!")
}

// testInformationGathering 测试信息收集
func testInformationGathering(manager *core.EngineManager, target *core.ScanTarget, config *core.ScanConfig) {
	// 创建扫描请求
	request := &core.ScanRequest{
		ID:        "info_scan_" + fmt.Sprintf("%d", time.Now().Unix()),
		Target:    target,
		Config:    config,
		Priority:  1,
		CreatedAt: time.Now(),
	}
	
	// 提交扫描任务
	taskInfo, err := manager.SubmitScanRequest(request)
	if err != nil {
		fmt.Printf("❌ 提交信息收集任务失败: %v\n", err)
		return
	}
	
	fmt.Printf("✅ 信息收集任务已提交: %s\n", taskInfo.ID)
	
	// 监控任务进度
	monitorTask(manager, taskInfo.ID, "信息收集")
}

// monitorTask 监控任务进度
func monitorTask(manager *core.EngineManager, taskID, taskType string) {
	fmt.Printf("📊 监控%s任务进度: %s\n", taskType, taskID)
	
	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()
	
	timeout := time.After(30 * time.Second) // 30秒超时
	
	for {
		select {
		case <-ticker.C:
			taskInfo, err := manager.GetTaskStatus(taskID)
			if err != nil {
				fmt.Printf("❌ 获取任务状态失败: %v\n", err)
				return
			}
			
			fmt.Printf("   状态: %s, 进度: %.1f%%\n", taskInfo.Status, taskInfo.Progress)
			
			// 检查任务是否完成
			if taskInfo.Status == core.StatusCompleted {
				fmt.Printf("✅ %s任务完成!\n", taskType)
				
				// 显示结果摘要
				if taskInfo.Result != nil {
					displayResultSummary(taskInfo.Result, taskType)
				}
				return
			} else if taskInfo.Status == core.StatusFailed {
				fmt.Printf("❌ %s任务失败: %v\n", taskType, taskInfo.Error)
				return
			} else if taskInfo.Status == core.StatusCancelled {
				fmt.Printf("⚠️ %s任务被取消\n", taskType)
				return
			}
			
		case <-timeout:
			fmt.Printf("⏰ %s任务监控超时\n", taskType)
			return
		}
	}
}

// displayResultSummary 显示结果摘要
func displayResultSummary(result *core.ScanResult, taskType string) {
	fmt.Printf("📋 %s结果摘要:\n", taskType)
	fmt.Printf("   任务ID: %s\n", result.TaskID)
	fmt.Printf("   引擎类型: %s\n", result.EngineType)
	fmt.Printf("   扫描目标: %s\n", result.Target.Value)
	fmt.Printf("   开始时间: %s\n", result.StartedAt.Format("2006-01-02 15:04:05"))
	if result.CompletedAt != nil {
		fmt.Printf("   完成时间: %s\n", result.CompletedAt.Format("2006-01-02 15:04:05"))
	}
	fmt.Printf("   扫描耗时: %v\n", result.Duration)
	
	// 漏洞统计
	if len(result.Vulnerabilities) > 0 {
		fmt.Printf("   发现漏洞: %d个\n", len(result.Vulnerabilities))
		
		// 按严重程度统计
		severityCount := make(map[core.SeverityLevel]int)
		for _, vuln := range result.Vulnerabilities {
			severityCount[vuln.Severity]++
		}
		
		for severity, count := range severityCount {
			fmt.Printf("     %s: %d个\n", severity, count)
		}
	} else {
		fmt.Printf("   发现漏洞: 0个\n")
	}
	
	// 信息收集结果
	if result.Information != nil {
		fmt.Printf("   信息收集:\n")
		if result.Information.TargetInfo != nil {
			fmt.Printf("     目标信息: %s\n", result.Information.TargetInfo.URL)
			fmt.Printf("     服务器: %s\n", result.Information.TargetInfo.Server)
			fmt.Printf("     状态码: %d\n", result.Information.TargetInfo.StatusCode)
		}
		if result.Information.Services != nil {
			fmt.Printf("     发现服务: %d个\n", len(result.Information.Services))
		}
		if result.Information.TechStack != nil {
			if result.Information.TechStack.WebServer != nil {
				fmt.Printf("     Web服务器: %s\n", result.Information.TechStack.WebServer.Name)
			}
			if result.Information.TechStack.Language != nil {
				fmt.Printf("     编程语言: %s\n", result.Information.TechStack.Language.Name)
			}
		}
	}
	
	// 统计信息
	if result.Statistics != nil {
		fmt.Printf("   统计信息:\n")
		fmt.Printf("     扫描目标数: %d\n", result.Statistics.TotalTargets)
		fmt.Printf("     发送请求数: %d\n", result.Statistics.RequestsSent)
		fmt.Printf("     接收响应数: %d\n", result.Statistics.ResponsesReceived)
	}
	
	// 警告信息
	if len(result.Warnings) > 0 {
		fmt.Printf("   警告信息: %d条\n", len(result.Warnings))
		for i, warning := range result.Warnings {
			if i >= 3 {
				fmt.Printf("     ... 还有 %d 条警告\n", len(result.Warnings)-3)
				break
			}
			fmt.Printf("     - %s\n", warning)
		}
	}
	
	fmt.Println()
}
