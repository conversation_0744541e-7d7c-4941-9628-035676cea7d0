package engines

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"strings"

	"scanner/internal/scanner/types"
)

// collectEnhancedSystemInformation 增强的系统信息收集 - 操作系统、硬件信息、网络配置
func (e *InformationGatheringEngine) collectEnhancedSystemInformation(ctx context.Context, target string) (*types.SystemInformation, error) {
	systemInfo := &types.SystemInformation{}

	// 解析目标URL
	parsedURL, err := url.Parse(target)
	if err != nil {
		return nil, err
	}

	// 通过HTTP头推断操作系统
	req, err := http.NewRequestWithContext(ctx, "GET", target, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("User-Agent", e.userAgent)

	resp, err := e.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 分析Server头推断操作系统
	if server := resp.Header.Get("Server"); server != "" {
		systemInfo.OS = e.inferOSFromServer(server)
		systemInfo.WebServer = e.extractWebServerFromHeader(server)
	}

	// 通过TTL值推断操作系统（需要ping功能）
	if osFromTTL := e.inferOSFromTTL(parsedURL.Hostname()); osFromTTL != "" {
		if systemInfo.OS == "" {
			systemInfo.OS = osFromTTL
		}
	}

	// 收集网络配置信息
	networkConfig := e.collectNetworkConfiguration(parsedURL.Hostname())
	systemInfo.NetworkConfig = networkConfig

	// 收集硬件信息（通过HTTP响应推断）
	hardwareInfo := e.inferHardwareInfo(resp)
	systemInfo.HardwareInfo = hardwareInfo

	return systemInfo, nil
}

// inferOSFromServer 从Server头推断操作系统
func (e *InformationGatheringEngine) inferOSFromServer(server string) string {
	server = strings.ToLower(server)

	if strings.Contains(server, "win32") || strings.Contains(server, "windows") {
		return "Windows"
	} else if strings.Contains(server, "ubuntu") {
		return "Ubuntu"
	} else if strings.Contains(server, "centos") {
		return "CentOS"
	} else if strings.Contains(server, "debian") {
		return "Debian"
	} else if strings.Contains(server, "redhat") || strings.Contains(server, "rhel") {
		return "Red Hat"
	} else if strings.Contains(server, "unix") {
		return "Unix"
	} else if strings.Contains(server, "linux") {
		return "Linux"
	}

	return "Unknown"
}

// extractWebServerFromHeader 从Server头提取Web服务器信息
func (e *InformationGatheringEngine) extractWebServerFromHeader(server string) string {
	server = strings.ToLower(server)

	if strings.Contains(server, "apache") {
		return "Apache"
	} else if strings.Contains(server, "nginx") {
		return "Nginx"
	} else if strings.Contains(server, "iis") {
		return "IIS"
	} else if strings.Contains(server, "lighttpd") {
		return "Lighttpd"
	} else if strings.Contains(server, "tomcat") {
		return "Tomcat"
	}

	return "Unknown"
}

// inferOSFromTTL 通过TTL值推断操作系统
func (e *InformationGatheringEngine) inferOSFromTTL(hostname string) string {
	// 这里是简化实现，实际需要ping功能
	// 不同操作系统的默认TTL值：
	// Windows: 128
	// Linux: 64
	// macOS: 64
	// 由于Go标准库没有直接的ping功能，这里返回空字符串
	return ""
}

// collectNetworkConfiguration 收集网络配置信息
func (e *InformationGatheringEngine) collectNetworkConfiguration(hostname string) map[string]interface{} {
	config := make(map[string]interface{})

	// DNS解析信息
	if ips, err := net.LookupIP(hostname); err == nil {
		var ipv4s, ipv6s []string
		for _, ip := range ips {
			if ip.To4() != nil {
				ipv4s = append(ipv4s, ip.String())
			} else {
				ipv6s = append(ipv6s, ip.String())
			}
		}
		config["ipv4_addresses"] = ipv4s
		config["ipv6_addresses"] = ipv6s
	}

	// CNAME记录
	if cname, err := net.LookupCNAME(hostname); err == nil && cname != hostname+"." {
		config["cname"] = strings.TrimSuffix(cname, ".")
	}

	// MX记录
	if mxRecords, err := net.LookupMX(hostname); err == nil {
		var mxs []string
		for _, mx := range mxRecords {
			mxs = append(mxs, fmt.Sprintf("%d %s", mx.Pref, mx.Host))
		}
		config["mx_records"] = mxs
	}

	// NS记录
	if nsRecords, err := net.LookupNS(hostname); err == nil {
		var nss []string
		for _, ns := range nsRecords {
			nss = append(nss, strings.TrimSuffix(ns.Host, "."))
		}
		config["ns_records"] = nss
	}

	return config
}

// inferHardwareInfo 推断硬件信息
func (e *InformationGatheringEngine) inferHardwareInfo(resp *http.Response) map[string]interface{} {
	info := make(map[string]interface{})

	// 通过响应时间推断性能
	if resp.Header.Get("X-Response-Time") != "" {
		info["response_time"] = resp.Header.Get("X-Response-Time")
	}

	// 通过Server头推断架构
	if server := resp.Header.Get("Server"); server != "" {
		if strings.Contains(strings.ToLower(server), "x64") || strings.Contains(strings.ToLower(server), "amd64") {
			info["architecture"] = "x64"
		} else if strings.Contains(strings.ToLower(server), "x86") {
			info["architecture"] = "x86"
		}
	}

	return info
}

// discoverSensitiveInformation 敏感信息发现 - 配置文件、备份文件、源码泄露
func (e *InformationGatheringEngine) discoverSensitiveInformation(ctx context.Context, target string) (*types.SensitiveInformation, error) {
	sensitiveInfo := &types.SensitiveInformation{
		ConfigFiles:          make([]*types.SensitiveFile, 0),
		BackupFiles:          make([]*types.SensitiveFile, 0),
		SourceCodeLeaks:      make([]*types.SensitiveFile, 0),
		SensitiveDirectories: make([]*types.SensitiveDirectory, 0),
		InformationLeaks:     make([]*types.InformationLeak, 0),
	}

	// 解析基础URL
	baseURL, err := url.Parse(target)
	if err != nil {
		return nil, err
	}

	// 扫描敏感文件
	e.scanSensitiveFiles(ctx, baseURL, sensitiveInfo)

	// 扫描敏感目录
	e.scanSensitiveDirectories(ctx, baseURL, sensitiveInfo)

	// 扫描备份文件
	e.scanBackupFiles(ctx, baseURL, sensitiveInfo)

	// 扫描源码泄露
	e.scanSourceCodeLeaks(ctx, baseURL, sensitiveInfo)

	// 更新统计信息
	sensitiveInfo.TotalFiles = len(sensitiveInfo.ConfigFiles) + len(sensitiveInfo.BackupFiles) + len(sensitiveInfo.SourceCodeLeaks)

	// 计算风险级别统计
	for _, file := range sensitiveInfo.ConfigFiles {
		e.updateRiskStats(file.Risk, sensitiveInfo)
	}
	for _, file := range sensitiveInfo.BackupFiles {
		e.updateRiskStats(file.Risk, sensitiveInfo)
	}
	for _, file := range sensitiveInfo.SourceCodeLeaks {
		e.updateRiskStats(file.Risk, sensitiveInfo)
	}

	return sensitiveInfo, nil
}

// scanSensitiveFiles 扫描敏感配置文件
func (e *InformationGatheringEngine) scanSensitiveFiles(ctx context.Context, baseURL *url.URL, sensitiveInfo *types.SensitiveInformation) {
	// 常见的敏感配置文件
	sensitiveFiles := []string{
		".env",
		"config.php",
		"database.yml",
		"settings.py",
		"web.config",
		"app.config",
		"config.json",
		"config.xml",
		"application.properties",
		"config.ini",
		"wp-config.php",
		"configuration.php",
		"config.inc.php",
		"settings.ini",
		"local_settings.py",
		"production.py",
		"development.py",
	}

	for _, file := range sensitiveFiles {
		select {
		case <-ctx.Done():
			return
		default:
		}

		fileURL := fmt.Sprintf("%s://%s/%s", baseURL.Scheme, baseURL.Host, file)
		if sensitiveFile := e.checkSensitiveFile(ctx, fileURL, "config"); sensitiveFile != nil {
			sensitiveInfo.ConfigFiles = append(sensitiveInfo.ConfigFiles, sensitiveFile)
		}
	}
}

// scanSensitiveDirectories 扫描敏感目录
func (e *InformationGatheringEngine) scanSensitiveDirectories(ctx context.Context, baseURL *url.URL, sensitiveInfo *types.SensitiveInformation) {
	// 常见的敏感目录
	sensitiveDirectories := []string{
		"admin/",
		"administrator/",
		"management/",
		"manager/",
		"control/",
		"panel/",
		"dashboard/",
		"backend/",
		"cms/",
		"wp-admin/",
		"phpmyadmin/",
		"adminer/",
		"backup/",
		"backups/",
		"bak/",
		"old/",
		"temp/",
		"tmp/",
		"test/",
		"debug/",
		"dev/",
		"development/",
		".git/",
		".svn/",
		".hg/",
		"CVS/",
	}

	for _, dir := range sensitiveDirectories {
		select {
		case <-ctx.Done():
			return
		default:
		}

		dirURL := fmt.Sprintf("%s://%s/%s", baseURL.Scheme, baseURL.Host, dir)
		if sensitiveDir := e.checkSensitiveDirectory(ctx, dirURL, dir); sensitiveDir != nil {
			sensitiveInfo.SensitiveDirectories = append(sensitiveInfo.SensitiveDirectories, sensitiveDir)
		}
	}
}

// scanBackupFiles 扫描备份文件
func (e *InformationGatheringEngine) scanBackupFiles(ctx context.Context, baseURL *url.URL, sensitiveInfo *types.SensitiveInformation) {
	// 常见的备份文件扩展名和模式
	backupPatterns := []string{
		"backup.zip",
		"backup.tar.gz",
		"backup.sql",
		"database.sql",
		"dump.sql",
		"site.zip",
		"website.zip",
		"www.zip",
		"backup.tar",
		"backup.rar",
		"db_backup.sql",
		"mysql_backup.sql",
		"postgres_backup.sql",
	}

	for _, pattern := range backupPatterns {
		select {
		case <-ctx.Done():
			return
		default:
		}

		fileURL := fmt.Sprintf("%s://%s/%s", baseURL.Scheme, baseURL.Host, pattern)
		if backupFile := e.checkSensitiveFile(ctx, fileURL, "backup"); backupFile != nil {
			sensitiveInfo.BackupFiles = append(sensitiveInfo.BackupFiles, backupFile)
		}
	}
}

// scanSourceCodeLeaks 扫描源码泄露
func (e *InformationGatheringEngine) scanSourceCodeLeaks(ctx context.Context, baseURL *url.URL, sensitiveInfo *types.SensitiveInformation) {
	// 常见的源码泄露文件
	sourceFiles := []string{
		"index.php~",
		"index.php.bak",
		"config.php~",
		"config.php.bak",
		"database.php.bak",
		"settings.py.bak",
		"app.py~",
		"main.py.bak",
		"index.jsp~",
		"web.xml.bak",
		"application.properties.bak",
	}

	for _, file := range sourceFiles {
		select {
		case <-ctx.Done():
			return
		default:
		}

		fileURL := fmt.Sprintf("%s://%s/%s", baseURL.Scheme, baseURL.Host, file)
		if sourceFile := e.checkSensitiveFile(ctx, fileURL, "source"); sourceFile != nil {
			sensitiveInfo.SourceCodeLeaks = append(sensitiveInfo.SourceCodeLeaks, sourceFile)
		}
	}
}

// checkSensitiveFile 检查敏感文件
func (e *InformationGatheringEngine) checkSensitiveFile(ctx context.Context, fileURL, fileType string) *types.SensitiveFile {
	req, err := http.NewRequestWithContext(ctx, "GET", fileURL, nil)
	if err != nil {
		return nil
	}
	req.Header.Set("User-Agent", e.userAgent)

	resp, err := e.client.Do(req)
	if err != nil {
		return nil
	}
	defer resp.Body.Close()

	// 只处理成功的响应
	if resp.StatusCode != 200 {
		return nil
	}

	// 读取部分内容
	content := make([]byte, 1024)
	n, _ := resp.Body.Read(content)
	contentStr := string(content[:n])

	// 分析内容确定风险级别
	risk := e.assessFileRisk(contentStr, fileType)
	if risk == "none" {
		return nil
	}

	// 提取文件路径
	parsedURL, _ := url.Parse(fileURL)

	return &types.SensitiveFile{
		Path:        parsedURL.Path,
		Type:        fileType,
		Size:        resp.ContentLength,
		Content:     contentStr,
		Risk:        risk,
		Description: e.getFileDescription(fileType, risk),
		Headers:     e.convertHeaders(resp.Header),
		StatusCode:  resp.StatusCode,
		Evidence:    e.extractEvidence(contentStr, fileType),
		Confidence:  e.calculateConfidence(contentStr, fileType),
	}
}

// checkSensitiveDirectory 检查敏感目录
func (e *InformationGatheringEngine) checkSensitiveDirectory(ctx context.Context, dirURL, dirPath string) *types.SensitiveDirectory {
	req, err := http.NewRequestWithContext(ctx, "GET", dirURL, nil)
	if err != nil {
		return nil
	}
	req.Header.Set("User-Agent", e.userAgent)

	resp, err := e.client.Do(req)
	if err != nil {
		return nil
	}
	defer resp.Body.Close()

	// 检查是否可访问
	if resp.StatusCode == 403 || resp.StatusCode == 401 {
		// 目录存在但被保护，这也是有价值的信息
		return &types.SensitiveDirectory{
			Path:        dirPath,
			Type:        e.getDirType(dirPath),
			Risk:        "medium",
			Description: "目录存在但访问被限制",
			StatusCode:  resp.StatusCode,
			Evidence:    []string{fmt.Sprintf("HTTP %d响应", resp.StatusCode)},
		}
	} else if resp.StatusCode == 200 {
		// 目录可访问
		return &types.SensitiveDirectory{
			Path:        dirPath,
			Type:        e.getDirType(dirPath),
			Risk:        "high",
			Description: "敏感目录可直接访问",
			StatusCode:  resp.StatusCode,
			Evidence:    []string{"目录可直接访问"},
		}
	}

	return nil
}

// 辅助方法

// updateRiskStats 更新风险统计
func (e *InformationGatheringEngine) updateRiskStats(risk string, sensitiveInfo *types.SensitiveInformation) {
	switch risk {
	case "high":
		sensitiveInfo.HighRiskFiles++
	case "medium":
		sensitiveInfo.MediumRiskFiles++
	case "low":
		sensitiveInfo.LowRiskFiles++
	}
}

// assessFileRisk 评估文件风险级别
func (e *InformationGatheringEngine) assessFileRisk(content, fileType string) string {
	content = strings.ToLower(content)

	// 高风险指标
	highRiskPatterns := []string{
		"password", "passwd", "pwd", "secret", "key", "token",
		"database", "db_", "mysql", "postgres", "mongodb",
		"api_key", "access_token", "private_key", "secret_key",
		"aws_access_key", "aws_secret", "google_api_key",
		"smtp_password", "email_password", "ftp_password",
	}

	for _, pattern := range highRiskPatterns {
		if strings.Contains(content, pattern) {
			return "high"
		}
	}

	// 中风险指标
	mediumRiskPatterns := []string{
		"config", "setting", "configuration", "admin",
		"debug", "test", "development", "staging",
		"localhost", "127.0.0.1", "internal",
	}

	for _, pattern := range mediumRiskPatterns {
		if strings.Contains(content, pattern) {
			return "medium"
		}
	}

	// 低风险指标
	lowRiskPatterns := []string{
		"version", "build", "release", "info",
		"public", "static", "assets",
	}

	for _, pattern := range lowRiskPatterns {
		if strings.Contains(content, pattern) {
			return "low"
		}
	}

	return "none"
}

// getFileDescription 获取文件描述
func (e *InformationGatheringEngine) getFileDescription(fileType, risk string) string {
	descriptions := map[string]map[string]string{
		"config": {
			"high":   "包含敏感配置信息的文件",
			"medium": "包含配置信息的文件",
			"low":    "一般配置文件",
		},
		"backup": {
			"high":   "包含敏感数据的备份文件",
			"medium": "数据备份文件",
			"low":    "一般备份文件",
		},
		"source": {
			"high":   "包含敏感信息的源码文件",
			"medium": "源码文件泄露",
			"low":    "一般源码文件",
		},
	}

	if typeDesc, exists := descriptions[fileType]; exists {
		if desc, exists := typeDesc[risk]; exists {
			return desc
		}
	}

	return "敏感文件"
}

// convertHeaders 转换HTTP头
func (e *InformationGatheringEngine) convertHeaders(headers http.Header) map[string]string {
	result := make(map[string]string)
	for name, values := range headers {
		if len(values) > 0 {
			result[name] = values[0]
		}
	}
	return result
}

// extractEvidence 提取证据
func (e *InformationGatheringEngine) extractEvidence(content, fileType string) []string {
	var evidence []string

	// 根据文件类型提取不同的证据
	switch fileType {
	case "config":
		if strings.Contains(strings.ToLower(content), "password") {
			evidence = append(evidence, "包含密码字段")
		}
		if strings.Contains(strings.ToLower(content), "database") {
			evidence = append(evidence, "包含数据库配置")
		}
		if strings.Contains(strings.ToLower(content), "api_key") {
			evidence = append(evidence, "包含API密钥")
		}
	case "backup":
		if strings.Contains(strings.ToLower(content), "sql") {
			evidence = append(evidence, "SQL备份文件")
		}
		if strings.Contains(strings.ToLower(content), "dump") {
			evidence = append(evidence, "数据转储文件")
		}
	case "source":
		if strings.Contains(strings.ToLower(content), "<?php") {
			evidence = append(evidence, "PHP源码文件")
		}
		if strings.Contains(strings.ToLower(content), "import") {
			evidence = append(evidence, "包含导入语句")
		}
	}

	if len(evidence) == 0 {
		evidence = append(evidence, "文件可访问")
	}

	return evidence
}

// calculateConfidence 计算置信度
func (e *InformationGatheringEngine) calculateConfidence(content, fileType string) float64 {
	confidence := 0.5 // 基础置信度

	content = strings.ToLower(content)

	// 根据内容特征调整置信度
	if strings.Contains(content, "password") || strings.Contains(content, "secret") {
		confidence += 0.3
	}
	if strings.Contains(content, "database") || strings.Contains(content, "mysql") {
		confidence += 0.2
	}
	if strings.Contains(content, "api_key") || strings.Contains(content, "token") {
		confidence += 0.2
	}

	// 根据文件类型调整
	switch fileType {
	case "config":
		confidence += 0.1
	case "backup":
		confidence += 0.2
	case "source":
		confidence += 0.1
	}

	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// getDirType 获取目录类型
func (e *InformationGatheringEngine) getDirType(dirPath string) string {
	dirPath = strings.ToLower(dirPath)

	if strings.Contains(dirPath, "admin") {
		return "admin"
	} else if strings.Contains(dirPath, "backup") {
		return "backup"
	} else if strings.Contains(dirPath, "debug") || strings.Contains(dirPath, "test") {
		return "debug"
	} else if strings.Contains(dirPath, "git") || strings.Contains(dirPath, "svn") {
		return "vcs"
	} else if strings.Contains(dirPath, "temp") || strings.Contains(dirPath, "tmp") {
		return "temp"
	}

	return "sensitive"
}
