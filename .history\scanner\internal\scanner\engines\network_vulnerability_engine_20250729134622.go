package engines

import (
	"context"
	"fmt"
	"net"
	"strconv"
	"strings"
	"sync"
	"time"

	"scanner/internal/scanner/core"
)

// NetworkVulnerabilityEngine 网络漏洞扫描引擎
// 负责检测网络层面的漏洞，包括端口扫描、服务漏洞、协议漏洞等
type NetworkVulnerabilityEngine struct {
	*core.BaseEngine
	
	// 配置
	config *NetworkVulnConfig
	
	// 扫描器
	portScanner     *PortScanner
	serviceScanner  *ServiceScanner
	protocolScanner *ProtocolScanner
	
	// 漏洞检测器
	serviceVulnDetector *ServiceVulnerabilityDetector
	protocolVulnDetector *ProtocolVulnerabilityDetector
	
	// 规则引擎
	ruleEngine *core.RuleEngine
}

// NetworkVulnConfig 网络漏洞扫描配置
type NetworkVulnConfig struct {
	// 端口扫描配置
	PortScanEnabled    bool          `json:"port_scan_enabled"`
	PortRange          string        `json:"port_range"`
	ScanTimeout        time.Duration `json:"scan_timeout"`
	ConnectTimeout     time.Duration `json:"connect_timeout"`
	
	// 服务检测配置
	ServiceDetection   bool          `json:"service_detection"`
	BannerGrabbing     bool          `json:"banner_grabbing"`
	ServiceTimeout     time.Duration `json:"service_timeout"`
	
	// 漏洞检测配置
	VulnDetectionEnabled bool        `json:"vuln_detection_enabled"`
	CVECheckEnabled      bool        `json:"cve_check_enabled"`
	WeakPasswordCheck    bool        `json:"weak_password_check"`
	
	// 并发控制
	MaxConcurrency     int           `json:"max_concurrency"`
	ScanDelay          time.Duration `json:"scan_delay"`
	
	// 高级配置
	OSDetection        bool          `json:"os_detection"`
	VersionDetection   bool          `json:"version_detection"`
	ScriptScan         bool          `json:"script_scan"`
}

// PortScanner 端口扫描器
type PortScanner struct {
	config    *NetworkVulnConfig
	semaphore chan struct{}
}

// ServiceScanner 服务扫描器
type ServiceScanner struct {
	config     *NetworkVulnConfig
	signatures map[int]*ServiceSignature
}

// ProtocolScanner 协议扫描器
type ProtocolScanner struct {
	config *NetworkVulnConfig
}

// ServiceVulnerabilityDetector 服务漏洞检测器
type ServiceVulnerabilityDetector struct {
	vulnDatabase map[string]*ServiceVulnerability
}

// ProtocolVulnerabilityDetector 协议漏洞检测器
type ProtocolVulnerabilityDetector struct {
	vulnDatabase map[string]*ProtocolVulnerability
}

// ServiceSignature 服务签名
type ServiceSignature struct {
	Port        int      `json:"port"`
	Protocol    string   `json:"protocol"`
	Service     string   `json:"service"`
	Probes      []string `json:"probes"`
	Patterns    []string `json:"patterns"`
	Confidence  float64  `json:"confidence"`
}

// ServiceVulnerability 服务漏洞
type ServiceVulnerability struct {
	ID          string   `json:"id"`
	Service     string   `json:"service"`
	Version     string   `json:"version"`
	CVE         string   `json:"cve"`
	Severity    string   `json:"severity"`
	Description string   `json:"description"`
	Exploit     string   `json:"exploit"`
	References  []string `json:"references"`
}

// ProtocolVulnerability 协议漏洞
type ProtocolVulnerability struct {
	ID          string   `json:"id"`
	Protocol    string   `json:"protocol"`
	CVE         string   `json:"cve"`
	Severity    string   `json:"severity"`
	Description string   `json:"description"`
	Exploit     string   `json:"exploit"`
	References  []string `json:"references"`
}

// PortScanResult 端口扫描结果
type PortScanResult struct {
	Host      string        `json:"host"`
	Port      int           `json:"port"`
	Protocol  string        `json:"protocol"`
	State     string        `json:"state"`
	Service   string        `json:"service"`
	Version   string        `json:"version"`
	Banner    string        `json:"banner"`
	Response  string        `json:"response"`
	Latency   time.Duration `json:"latency"`
	Timestamp time.Time     `json:"timestamp"`
}

// ServiceScanResult 服务扫描结果
type ServiceScanResult struct {
	Host        string            `json:"host"`
	Port        int               `json:"port"`
	Service     string            `json:"service"`
	Version     string            `json:"version"`
	Product     string            `json:"product"`
	ExtraInfo   string            `json:"extra_info"`
	Confidence  float64           `json:"confidence"`
	Fingerprint map[string]string `json:"fingerprint"`
	Timestamp   time.Time         `json:"timestamp"`
}

// NewNetworkVulnerabilityEngine 创建网络漏洞扫描引擎
func NewNetworkVulnerabilityEngine() *NetworkVulnerabilityEngine {
	baseEngine := core.NewBaseEngine(
		"网络漏洞扫描引擎",
		core.EngineTypeNetworkVulnerability,
		"1.0.0",
		"负责检测网络层面的漏洞，包括端口扫描、服务漏洞、协议漏洞等",
	)
	
	config := &NetworkVulnConfig{
		PortScanEnabled:      true,
		PortRange:            "1-1000,3306,5432,6379,27017",
		ScanTimeout:          30 * time.Second,
		ConnectTimeout:       3 * time.Second,
		ServiceDetection:     true,
		BannerGrabbing:       true,
		ServiceTimeout:       10 * time.Second,
		VulnDetectionEnabled: true,
		CVECheckEnabled:      true,
		WeakPasswordCheck:    true,
		MaxConcurrency:       100,
		ScanDelay:            10 * time.Millisecond,
		OSDetection:          true,
		VersionDetection:     true,
		ScriptScan:           true,
	}
	
	// 创建规则引擎
	ruleEngine := core.NewRuleEngine("rules")
	
	engine := &NetworkVulnerabilityEngine{
		BaseEngine:           baseEngine,
		config:               config,
		portScanner:          NewPortScanner(config),
		serviceScanner:       NewServiceScanner(config),
		protocolScanner:      NewProtocolScanner(config),
		serviceVulnDetector:  NewServiceVulnerabilityDetector(),
		protocolVulnDetector: NewProtocolVulnerabilityDetector(),
		ruleEngine:           ruleEngine,
	}
	
	return engine
}

// GetSupportedTargets 获取支持的目标类型
func (e *NetworkVulnerabilityEngine) GetSupportedTargets() []core.TargetType {
	return []core.TargetType{
		core.TargetTypeIP,
		core.TargetTypeDomain,
		core.TargetTypeHost,
		core.TargetTypeNetwork,
	}
}

// Scan 执行网络漏洞扫描
func (e *NetworkVulnerabilityEngine) Scan(ctx context.Context, request *core.ScanRequest) (*core.ScanResult, error) {
	// 创建任务上下文
	task, taskCtx := e.CreateTaskContext(request)
	defer func() {
		if task.CancelFunc != nil {
			task.CancelFunc()
		}
	}()
	
	e.LogInfo("开始网络漏洞扫描: %s", request.Target.Value)
	
	// 创建扫描结果
	result := &core.ScanResult{
		ID:              fmt.Sprintf("network_%s_%d", request.ID, time.Now().Unix()),
		TaskID:          request.Config.TaskID,
		EngineType:      e.GetType(),
		Target:          request.Target,
		Status:          core.StatusRunning,
		Progress:        0.0,
		StartedAt:       time.Now(),
		Vulnerabilities: make([]*core.Vulnerability, 0),
		Statistics:      &core.ScanStatistics{},
		Warnings:        make([]string, 0),
		Metadata:        make(map[string]interface{}),
	}
	
	// 执行扫描步骤
	steps := []struct {
		name     string
		progress float64
		function func(context.Context, *core.ScanRequest, *core.ScanResult) error
	}{
		{"目标解析", 10, e.resolveTarget},
		{"端口扫描", 30, e.scanPorts},
		{"服务识别", 50, e.identifyServices},
		{"协议检测", 60, e.detectProtocols},
		{"漏洞检测", 80, e.detectVulnerabilities},
		{"CVE检查", 90, e.checkCVEs},
		{"结果分析", 100, e.analyzeNetworkResults},
	}
	
	for _, step := range steps {
		select {
		case <-taskCtx.Done():
			result.Status = core.StatusCancelled
			e.CompleteTask(request.ID, result, fmt.Errorf("扫描被取消"))
			return result, fmt.Errorf("扫描被取消")
		default:
		}
		
		e.LogInfo("执行步骤: %s", step.name)
		e.UpdateTaskProgress(request.ID, step.progress, step.name)
		
		if err := step.function(taskCtx, request, result); err != nil {
			e.LogError("步骤执行失败 %s: %v", step.name, err)
			result.Warnings = append(result.Warnings, fmt.Sprintf("步骤 %s 执行失败: %v", step.name, err))
		}
		
		// 添加扫描延迟
		time.Sleep(e.config.ScanDelay)
	}
	
	// 完成扫描
	result.Status = core.StatusCompleted
	result.Progress = 100.0
	completedAt := time.Now()
	result.CompletedAt = &completedAt
	result.Duration = time.Since(result.StartedAt)
	
	// 更新统计信息
	e.updateNetworkStatistics(result)
	
	e.LogInfo("网络漏洞扫描完成: %s，发现 %d 个漏洞", request.Target.Value, len(result.Vulnerabilities))
	e.CompleteTask(request.ID, result, nil)
	
	return result, nil
}

// resolveTarget 解析目标
func (e *NetworkVulnerabilityEngine) resolveTarget(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	target := request.Target
	
	switch target.Type {
	case core.TargetTypeIP:
		// IP地址直接使用
		result.Metadata["resolved_ips"] = []string{target.Value}
		
	case core.TargetTypeDomain, core.TargetTypeHost:
		// 域名需要DNS解析
		ips, err := net.LookupIP(target.Value)
		if err != nil {
			return fmt.Errorf("DNS解析失败: %v", err)
		}
		
		resolvedIPs := make([]string, 0, len(ips))
		for _, ip := range ips {
			resolvedIPs = append(resolvedIPs, ip.String())
		}
		result.Metadata["resolved_ips"] = resolvedIPs
		
	case core.TargetTypeNetwork:
		// 网络段需要展开为IP列表
		ips, err := e.expandNetworkRange(target.Value)
		if err != nil {
			return fmt.Errorf("网络段解析失败: %v", err)
		}
		result.Metadata["resolved_ips"] = ips
		
	default:
		return fmt.Errorf("不支持的目标类型: %s", target.Type)
	}
	
	e.LogInfo("目标解析完成，共 %d 个IP地址", len(result.Metadata["resolved_ips"].([]string)))
	return nil
}

// expandNetworkRange 展开网络段
func (e *NetworkVulnerabilityEngine) expandNetworkRange(network string) ([]string, error) {
	_, ipNet, err := net.ParseCIDR(network)
	if err != nil {
		return nil, err
	}
	
	var ips []string
	for ip := ipNet.IP.Mask(ipNet.Mask); ipNet.Contains(ip); e.incrementIP(ip) {
		ips = append(ips, ip.String())
		// 限制最大IP数量，避免扫描过大的网络段
		if len(ips) >= 1000 {
			e.LogWarn("网络段过大，限制扫描前1000个IP")
			break
		}
	}
	
	return ips, nil
}

// incrementIP 递增IP地址
func (e *NetworkVulnerabilityEngine) incrementIP(ip net.IP) {
	for j := len(ip) - 1; j >= 0; j-- {
		ip[j]++
		if ip[j] > 0 {
			break
		}
	}
}

// scanPorts 扫描端口
func (e *NetworkVulnerabilityEngine) scanPorts(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	if !e.config.PortScanEnabled {
		return nil
	}
	
	resolvedIPs := result.Metadata["resolved_ips"].([]string)
	var allResults []*PortScanResult
	
	for _, ip := range resolvedIPs {
		e.LogInfo("扫描主机端口: %s", ip)
		
		results, err := e.portScanner.ScanHost(ctx, ip)
		if err != nil {
			e.LogWarn("端口扫描失败 %s: %v", ip, err)
			continue
		}
		
		allResults = append(allResults, results...)
	}
	
	result.Metadata["port_scan_results"] = allResults
	e.LogInfo("端口扫描完成，发现 %d 个开放端口", len(allResults))
	
	return nil
}

// identifyServices 识别服务
func (e *NetworkVulnerabilityEngine) identifyServices(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	if !e.config.ServiceDetection {
		return nil
	}
	
	portResults, exists := result.Metadata["port_scan_results"]
	if !exists {
		return nil
	}
	
	portScanResults := portResults.([]*PortScanResult)
	var serviceResults []*ServiceScanResult
	
	for _, portResult := range portScanResults {
		if portResult.State != "open" {
			continue
		}
		
		e.LogDebug("识别服务: %s:%d", portResult.Host, portResult.Port)
		
		serviceResult, err := e.serviceScanner.IdentifyService(ctx, portResult)
		if err != nil {
			e.LogWarn("服务识别失败 %s:%d: %v", portResult.Host, portResult.Port, err)
			continue
		}
		
		if serviceResult != nil {
			serviceResults = append(serviceResults, serviceResult)
		}
	}
	
	result.Metadata["service_scan_results"] = serviceResults
	e.LogInfo("服务识别完成，识别 %d 个服务", len(serviceResults))
	
	return nil
}

// detectProtocols 检测协议
func (e *NetworkVulnerabilityEngine) detectProtocols(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	// 协议检测逻辑
	e.LogInfo("协议检测完成")
	return nil
}

// detectVulnerabilities 检测漏洞
func (e *NetworkVulnerabilityEngine) detectVulnerabilities(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	if !e.config.VulnDetectionEnabled {
		return nil
	}
	
	serviceResults, exists := result.Metadata["service_scan_results"]
	if !exists {
		return nil
	}
	
	serviceScanResults := serviceResults.([]*ServiceScanResult)
	
	for _, serviceResult := range serviceScanResults {
		// 检测服务漏洞
		vulns := e.serviceVulnDetector.DetectVulnerabilities(serviceResult)
		for _, vuln := range vulns {
			result.Vulnerabilities = append(result.Vulnerabilities, e.convertServiceVulnToVulnerability(vuln, serviceResult))
		}
	}
	
	e.LogInfo("漏洞检测完成，发现 %d 个漏洞", len(result.Vulnerabilities))
	return nil
}

// checkCVEs 检查CVE漏洞
func (e *NetworkVulnerabilityEngine) checkCVEs(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	if !e.config.CVECheckEnabled {
		return nil
	}
	
	// 初始化规则引擎
	if err := e.ruleEngine.Initialize(); err != nil {
		e.LogError("规则引擎初始化失败: %v", err)
		return err
	}
	
	// 执行CVE检查
	e.LogInfo("CVE检查完成")
	return nil
}

// analyzeNetworkResults 分析网络扫描结果
func (e *NetworkVulnerabilityEngine) analyzeNetworkResults(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	// 统计漏洞
	criticalCount := 0
	highCount := 0
	mediumCount := 0
	lowCount := 0
	infoCount := 0
	
	for _, vuln := range result.Vulnerabilities {
		switch vuln.Severity {
		case core.SeverityCritical:
			criticalCount++
		case core.SeverityHigh:
			highCount++
		case core.SeverityMedium:
			mediumCount++
		case core.SeverityLow:
			lowCount++
		case core.SeverityInfo:
			infoCount++
		}
	}
	
	// 更新统计信息
	result.Statistics.TotalVulns = len(result.Vulnerabilities)
	result.Statistics.CriticalVulns = criticalCount
	result.Statistics.HighVulns = highCount
	result.Statistics.MediumVulns = mediumCount
	result.Statistics.LowVulns = lowCount
	result.Statistics.InfoVulns = infoCount
	
	return nil
}

// updateNetworkStatistics 更新网络扫描统计信息
func (e *NetworkVulnerabilityEngine) updateNetworkStatistics(result *core.ScanResult) {
	resolvedIPs := result.Metadata["resolved_ips"].([]string)
	result.Statistics.TotalTargets = len(resolvedIPs)
	result.Statistics.ScannedTargets = len(resolvedIPs)
	result.Statistics.ScanDuration = result.Duration
	
	// 计算端口扫描统计
	if portResults, exists := result.Metadata["port_scan_results"]; exists {
		portScanResults := portResults.([]*PortScanResult)
		result.Statistics.RequestsSent = len(portScanResults)
		result.Statistics.ResponsesReceived = len(portScanResults)
	}
}

// convertServiceVulnToVulnerability 将服务漏洞转换为漏洞信息
func (e *NetworkVulnerabilityEngine) convertServiceVulnToVulnerability(serviceVuln *ServiceVulnerability, serviceResult *ServiceScanResult) *core.Vulnerability {
	vuln := &core.Vulnerability{
		ID:          fmt.Sprintf("service_%s_%d", serviceVuln.ID, time.Now().Unix()),
		Name:        fmt.Sprintf("%s 服务漏洞", serviceResult.Service),
		Type:        "service_vulnerability",
		URL:         fmt.Sprintf("%s:%d", serviceResult.Host, serviceResult.Port),
		Description: serviceVuln.Description,
		CVE:         serviceVuln.CVE,
		References:  serviceVuln.References,
		Confidence:  0.8,
		DiscoveredAt: time.Now(),
	}
	
	// 设置严重程度
	switch serviceVuln.Severity {
	case "critical":
		vuln.Severity = core.SeverityCritical
		vuln.CVSS = 9.0
		vuln.Risk = core.RiskCritical
	case "high":
		vuln.Severity = core.SeverityHigh
		vuln.CVSS = 7.5
		vuln.Risk = core.RiskHigh
	case "medium":
		vuln.Severity = core.SeverityMedium
		vuln.CVSS = 5.0
		vuln.Risk = core.RiskMedium
	case "low":
		vuln.Severity = core.SeverityLow
		vuln.CVSS = 2.5
		vuln.Risk = core.RiskLow
	default:
		vuln.Severity = core.SeverityInfo
		vuln.CVSS = 0.0
		vuln.Risk = core.RiskInfo
	}
	
	return vuln
}
