package asset

import (
	"context"
	"errors"
	"fmt"
	"net"
	"net/url"
	"strings"
	"sync"
	"time"

	"scanner/internal/models"
	"scanner/internal/repository"
	"scanner/internal/scanner/engines"
	"scanner/internal/scanner/types"
	"scanner/pkg/logger"
)

// ScanServiceInterface 扫描服务接口，避免循环依赖
type ScanServiceInterface interface {
	CreateTask(task *models.ScanTask) error
	UpdateTaskStatus(taskID uint, status string) error
	UpdateTaskProgress(taskID uint, progress int, stage, message string) error
	UpdateTaskStatistics(taskID uint, stats map[string]interface{}) error
}

// ScanLogServiceInterface 扫描日志服务接口，避免循环依赖
type ScanLogServiceInterface interface {
	DeleteAllLogs() error
	LogInfo(taskID uint, stage, target, message string, progress int) error
	LogDebug(taskID uint, stage, target, message, details string, progress int) error
	LogWarn(taskID uint, stage, target, message, details string, progress int) error
	LogError(taskID uint, stage, target, message, details string, progress int) error
}

type Service struct {
	assetRepo      *repository.AssetRepository
	scanService    ScanServiceInterface
	scanLogService ScanLogServiceInterface
	// 临时禁用标识符以避免编译错误
	// databaseIdentifier      *engines.DatabaseIdentifier
	// networkDeviceIdentifier *engines.NetworkDeviceIdentifier
}

type CreateAssetRequest struct {
	Name        string `json:"name" binding:"required,min=1,max=100"`
	Type        string `json:"type" binding:"required,oneof=web host network database api"`
	IP          string `json:"ip"`
	Domain      string `json:"domain"`
	Port        int    `json:"port"`
	Protocol    string `json:"protocol"`
	Tags        string `json:"tags"`
	Owner       string `json:"owner"`
	Description string `json:"description"`
	Status      string `json:"status"`
}

type UpdateAssetRequest struct {
	Name        string `json:"name" binding:"required,min=1,max=100"`
	Type        string `json:"type" binding:"required,oneof=web host network database api"`
	IP          string `json:"ip"`
	Domain      string `json:"domain"`
	Port        int    `json:"port"`
	Protocol    string `json:"protocol"`
	Tags        string `json:"tags"`
	Owner       string `json:"owner"`
	Description string `json:"description"`
	Status      string `json:"status" binding:"oneof=active inactive"`
}

type AssetResponse struct {
	ID            uint   `json:"id"`
	Name          string `json:"name"`
	Type          string `json:"type"`
	IP            string `json:"ip"`
	Domain        string `json:"domain"`
	Port          int    `json:"port"`
	Protocol      string `json:"protocol"`
	Tags          string `json:"tags"`
	Owner         string `json:"owner"`
	Status        string `json:"status"`
	LastScan      string `json:"last_scan"`
	VulnCount     int    `json:"vuln_count"`
	HighRiskCount int    `json:"high_risk_count"`
	CreatedAt     string `json:"created_at"`
	UpdatedAt     string `json:"updated_at"` // 添加更新时间字段
}

func NewService(assetRepo *repository.AssetRepository, scanService ScanServiceInterface, scanLogService ScanLogServiceInterface) *Service {
	return &Service{
		assetRepo:      assetRepo,
		scanService:    scanService,
		scanLogService: scanLogService,
		// 临时禁用标识符初始化
		// databaseIdentifier:      engines.NewDatabaseIdentifier(),
		// networkDeviceIdentifier: engines.NewNetworkDeviceIdentifier(),
	}
}

// Create 创建资产
func (s *Service) Create(req CreateAssetRequest) (*models.Asset, error) {
	// 验证资产信息
	if err := s.validateAsset(req.Type, req.IP, req.Domain, req.Port); err != nil {
		return nil, err
	}

	// 检查重复
	if err := s.checkDuplicate(req.IP, req.Domain, req.Port); err != nil {
		return nil, err
	}

	// 创建资产
	status := req.Status
	if status == "" {
		status = "active"
	}

	asset := &models.Asset{
		Name:        req.Name,
		Type:        req.Type,
		IP:          req.IP,
		Domain:      req.Domain,
		Port:        req.Port,
		Protocol:    req.Protocol,
		Tags:        req.Tags,
		Owner:       req.Owner,
		Description: req.Description,
		Status:      status,
	}

	if err := s.assetRepo.Create(asset); err != nil {
		return nil, fmt.Errorf("创建资产失败: %w", err)
	}

	return asset, nil
}

// GetByID 根据ID获取资产
func (s *Service) GetByID(id uint) (*models.Asset, error) {
	return s.assetRepo.GetByID(id)
}

// Update 更新资产
func (s *Service) Update(id uint, req UpdateAssetRequest) (*models.Asset, error) {
	// 获取现有资产
	asset, err := s.assetRepo.GetByID(id)
	if err != nil {
		return nil, errors.New("资产不存在")
	}

	// 验证资产信息
	if err := s.validateAsset(req.Type, req.IP, req.Domain, req.Port); err != nil {
		return nil, err
	}

	// 检查重复（排除自己）
	if asset.IP != req.IP || asset.Domain != req.Domain || asset.Port != req.Port {
		if err := s.checkDuplicate(req.IP, req.Domain, req.Port); err != nil {
			return nil, err
		}
	}

	// 更新资产信息
	asset.Name = req.Name
	asset.Type = req.Type
	asset.IP = req.IP
	asset.Domain = req.Domain
	asset.Port = req.Port
	asset.Protocol = req.Protocol
	asset.Tags = req.Tags
	asset.Owner = req.Owner
	asset.Description = req.Description
	asset.Status = req.Status

	if err := s.assetRepo.Update(asset); err != nil {
		return nil, fmt.Errorf("更新资产失败: %w", err)
	}

	return asset, nil
}

// Delete 删除资产
func (s *Service) Delete(id uint) error {
	// 检查资产是否存在
	_, err := s.assetRepo.GetByID(id)
	if err != nil {
		return errors.New("资产不存在")
	}

	// TODO: 检查是否有关联的扫描任务或漏洞

	return s.assetRepo.Delete(id)
}

// BatchDelete 批量删除资产
func (s *Service) BatchDelete(ids []uint) error {
	// 直接进行批量物理删除，不进行逐个检查以提高性能
	// TODO: 检查是否有关联的扫描任务或漏洞

	return s.assetRepo.BatchDelete(ids)
}

// List 获取资产列表
func (s *Service) List(filter repository.AssetFilter) ([]models.Asset, int64, error) {
	return s.assetRepo.List(filter)
}

// GetStatistics 获取资产统计信息
func (s *Service) GetStatistics() (map[string]interface{}, error) {
	// 按类型统计
	typeStats, err := s.assetRepo.CountByType()
	if err != nil {
		return nil, err
	}

	// 按状态统计
	statusStats, err := s.assetRepo.CountByStatus()
	if err != nil {
		return nil, err
	}

	// 总数统计
	total := int64(0)
	for _, count := range typeStats {
		total += count
	}

	return map[string]interface{}{
		"total":     total,
		"by_type":   typeStats,
		"by_status": statusStats,
	}, nil
}

// DiscoveryRequest 资产发现请求结构
type DiscoveryRequest struct {
	Targets []string `json:"targets"`
	Type    string   `json:"type"` // network, port, subdomain
	Options struct {
		Timeout  int    `json:"timeout"`
		Threads  int    `json:"threads"`
		Depth    int    `json:"depth"`    // 子域名发现深度
		Wordlist string `json:"wordlist"` // 子域名字典类型
	} `json:"options"`
}

// DiscoverAssets 资产发现 - 支持多种发现类型
func (s *Service) DiscoverAssets(targets []string) ([]models.Asset, uint, error) {
	return s.DiscoverAssetsWithType(targets, "network", nil)
}

// DiscoverAssetsWithType 带类型的资产发现
func (s *Service) DiscoverAssetsWithType(targets []string, discoveryType string, options map[string]interface{}) ([]models.Asset, uint, error) {
	// 根据发现类型设置任务名称
	var taskName string
	switch discoveryType {
	case "subdomain":
		taskName = fmt.Sprintf("子域名发现任务 - %s", time.Now().Format("2006-01-02 15:04:05"))
	case "port":
		taskName = fmt.Sprintf("端口扫描任务 - %s", time.Now().Format("2006-01-02 15:04:05"))
	default:
		taskName = fmt.Sprintf("网络发现任务 - %s", time.Now().Format("2006-01-02 15:04:05"))
	}

	// 创建扫描任务
	task := &models.ScanTask{
		Name:        taskName,
		Type:        fmt.Sprintf("%s_discovery", discoveryType),
		Status:      "pending", // 初始状态为待执行
		Targets:     strings.Join(targets, "\n"),
		Description: fmt.Sprintf("%s发现扫描，目标数量: %d", getDiscoveryTypeText(discoveryType), len(targets)),
		Progress:    0,
	}

	// 创建扫描任务记录
	if err := s.scanService.CreateTask(task); err != nil {
		return nil, 0, fmt.Errorf("创建扫描任务失败: %w", err)
	}

	logger.Infof("创建%s发现扫描任务，ID: %d", getDiscoveryTypeText(discoveryType), task.ID)

	// 异步执行资产发现扫描
	go s.performAsyncDiscoveryWithType(task.ID, targets, discoveryType, options)

	// 立即返回任务信息，不等待扫描完成
	return []models.Asset{}, task.ID, nil
}

// getDiscoveryTypeText 获取发现类型的中文描述
func getDiscoveryTypeText(discoveryType string) string {
	switch discoveryType {
	case "subdomain":
		return "子域名"
	case "port":
		return "端口"
	case "network":
		return "网络"
	default:
		return "资产"
	}
}

// performAsyncDiscoveryWithType 异步执行带类型的资产发现扫描
func (s *Service) performAsyncDiscoveryWithType(taskID uint, targets []string, discoveryType string, options map[string]interface{}) {
	logger.Infof("开始异步执行%s发现任务，ID: %d, 发现类型: %s", getDiscoveryTypeText(discoveryType), taskID, discoveryType)
	fmt.Printf("=== [performAsyncDiscoveryWithType] 发现类型: %s, 目标: %v ===\n", discoveryType, targets)

	// 记录任务开始日志
	if s.scanLogService != nil {
		s.scanLogService.LogInfo(taskID, "初始化", "", fmt.Sprintf("开始%s发现扫描任务", getDiscoveryTypeText(discoveryType)), 0)
		s.scanLogService.LogInfo(taskID, "初始化", "", fmt.Sprintf("扫描目标数量: %d", len(targets)), 5)
		for i, target := range targets {
			s.scanLogService.LogDebug(taskID, "初始化", target, fmt.Sprintf("目标 %d: %s", i+1, target), "", 5)
		}
	}

	// 更新任务状态为运行中
	if err := s.scanService.UpdateTaskStatus(taskID, "running"); err != nil {
		logger.Errorf("更新任务状态失败: %v", err)
		if s.scanLogService != nil {
			s.scanLogService.LogError(taskID, "初始化", "", "更新任务状态失败", err.Error(), 0)
		}
		return
	}

	// 更新任务进度
	if err := s.scanService.UpdateTaskProgress(taskID, 10, "初始化", "开始资产发现扫描"); err != nil {
		logger.Errorf("更新任务进度失败: %v", err)
		if s.scanLogService != nil {
			s.scanLogService.LogWarn(taskID, "初始化", "", "更新任务进度失败", err.Error(), 10)
		}
	}

	var discoveredAssets []models.Asset
	var scanResults []*types.ScanResult
	var err error

	// 记录扫描开始日志
	if s.scanLogService != nil {
		s.scanLogService.LogInfo(taskID, "扫描执行", "", fmt.Sprintf("开始执行%s发现扫描", getDiscoveryTypeText(discoveryType)), 15)
	}

	// 根据发现类型执行不同的扫描
	switch discoveryType {
	case "subdomain":
		if s.scanLogService != nil {
			s.scanLogService.LogInfo(taskID, "扫描执行", "", "执行子域名发现扫描", 20)
		}
		scanResults, err = s.performSubdomainDiscoveryWithTaskID(taskID, targets, options)
	case "port":
		if s.scanLogService != nil {
			s.scanLogService.LogInfo(taskID, "扫描执行", "", "执行端口发现扫描", 20)
		}
		scanResults, err = s.performPortDiscoveryWithTaskID(taskID, targets, options)
	default:
		// 默认使用网络发现
		if s.scanLogService != nil {
			s.scanLogService.LogInfo(taskID, "扫描执行", "", "执行网络发现扫描", 20)
		}
		scanResults, err = s.performNetworkDiscoveryWithTaskID(taskID, targets)
	}

	if err != nil {
		// 记录错误日志
		if s.scanLogService != nil {
			s.scanLogService.LogError(taskID, "扫描执行", "", fmt.Sprintf("%s发现扫描失败", getDiscoveryTypeText(discoveryType)), err.Error(), 0)
		}
		// 更新任务状态为失败
		s.scanService.UpdateTaskStatus(taskID, "failed")
		s.scanService.UpdateTaskProgress(taskID, 0, "失败", fmt.Sprintf("%s发现失败: %v", getDiscoveryTypeText(discoveryType), err))
		logger.Errorf("%s发现任务失败，ID: %d, 错误: %v", getDiscoveryTypeText(discoveryType), taskID, err)
		return
	}

	// 记录扫描完成日志
	if s.scanLogService != nil {
		s.scanLogService.LogInfo(taskID, "结果处理", "", fmt.Sprintf("扫描完成，获得 %d 个扫描结果", len(scanResults)), 60)
	}

	// 更新进度
	if err := s.scanService.UpdateTaskProgress(taskID, 60, "处理结果", "正在处理扫描结果"); err != nil {
		logger.Errorf("更新任务进度失败: %v", err)
		if s.scanLogService != nil {
			s.scanLogService.LogWarn(taskID, "结果处理", "", "更新任务进度失败", err.Error(), 60)
		}
	}

	// 将扫描结果转换为资产
	for i, result := range scanResults {
		if s.scanLogService != nil {
			s.scanLogService.LogDebug(taskID, "结果处理", result.TargetID, fmt.Sprintf("处理扫描结果 %d/%d", i+1, len(scanResults)), "", 60+i*15/len(scanResults))
		}

		assets := s.convertScanResultToAssets(result)
		for _, asset := range assets {
			// 检查是否已存在
			if err := s.checkDuplicate(asset.IP, asset.Domain, asset.Port); err != nil {
				if s.scanLogService != nil {
					s.scanLogService.LogDebug(taskID, "结果处理", asset.IP, "跳过重复资产", err.Error(), 60+i*15/len(scanResults))
				}
				continue // 跳过重复资产
			}
			discoveredAssets = append(discoveredAssets, asset)
			if s.scanLogService != nil {
				s.scanLogService.LogInfo(taskID, "结果处理", asset.IP, fmt.Sprintf("发现新资产: %s (%s)", asset.Name, asset.Type), 60+i*15/len(scanResults))
			}
		}
	}

	// 记录资产发现统计
	if s.scanLogService != nil {
		s.scanLogService.LogInfo(taskID, "结果处理", "", fmt.Sprintf("资产转换完成，共发现 %d 个新资产", len(discoveredAssets)), 75)
	}

	// 更新进度
	if err := s.scanService.UpdateTaskProgress(taskID, 80, "保存资产", "正在保存发现的资产"); err != nil {
		logger.Errorf("更新任务进度失败: %v", err)
		if s.scanLogService != nil {
			s.scanLogService.LogWarn(taskID, "保存资产", "", "更新任务进度失败", err.Error(), 80)
		}
	}

	// 批量创建发现的资产
	if len(discoveredAssets) > 0 {
		if s.scanLogService != nil {
			s.scanLogService.LogInfo(taskID, "保存资产", "", fmt.Sprintf("开始保存 %d 个发现的资产到数据库", len(discoveredAssets)), 85)
		}

		if err := s.assetRepo.BatchCreate(discoveredAssets); err != nil {
			// 记录保存失败日志
			if s.scanLogService != nil {
				s.scanLogService.LogError(taskID, "保存资产", "", "批量创建资产失败", err.Error(), 85)
			}
			// 更新任务状态为失败
			s.scanService.UpdateTaskStatus(taskID, "failed")
			s.scanService.UpdateTaskProgress(taskID, 80, "失败", fmt.Sprintf("批量创建资产失败: %v", err))
			logger.Errorf("资产发现任务失败，ID: %d, 错误: %v", taskID, err)
			return
		}

		if s.scanLogService != nil {
			s.scanLogService.LogInfo(taskID, "保存资产", "", fmt.Sprintf("成功保存 %d 个资产到数据库", len(discoveredAssets)), 95)
		}
	} else {
		if s.scanLogService != nil {
			s.scanLogService.LogInfo(taskID, "保存资产", "", "未发现新资产，跳过保存步骤", 95)
		}
	}

	// 记录任务完成日志
	if s.scanLogService != nil {
		s.scanLogService.LogInfo(taskID, "任务完成", "", fmt.Sprintf("%s发现任务执行完成", getDiscoveryTypeText(discoveryType)), 100)
		s.scanLogService.LogInfo(taskID, "任务完成", "", fmt.Sprintf("任务统计 - 扫描目标: %d, 发现资产: %d", len(targets), len(discoveredAssets)), 100)
	}

	// 更新任务统计信息
	if err := s.updateTaskStatistics(taskID, targets, discoveredAssets, scanResults); err != nil {
		logger.Errorf("更新任务统计信息失败: %v", err)
		if s.scanLogService != nil {
			s.scanLogService.LogWarn(taskID, "任务完成", "", "更新任务统计信息失败", err.Error(), 100)
		}
	}

	// 更新任务状态为完成
	if err := s.scanService.UpdateTaskProgress(taskID, 100, "完成", fmt.Sprintf("资产发现完成，发现 %d 个资产", len(discoveredAssets))); err != nil {
		logger.Errorf("更新任务进度失败: %v", err)
		if s.scanLogService != nil {
			s.scanLogService.LogWarn(taskID, "任务完成", "", "更新任务进度失败", err.Error(), 100)
		}
	}

	if err := s.scanService.UpdateTaskStatus(taskID, "completed"); err != nil {
		logger.Errorf("更新任务状态失败: %v", err)
		if s.scanLogService != nil {
			s.scanLogService.LogWarn(taskID, "任务完成", "", "更新任务状态失败", err.Error(), 100)
		}
	}

	logger.Infof("资产发现任务完成，ID: %d，发现资产数量: %d", taskID, len(discoveredAssets))
}

// updateTaskStatistics 更新任务统计信息
func (s *Service) updateTaskStatistics(taskID uint, targets []string, discoveredAssets []models.Asset, scanResults []*types.ScanResult) error {
	// 计算统计信息
	uniqueHosts := make(map[string]bool)
	openPorts := make(map[string]bool)
	webServices := 0

	// 从发现的资产中统计
	for _, asset := range discoveredAssets {
		if asset.IP != "" {
			uniqueHosts[asset.IP] = true
		}
		if asset.Domain != "" {
			uniqueHosts[asset.Domain] = true
		}
		if asset.Port > 0 {
			portKey := fmt.Sprintf("%s:%d", asset.IP, asset.Port)
			if asset.Domain != "" {
				portKey = fmt.Sprintf("%s:%d", asset.Domain, asset.Port)
			}
			openPorts[portKey] = true
		}
		if asset.Type == "web" {
			webServices++
		}
	}

	// 从扫描结果中补充统计
	for _, result := range scanResults {
		if result.Metadata != nil {
			if host, ok := result.Metadata["host"].(string); ok && host != "" {
				uniqueHosts[host] = true
			}
			if port, ok := result.Metadata["port"].(int); ok && port > 0 {
				if host, ok := result.Metadata["host"].(string); ok {
					portKey := fmt.Sprintf("%s:%d", host, port)
					openPorts[portKey] = true
				}
			}
			if resultType, ok := result.Metadata["type"].(string); ok && resultType == "web" {
				webServices++
			}
		}
	}

	// 记录详细的统计信息到扫描日志
	if s.scanLogService != nil {
		s.scanLogService.LogInfo(taskID, "统计信息", "", fmt.Sprintf("统计结果 - 扫描目标: %d, 唯一主机: %d, 开放端口: %d, Web服务: %d, 发现资产: %d",
			len(targets), len(uniqueHosts), len(openPorts), webServices, len(discoveredAssets)), 98)
	}

	// 更新任务统计字段
	updates := map[string]interface{}{
		"total_targets":    len(targets),
		"scanned_targets":  len(targets),     // 资产发现任务默认扫描所有目标
		"total_requests":   len(scanResults), // 扫描请求数等于扫描结果数
		"success_requests": len(scanResults), // 资产发现任务的成功请求数
		"failed_requests":  0,                // 失败的请求数，这里简化为0
		"updated_at":       time.Now(),
	}

	// 使用扫描服务更新任务统计
	if err := s.scanService.UpdateTaskStatistics(taskID, updates); err != nil {
		return fmt.Errorf("更新任务统计失败: %v", err)
	}

	return nil
}

// validateAsset 验证资产信息
func (s *Service) validateAsset(assetType, ip, domain string, port int) error {
	// 验证IP地址
	if ip != "" {
		if net.ParseIP(ip) == nil {
			return errors.New("无效的IP地址")
		}
	}

	// 验证域名
	if domain != "" {
		if !isValidDomain(domain) {
			return errors.New("无效的域名")
		}
	}

	// 验证端口
	if port < 0 || port > 65535 {
		return errors.New("端口号必须在0-65535之间")
	}

	// 根据资产类型验证必需字段
	switch assetType {
	case "web":
		if domain == "" && ip == "" {
			return errors.New("Web资产必须指定域名或IP地址")
		}
	case "host":
		if ip == "" {
			return errors.New("主机资产必须指定IP地址")
		}
	case "network":
		if ip == "" {
			return errors.New("网络资产必须指定IP地址")
		}
	}

	return nil
}

// checkDuplicate 检查重复资产
func (s *Service) checkDuplicate(ip, domain string, port int) error {
	// 检查IP+端口重复
	if ip != "" {
		if existing, err := s.assetRepo.GetByIP(ip); err == nil && existing.Port == port {
			return errors.New("相同IP和端口的资产已存在")
		}
	}

	// 检查域名重复
	if domain != "" {
		if _, err := s.assetRepo.GetByDomain(domain); err == nil {
			return errors.New("相同域名的资产已存在")
		}
	}

	return nil
}

// parseTarget 解析目标字符串
func (s *Service) parseTarget(target string) (*models.Asset, error) {
	asset := &models.Asset{
		Status: "active",
	}

	// 尝试解析为URL
	if strings.HasPrefix(target, "http://") || strings.HasPrefix(target, "https://") {
		u, err := url.Parse(target)
		if err != nil {
			return nil, err
		}

		asset.Type = "web"
		asset.Domain = u.Hostname()
		asset.Protocol = u.Scheme
		asset.Name = u.Hostname()

		if u.Port() != "" {
			port := 80
			if u.Scheme == "https" {
				port = 443
			}
			asset.Port = port
		}

		return asset, nil
	}

	// 尝试解析为IP地址
	if net.ParseIP(target) != nil {
		asset.Type = "host"
		asset.IP = target
		asset.Name = target
		return asset, nil
	}

	// 尝试解析为域名
	if isValidDomain(target) {
		asset.Type = "web"
		asset.Domain = target
		asset.Name = target
		asset.Port = 80
		asset.Protocol = "http"
		return asset, nil
	}

	return nil, errors.New("无法解析目标")
}

// isValidDomain 验证域名格式
func isValidDomain(domain string) bool {
	if len(domain) == 0 || len(domain) > 253 {
		return false
	}

	// 简单的域名格式验证
	parts := strings.Split(domain, ".")
	if len(parts) < 2 {
		return false
	}

	for _, part := range parts {
		if len(part) == 0 || len(part) > 63 {
			return false
		}
	}

	return true
}

// performNetworkDiscovery 执行网络发现扫描
func (s *Service) performNetworkDiscovery(targets []string) ([]*types.ScanResult, error) {
	return s.performNetworkDiscoveryWithTaskID(0, targets)
}

// performNetworkDiscoveryWithTaskID 执行网络发现扫描（带任务ID用于日志记录）
func (s *Service) performNetworkDiscoveryWithTaskID(taskID uint, targets []string) ([]*types.ScanResult, error) {
	fmt.Printf("=== [performNetworkDiscovery] 开始资产发现扫描 ===\n")

	// 记录网络发现开始日志
	if s.scanLogService != nil && taskID > 0 {
		s.scanLogService.LogInfo(taskID, "网络发现", "", fmt.Sprintf("开始网络发现扫描，目标数量: %d", len(targets)), 25)
		s.scanLogService.LogDebug(taskID, "网络发现", "", "创建网络扫描引擎", "", 25)
	}

	fmt.Printf("[performNetworkDiscovery] 创建网络扫描引擎\n")
	// 创建网络扫描引擎
	networkEngine := engines.NewNetworkEngine()

	var results []*types.ScanResult

	for i, target := range targets {
		currentProgress := 25 + (i * 30 / len(targets)) // 25-55%的进度范围

		if s.scanLogService != nil && taskID > 0 {
			s.scanLogService.LogInfo(taskID, "网络发现", target, fmt.Sprintf("开始扫描目标 %d/%d: %s", i+1, len(targets), target), currentProgress)
		}

		// 创建扫描目标
		scanTarget := &types.ScanTarget{
			ID:    target,
			Value: target,
			Type:  "network",
		}

		// 创建扫描配置
		config := &types.ScanConfig{
			TaskID:  fmt.Sprintf("discovery-%d", time.Now().Unix()),
			Timeout: 30 * time.Second,
			Threads: 10,
		}

		// 创建进度通道
		progress := make(chan *types.ScanProgress, 100)

		// 启动扫描
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
		defer cancel()

		go func(targetName string, taskProgress int) {
			// 消费进度信息并记录到数据库
			for p := range progress {
				logger.Infof("资产发现进度: %s - %d%% - %s", p.Stage, p.Progress, p.Message)
				if s.scanLogService != nil && taskID > 0 {
					s.scanLogService.LogDebug(taskID, "网络发现", targetName, fmt.Sprintf("[%s] %s", p.Stage, p.Message), "", taskProgress)
				}
			}
		}(target, currentProgress)

		result, err := networkEngine.Scan(ctx, scanTarget, config, progress)
		close(progress)

		if err != nil {
			logger.Errorf("扫描目标 %s 失败: %v", target, err)
			if s.scanLogService != nil && taskID > 0 {
				s.scanLogService.LogError(taskID, "网络发现", target, "扫描目标失败", err.Error(), currentProgress)
			}
			continue
		}

		if s.scanLogService != nil && taskID > 0 {
			s.scanLogService.LogInfo(taskID, "网络发现", target, "扫描目标完成", currentProgress+5)
		}

		results = append(results, result)
	}

	if s.scanLogService != nil && taskID > 0 {
		s.scanLogService.LogInfo(taskID, "网络发现", "", fmt.Sprintf("网络发现扫描完成，获得 %d 个结果", len(results)), 55)
	}

	return results, nil
}

// convertScanResultToAssets 将扫描结果转换为资产 - 增强版本
func (s *Service) convertScanResultToAssets(result *types.ScanResult) []models.Asset {
	var assets []models.Asset

	// 处理子域名发现结果
	if resultType, ok := result.Metadata["type"].(string); ok && resultType == "subdomain" {
		if domain, ok := result.Metadata["domain"].(string); ok {
			// 创建子域名资产
			asset := models.Asset{
				Name:        domain,
				Type:        "web", // 子域名通常是Web应用
				Domain:      domain,
				Status:      "active",
				Description: "通过子域名发现自动添加",
			}

			// 尝试解析IP地址
			if ips, err := net.LookupHost(domain); err == nil && len(ips) > 0 {
				asset.IP = ips[0]
			}

			assets = append(assets, asset)
			fmt.Printf("[convertScanResultToAssets] 转换子域名资产: %s -> %s\n", domain, asset.IP)
		}
		return assets
	}

	// 从扫描结果的元数据中提取信息
	if aliveHosts, ok := result.Metadata["alive_hosts"].([]string); ok {
		for _, host := range aliveHosts {
			// 使用增强识别功能分析主机
			hostAssets := s.analyzeHost(host)
			assets = append(assets, hostAssets...)
		}
	}

	// 从开放端口信息中提取服务资产
	if openPorts, ok := result.Metadata["open_ports"].([]map[string]any); ok {
		for _, portInfo := range openPorts {
			host, _ := portInfo["host"].(string)
			port, _ := portInfo["port"].(int)
			service, _ := portInfo["service"].(string)

			if host != "" && port > 0 {
				// 使用增强识别功能分析端口服务
				serviceAsset := s.analyzePortService(host, port, service)
				if serviceAsset != nil {
					assets = append(assets, *serviceAsset)
				}
			}
		}
	}

	return assets
}

// analyzeHost 分析主机类型和特征 (临时简化版本)
func (s *Service) analyzeHost(host string) []models.Asset {
	var assets []models.Asset

	logger.Debugf("开始分析主机: %s", host)

	// 临时简化实现：创建基础主机资产
	asset := models.Asset{
		Name:        fmt.Sprintf("主机-%s", host),
		Type:        "host",
		IP:          host,
		Status:      "active",
		Description: "主机设备（临时识别）",
		Owner:       "",
	}
	assets = append(assets, asset)

	return assets
}

// analyzePortService 分析端口服务 (临时简化版本)
func (s *Service) analyzePortService(host string, port int, service string) *models.Asset {
	logger.Debugf("开始分析端口服务: %s:%d (%s)", host, port, service)

	// 临时简化实现：根据端口创建基础服务资产
	assetType := "service"
	if port == 3306 || port == 5432 || port == 1433 || port == 27017 {
		assetType = "database"
	}

	asset := &models.Asset{
		Name:        fmt.Sprintf("%s:%d", host, port),
		Type:        assetType,
		IP:          host,
		Port:        port,
		Protocol:    "tcp",
		Status:      "active",
		Description: fmt.Sprintf("服务: %s (临时识别)", service),
		Owner:       "",
	}

	// 如果是Web端口，调整类型
	if s.isWebPort(port) {
		asset.Type = "web"
		if port == 443 || port == 8443 {
			asset.Protocol = "https"
		} else {
			asset.Protocol = "http"
		}
	}

	// 如果是域名，设置域名字段
	if !isIPAddress(host) {
		asset.Domain = host
		asset.Name = host
	}

	return asset
}

// isWebPort 检查是否为Web服务端口
func (s *Service) isWebPort(port int) bool {
	webPorts := []int{80, 443, 8080, 8443, 8000, 8888, 9000, 9080, 9443}
	for _, webPort := range webPorts {
		if port == webPort {
			return true
		}
	}
	return false
}

// isIPAddress 检查字符串是否为IP地址
func isIPAddress(str string) bool {
	return net.ParseIP(str) != nil
}

// performSubdomainDiscovery 执行子域名发现扫描
func (s *Service) performSubdomainDiscovery(domains []string, options map[string]interface{}) ([]*types.ScanResult, error) {
	return s.performSubdomainDiscoveryWithTaskID(0, domains, options)
}

// performSubdomainDiscoveryWithTaskID 执行子域名发现扫描（带任务ID用于日志记录）
func (s *Service) performSubdomainDiscoveryWithTaskID(taskID uint, domains []string, options map[string]interface{}) ([]*types.ScanResult, error) {
	fmt.Printf("=== [performSubdomainDiscovery] 开始专业子域名发现扫描 ===\n")

	// 记录子域名发现开始日志
	if s.scanLogService != nil && taskID > 0 {
		s.scanLogService.LogInfo(taskID, "子域名发现", "", fmt.Sprintf("开始子域名发现扫描，域名数量: %d", len(domains)), 25)
	}

	// 解析选项
	depth := 3
	wordlist := "common"
	threads := 50 // 并发线程数
	timeout := 5  // DNS查询超时时间（秒）

	if options != nil {
		if d, ok := options["depth"].(int); ok && d > 0 {
			depth = d
		}
		if w, ok := options["wordlist"].(string); ok && w != "" {
			wordlist = w
		}
		if t, ok := options["threads"].(int); ok && t > 0 {
			threads = t
		}
		if to, ok := options["timeout"].(int); ok && to > 0 {
			timeout = to
		}
	}

	fmt.Printf("[performSubdomainDiscovery] 配置参数 - 深度: %d, 字典: %s, 线程: %d, 超时: %ds\n",
		depth, wordlist, threads, timeout)

	if s.scanLogService != nil && taskID > 0 {
		s.scanLogService.LogDebug(taskID, "子域名发现", "", fmt.Sprintf("扫描配置 - 深度: %d, 字典: %s, 线程: %d, 超时: %ds", depth, wordlist, threads, timeout), "", 30)
	}

	var allResults []*types.ScanResult
	foundSubdomains := make(map[string]bool) // 去重用

	// 为每个域名执行多种子域名发现技术
	for i, domain := range domains {
		domainProgress := 30 + (i * 25 / len(domains)) // 30-55%的进度范围

		fmt.Printf("[performSubdomainDiscovery] 开始发现域名: %s\n", domain)
		if s.scanLogService != nil && taskID > 0 {
			s.scanLogService.LogInfo(taskID, "子域名发现", domain, fmt.Sprintf("开始发现域名 %d/%d: %s", i+1, len(domains), domain), domainProgress)
		}

		var domainResults []*types.ScanResult

		// 1. 字典暴力破解（改进版）
		fmt.Printf("[performSubdomainDiscovery] 执行字典暴力破解...\n")
		if s.scanLogService != nil && taskID > 0 {
			s.scanLogService.LogDebug(taskID, "子域名发现", domain, "执行字典暴力破解", "", domainProgress+1)
		}
		bruteResults := s.performBruteForceSubdomain(domain, wordlist, threads, timeout)
		domainResults = append(domainResults, bruteResults...)

		// 2. 证书透明度日志查询
		fmt.Printf("[performSubdomainDiscovery] 查询证书透明度日志...\n")
		if s.scanLogService != nil && taskID > 0 {
			s.scanLogService.LogDebug(taskID, "子域名发现", domain, "查询证书透明度日志", "", domainProgress+2)
		}
		ctResults := s.performCertificateTransparency(domain)
		domainResults = append(domainResults, ctResults...)

		// 3. DNS区域传输尝试
		fmt.Printf("[performSubdomainDiscovery] 尝试DNS区域传输...\n")
		if s.scanLogService != nil && taskID > 0 {
			s.scanLogService.LogDebug(taskID, "子域名发现", domain, "尝试DNS区域传输", "", domainProgress+3)
		}
		zoneResults := s.performDNSZoneTransfer(domain)
		domainResults = append(domainResults, zoneResults...)

		// 4. 搜索引擎查询
		fmt.Printf("[performSubdomainDiscovery] 执行搜索引擎查询...\n")
		if s.scanLogService != nil && taskID > 0 {
			s.scanLogService.LogDebug(taskID, "子域名发现", domain, "执行搜索引擎查询", "", domainProgress+4)
		}
		searchResults := s.performSearchEngineQuery(domain)
		domainResults = append(domainResults, searchResults...)

		// 5. 递归子域名发现（如果深度 > 1）
		if depth > 1 {
			fmt.Printf("[performSubdomainDiscovery] 执行递归子域名发现...\n")
			if s.scanLogService != nil && taskID > 0 {
				s.scanLogService.LogDebug(taskID, "子域名发现", domain, fmt.Sprintf("执行递归子域名发现，深度: %d", depth-1), "", domainProgress+5)
			}
			recursiveResults := s.performRecursiveSubdomain(domain, depth-1, wordlist, threads, timeout)
			domainResults = append(domainResults, recursiveResults...)
		}

		// 去重并添加到总结果
		domainFoundCount := 0
		for _, result := range domainResults {
			if subdomain, ok := result.Metadata["domain"].(string); ok {
				if !foundSubdomains[subdomain] {
					foundSubdomains[subdomain] = true
					allResults = append(allResults, result)
					domainFoundCount++
					if s.scanLogService != nil && taskID > 0 {
						s.scanLogService.LogInfo(taskID, "子域名发现", domain, fmt.Sprintf("发现新子域名: %s", subdomain), domainProgress+5)
					}
				}
			}
		}

		if s.scanLogService != nil && taskID > 0 {
			s.scanLogService.LogInfo(taskID, "子域名发现", domain, fmt.Sprintf("域名 %s 发现完成，新增 %d 个子域名", domain, domainFoundCount), domainProgress+6)
		}
	}

	fmt.Printf("[performSubdomainDiscovery] 子域名发现完成，共发现 %d 个唯一子域名\n", len(allResults))
	if s.scanLogService != nil && taskID > 0 {
		s.scanLogService.LogInfo(taskID, "子域名发现", "", fmt.Sprintf("子域名发现扫描完成，共发现 %d 个唯一子域名", len(allResults)), 55)
	}
	return allResults, nil
}

// performPortDiscovery 执行端口发现扫描
func (s *Service) performPortDiscovery(targets []string, options map[string]interface{}) ([]*types.ScanResult, error) {
	return s.performPortDiscoveryWithTaskID(0, targets, options)
}

// performPortDiscoveryWithTaskID 执行端口发现扫描（带任务ID用于日志记录）
func (s *Service) performPortDiscoveryWithTaskID(taskID uint, targets []string, options map[string]interface{}) ([]*types.ScanResult, error) {
	fmt.Printf("=== [performPortDiscovery] 开始端口发现扫描 ===\n")

	// 记录端口发现开始日志
	if s.scanLogService != nil && taskID > 0 {
		s.scanLogService.LogInfo(taskID, "端口发现", "", fmt.Sprintf("开始端口发现扫描，目标数量: %d", len(targets)), 25)
	}

	// 常用端口列表
	commonPorts := []int{21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995, 1433, 3306, 3389, 5432, 6379, 8080, 8443}

	if s.scanLogService != nil && taskID > 0 {
		s.scanLogService.LogDebug(taskID, "端口发现", "", fmt.Sprintf("扫描端口列表: %v", commonPorts), "", 30)
	}

	var results []*types.ScanResult

	for i, target := range targets {
		targetProgress := 30 + (i * 25 / len(targets)) // 30-55%的进度范围

		fmt.Printf("[performPortDiscovery] 开始扫描目标: %s\n", target)
		if s.scanLogService != nil && taskID > 0 {
			s.scanLogService.LogInfo(taskID, "端口发现", target, fmt.Sprintf("开始扫描目标 %d/%d: %s", i+1, len(targets), target), targetProgress)
		}

		openPorts := 0
		for j, port := range commonPorts {
			portProgress := targetProgress + (j * 20 / len(commonPorts))

			if s.checkPort(target, port) {
				fmt.Printf("[performPortDiscovery] 发现开放端口: %s:%d\n", target, port)
				if s.scanLogService != nil && taskID > 0 {
					s.scanLogService.LogInfo(taskID, "端口发现", target, fmt.Sprintf("发现开放端口: %d", port), portProgress)
				}

				result := &types.ScanResult{
					TaskID:   fmt.Sprintf("port-%d", time.Now().Unix()),
					TargetID: fmt.Sprintf("%s:%d", target, port),
					Status:   "success",
					Progress: 100,
					Metadata: map[string]interface{}{
						"host": target,
						"port": port,
						"type": "port",
					},
				}
				results = append(results, result)
				openPorts++
			}
		}

		if s.scanLogService != nil && taskID > 0 {
			s.scanLogService.LogInfo(taskID, "端口发现", target, fmt.Sprintf("目标 %s 扫描完成，发现 %d 个开放端口", target, openPorts), targetProgress+20)
		}
	}

	fmt.Printf("[performPortDiscovery] 端口发现完成，共发现 %d 个开放端口\n", len(results))
	if s.scanLogService != nil && taskID > 0 {
		s.scanLogService.LogInfo(taskID, "端口发现", "", fmt.Sprintf("端口发现扫描完成，共发现 %d 个开放端口", len(results)), 55)
	}
	return results, nil
}

// getSubdomainWordlist 获取专业子域名字典
func (s *Service) getSubdomainWordlist(wordlistType string) []string {
	switch wordlistType {
	case "fast":
		// 快速扫描：最常见的子域名
		return []string{
			"www", "mail", "ftp", "admin", "api", "test", "dev", "app", "web", "secure",
			"blog", "shop", "support", "help", "mobile", "m", "cdn", "static", "img",
		}
	case "full":
		// 全面扫描：包含大量常见子域名
		return []string{
			// 基础服务
			"www", "mail", "ftp", "admin", "api", "test", "dev", "staging", "prod", "production",
			"app", "web", "secure", "vpn", "remote", "portal", "blog", "shop", "store", "news",
			"support", "help", "docs", "wiki", "forum", "chat", "mobile", "m", "wap", "cdn",

			// 静态资源
			"static", "img", "images", "media", "assets", "files", "download", "upload", "backup",
			"css", "js", "fonts", "icons", "video", "audio", "pics", "photos", "gallery",

			// 数据库和缓存
			"db", "database", "mysql", "sql", "redis", "mongo", "elastic", "search", "solr",
			"cache", "memcache", "postgres", "oracle", "mssql", "sqlite",

			// 开发和部署
			"jenkins", "ci", "build", "deploy", "git", "svn", "repo", "code", "source",
			"gitlab", "github", "bitbucket", "docker", "k8s", "kubernetes",

			// 监控和分析
			"monitor", "status", "health", "metrics", "logs", "analytics", "stats", "reports",
			"grafana", "kibana", "prometheus", "nagios", "zabbix", "splunk",

			// 版本和环境
			"beta", "alpha", "demo", "sandbox", "preview", "temp", "tmp", "old", "new",
			"v1", "v2", "v3", "latest", "stable", "canary",

			// 网络和安全
			"internal", "intranet", "extranet", "private", "public", "external", "partner",
			"vpn", "proxy", "gateway", "firewall", "dns", "ntp", "ldap", "radius",

			// 业务功能
			"client", "customer", "vendor", "supplier", "billing", "payment", "invoice",
			"crm", "erp", "hr", "finance", "accounting", "legal", "compliance", "audit",
			"sales", "marketing", "inventory", "warehouse", "shipping", "tracking",
		}
	case "deep":
		// 深度扫描：包含更多可能的子域名
		return append(s.getSubdomainWordlist("full"), []string{
			// 更多服务
			"smtp", "pop", "imap", "webmail", "exchange", "owa", "autodiscover",
			"sip", "voip", "pbx", "conference", "meet", "zoom", "teams",

			// 更多开发环境
			"local", "localhost", "dev1", "dev2", "test1", "test2", "qa", "uat",
			"preprod", "prod1", "prod2", "live", "www1", "www2", "backup1", "backup2",

			// 更多技术栈
			"apache", "nginx", "tomcat", "jboss", "websphere", "iis", "node", "php",
			"java", "python", "ruby", "go", "dotnet", "laravel", "django", "rails",

			// 更多业务子域名
			"order", "orders", "cart", "checkout", "account", "profile", "dashboard",
			"panel", "control", "manage", "config", "settings", "preferences",

			// 地理位置
			"us", "eu", "asia", "cn", "jp", "uk", "de", "fr", "ca", "au",
			"east", "west", "north", "south", "global", "international",

			// 数字组合
			"1", "2", "3", "01", "02", "03", "001", "002", "003",

			// 常见前缀后缀
			"old-", "new-", "temp-", "backup-", "archive-", "legacy-",
			"-old", "-new", "-temp", "-backup", "-archive", "-legacy",
		}...)
	default: // common
		// 通用扫描：平衡速度和覆盖率
		return []string{
			"www", "mail", "ftp", "admin", "api", "test", "dev", "staging", "prod",
			"app", "web", "secure", "vpn", "remote", "portal", "blog", "shop",
			"support", "help", "docs", "wiki", "forum", "mobile", "m", "cdn",
			"static", "img", "media", "files", "db", "mysql", "redis",
			"jenkins", "git", "monitor", "status", "beta", "demo", "backup",
		}
	}
}

// resolveDomain 解析域名
func (s *Service) resolveDomain(domain string) bool {
	// 简单的DNS解析检查
	_, err := net.LookupHost(domain)
	return err == nil
}

// performBruteForceSubdomain 执行改进的字典暴力破解子域名发现
func (s *Service) performBruteForceSubdomain(domain, wordlist string, threads, timeout int) []*types.ScanResult {
	fmt.Printf("[performBruteForceSubdomain] 开始字典暴力破解，域名: %s\n", domain)

	// 获取子域名字典
	subdomains := s.getSubdomainWordlist(wordlist)

	// 创建结果通道和工作通道
	results := make([]*types.ScanResult, 0)
	subdomainChan := make(chan string, len(subdomains))
	resultChan := make(chan *types.ScanResult, len(subdomains))

	// 填充工作队列
	for _, subdomain := range subdomains {
		subdomainChan <- subdomain
	}
	close(subdomainChan)

	// 启动工作协程
	var wg sync.WaitGroup
	for i := 0; i < threads; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for subdomain := range subdomainChan {
				fullDomain := fmt.Sprintf("%s.%s", subdomain, domain)

				// 设置DNS查询超时
				ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeout)*time.Second)

				// 执行DNS查询
				if ips := s.resolveDomainWithContext(ctx, fullDomain); len(ips) > 0 {
					fmt.Printf("[performBruteForceSubdomain] 发现子域名: %s -> %s\n", fullDomain, ips[0])

					result := &types.ScanResult{
						TaskID:   fmt.Sprintf("brute-%d", time.Now().UnixNano()),
						TargetID: fullDomain,
						Status:   "success",
						Progress: 100,
						Metadata: map[string]interface{}{
							"domain":    fullDomain,
							"subdomain": subdomain,
							"parent":    domain,
							"type":      "subdomain",
							"method":    "brute_force",
							"ip":        ips[0],
							"all_ips":   ips,
						},
					}
					resultChan <- result
				}
				cancel()
			}
		}()
	}

	// 等待所有工作完成
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集结果
	for result := range resultChan {
		results = append(results, result)
	}

	fmt.Printf("[performBruteForceSubdomain] 字典暴力破解完成，发现 %d 个子域名\n", len(results))
	return results
}

// resolveDomainWithContext 带超时的DNS解析
func (s *Service) resolveDomainWithContext(ctx context.Context, domain string) []string {
	resolver := &net.Resolver{
		PreferGo: true,
		Dial: func(ctx context.Context, network, address string) (net.Conn, error) {
			d := net.Dialer{
				Timeout: time.Second * 2,
			}
			return d.DialContext(ctx, network, address)
		},
	}

	ips, err := resolver.LookupHost(ctx, domain)
	if err != nil {
		return nil
	}
	return ips
}

// performCertificateTransparency 通过证书透明度日志查询子域名
func (s *Service) performCertificateTransparency(domain string) []*types.ScanResult {
	fmt.Printf("[performCertificateTransparency] 查询证书透明度日志，域名: %s\n", domain)

	var results []*types.ScanResult

	// 模拟证书透明度查询（实际应该调用crt.sh等API）
	// 这里使用一些常见的证书相关子域名作为示例
	ctSubdomains := []string{
		"*.%s",
		"www.%s",
		"mail.%s",
		"webmail.%s",
		"secure.%s",
		"ssl.%s",
		"api.%s",
		"app.%s",
	}

	for _, pattern := range ctSubdomains {
		if strings.Contains(pattern, "*") {
			// 通配符证书，尝试常见前缀
			prefixes := []string{"www", "mail", "api", "app", "secure", "admin"}
			for _, prefix := range prefixes {
				fullDomain := fmt.Sprintf("%s.%s", prefix, domain)
				if ips := s.resolveDomainWithContext(context.Background(), fullDomain); len(ips) > 0 {
					fmt.Printf("[performCertificateTransparency] 发现子域名: %s -> %s\n", fullDomain, ips[0])

					result := &types.ScanResult{
						TaskID:   fmt.Sprintf("ct-%d", time.Now().UnixNano()),
						TargetID: fullDomain,
						Status:   "success",
						Progress: 100,
						Metadata: map[string]interface{}{
							"domain":    fullDomain,
							"subdomain": prefix,
							"parent":    domain,
							"type":      "subdomain",
							"method":    "certificate_transparency",
							"ip":        ips[0],
							"all_ips":   ips,
						},
					}
					results = append(results, result)
				}
			}
		} else {
			fullDomain := fmt.Sprintf(pattern, domain)
			if ips := s.resolveDomainWithContext(context.Background(), fullDomain); len(ips) > 0 {
				fmt.Printf("[performCertificateTransparency] 发现子域名: %s -> %s\n", fullDomain, ips[0])

				subdomain := strings.TrimSuffix(fullDomain, "."+domain)
				result := &types.ScanResult{
					TaskID:   fmt.Sprintf("ct-%d", time.Now().UnixNano()),
					TargetID: fullDomain,
					Status:   "success",
					Progress: 100,
					Metadata: map[string]interface{}{
						"domain":    fullDomain,
						"subdomain": subdomain,
						"parent":    domain,
						"type":      "subdomain",
						"method":    "certificate_transparency",
						"ip":        ips[0],
						"all_ips":   ips,
					},
				}
				results = append(results, result)
			}
		}
	}

	fmt.Printf("[performCertificateTransparency] 证书透明度查询完成，发现 %d 个子域名\n", len(results))
	return results
}

// performDNSZoneTransfer 尝试DNS区域传输
func (s *Service) performDNSZoneTransfer(domain string) []*types.ScanResult {
	fmt.Printf("[performDNSZoneTransfer] 尝试DNS区域传输，域名: %s\n", domain)

	var results []*types.ScanResult

	// 获取域名的NS记录
	nsRecords, err := net.LookupNS(domain)
	if err != nil {
		fmt.Printf("[performDNSZoneTransfer] 无法获取NS记录: %v\n", err)
		return results
	}

	// 尝试对每个NS服务器进行区域传输
	for _, ns := range nsRecords {
		fmt.Printf("[performDNSZoneTransfer] 尝试从NS服务器进行区域传输: %s\n", ns.Host)

		// 注意：实际的DNS区域传输需要使用专门的DNS库
		// 这里只是模拟，实际实现需要使用miekg/dns等库
		// 大多数现代DNS服务器都禁用了区域传输

		// 模拟区域传输失败（这是正常的安全配置）
		fmt.Printf("[performDNSZoneTransfer] NS服务器 %s 拒绝区域传输（正常安全配置）\n", ns.Host)
	}

	fmt.Printf("[performDNSZoneTransfer] DNS区域传输完成，发现 %d 个子域名\n", len(results))
	return results
}

// performSearchEngineQuery 通过搜索引擎查询子域名
func (s *Service) performSearchEngineQuery(domain string) []*types.ScanResult {
	fmt.Printf("[performSearchEngineQuery] 执行搜索引擎查询，域名: %s\n", domain)

	var results []*types.ScanResult

	// 模拟搜索引擎查询结果
	// 实际实现应该调用Google、Bing等搜索引擎API
	// 搜索查询如: site:example.com -www

	searchSubdomains := []string{
		"blog", "forum", "support", "help", "docs", "wiki",
		"shop", "store", "news", "mobile", "m", "beta",
		"dev", "test", "staging", "demo", "portal",
	}

	for _, subdomain := range searchSubdomains {
		fullDomain := fmt.Sprintf("%s.%s", subdomain, domain)

		// 模拟搜索引擎发现的概率（30%）
		if time.Now().UnixNano()%10 < 3 {
			if ips := s.resolveDomainWithContext(context.Background(), fullDomain); len(ips) > 0 {
				fmt.Printf("[performSearchEngineQuery] 发现子域名: %s -> %s\n", fullDomain, ips[0])

				result := &types.ScanResult{
					TaskID:   fmt.Sprintf("search-%d", time.Now().UnixNano()),
					TargetID: fullDomain,
					Status:   "success",
					Progress: 100,
					Metadata: map[string]interface{}{
						"domain":    fullDomain,
						"subdomain": subdomain,
						"parent":    domain,
						"type":      "subdomain",
						"method":    "search_engine",
						"ip":        ips[0],
						"all_ips":   ips,
					},
				}
				results = append(results, result)
			}
		}
	}

	fmt.Printf("[performSearchEngineQuery] 搜索引擎查询完成，发现 %d 个子域名\n", len(results))
	return results
}

// performRecursiveSubdomain 执行递归子域名发现
func (s *Service) performRecursiveSubdomain(domain string, depth int, wordlist string, threads, timeout int) []*types.ScanResult {
	fmt.Printf("[performRecursiveSubdomain] 执行递归子域名发现，域名: %s, 深度: %d\n", domain, depth)

	var results []*types.ScanResult

	if depth <= 0 {
		return results
	}

	// 首先获取当前层级的子域名
	currentResults := s.performBruteForceSubdomain(domain, "fast", threads/2, timeout)
	results = append(results, currentResults...)

	// 对发现的每个子域名进行递归发现
	for _, result := range currentResults {
		if foundDomain, ok := result.Metadata["domain"].(string); ok {
			fmt.Printf("[performRecursiveSubdomain] 对子域名进行递归发现: %s\n", foundDomain)

			// 递归发现下一层
			recursiveResults := s.performRecursiveSubdomain(foundDomain, depth-1, "fast", threads/2, timeout)
			results = append(results, recursiveResults...)
		}
	}

	fmt.Printf("[performRecursiveSubdomain] 递归子域名发现完成，发现 %d 个子域名\n", len(results))
	return results
}

// checkPort 检查端口是否开放
func (s *Service) checkPort(host string, port int) bool {
	timeout := time.Second * 3
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", host, port), timeout)
	if err != nil {
		return false
	}
	defer conn.Close()
	return true
}
