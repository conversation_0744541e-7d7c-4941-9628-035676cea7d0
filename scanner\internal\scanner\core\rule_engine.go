package core

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"path/filepath"
	"regexp"
	"strings"
	"sync"

	"github.com/sirupsen/logrus"
)

// RuleEngine 规则引擎
// 负责加载、管理和执行扫描规则
type RuleEngine struct {
	// 规则存储
	rules       map[string]*ScanRule
	ruleGroups  map[string]*RuleGroup
	mutex       sync.RWMutex
	
	// 配置
	rulesDir    string
	logger      *logrus.Logger
	
	// 状态
	initialized bool
}

// ScanRule 扫描规则
type ScanRule struct {
	// 基础信息
	ID          string            `json:"id"`          // 规则ID
	Name        string            `json:"name"`        // 规则名称
	Description string            `json:"description"` // 规则描述
	Category    string            `json:"category"`    // 规则分类
	Severity    SeverityLevel     `json:"severity"`    // 严重程度
	
	// 规则条件
	Conditions  []*RuleCondition  `json:"conditions"`  // 匹配条件
	
	// 规则动作
	Actions     []*RuleAction     `json:"actions"`     // 执行动作
	
	// 元数据
	Tags        []string          `json:"tags"`        // 标签
	References  []string          `json:"references"`  // 参考链接
	Author      string            `json:"author"`      // 作者
	Version     string            `json:"version"`     // 版本
	Enabled     bool              `json:"enabled"`     // 是否启用
	
	// 配置
	Config      map[string]interface{} `json:"config"` // 规则配置
}

// RuleCondition 规则条件
type RuleCondition struct {
	Type        string            `json:"type"`        // 条件类型
	Field       string            `json:"field"`       // 检查字段
	Operator    string            `json:"operator"`    // 操作符
	Value       interface{}       `json:"value"`       // 期望值
	Regex       string            `json:"regex"`       // 正则表达式
	CaseSensitive bool            `json:"case_sensitive"` // 是否大小写敏感
	Metadata    map[string]string `json:"metadata"`    // 条件元数据
}

// RuleAction 规则动作
type RuleAction struct {
	Type        string            `json:"type"`        // 动作类型
	Target      string            `json:"target"`      // 目标
	Parameters  map[string]interface{} `json:"parameters"` // 参数
	Condition   string            `json:"condition"`   // 执行条件
}

// RuleGroup 规则组
type RuleGroup struct {
	ID          string            `json:"id"`          // 组ID
	Name        string            `json:"name"`        // 组名称
	Description string            `json:"description"` // 组描述
	Rules       []string          `json:"rules"`       // 包含的规则ID
	Enabled     bool              `json:"enabled"`     // 是否启用
	Config      map[string]interface{} `json:"config"` // 组配置
}

// RuleExecutionContext 规则执行上下文
type RuleExecutionContext struct {
	Target      *ScanTarget       `json:"target"`      // 扫描目标
	Request     *ScanRequest      `json:"request"`     // 扫描请求
	Response    interface{}       `json:"response"`    // 响应数据
	Variables   map[string]interface{} `json:"variables"` // 变量
	Results     []*RuleResult     `json:"results"`     // 执行结果
}

// RuleResult 规则执行结果
type RuleResult struct {
	RuleID      string            `json:"rule_id"`     // 规则ID
	Matched     bool              `json:"matched"`     // 是否匹配
	Confidence  float64           `json:"confidence"`  // 置信度
	Evidence    []string          `json:"evidence"`    // 证据
	Variables   map[string]interface{} `json:"variables"` // 提取的变量
	Error       string            `json:"error"`       // 错误信息
}

// NewRuleEngine 创建规则引擎
func NewRuleEngine(rulesDir string) *RuleEngine {
	return &RuleEngine{
		rules:      make(map[string]*ScanRule),
		ruleGroups: make(map[string]*RuleGroup),
		rulesDir:   rulesDir,
		logger:     logrus.New(),
	}
}

// Initialize 初始化规则引擎
func (re *RuleEngine) Initialize() error {
	re.mutex.Lock()
	defer re.mutex.Unlock()
	
	if re.initialized {
		return nil
	}
	
	re.logger.Info("初始化规则引擎")
	
	// 加载规则文件
	if err := re.loadRules(); err != nil {
		return fmt.Errorf("加载规则失败: %v", err)
	}
	
	// 加载规则组
	if err := re.loadRuleGroups(); err != nil {
		return fmt.Errorf("加载规则组失败: %v", err)
	}
	
	re.initialized = true
	re.logger.Infof("规则引擎初始化完成，加载了 %d 个规则，%d 个规则组", 
		len(re.rules), len(re.ruleGroups))
	
	return nil
}

// loadRules 加载规则文件
func (re *RuleEngine) loadRules() error {
	rulesPattern := filepath.Join(re.rulesDir, "rules", "*.json")
	files, err := filepath.Glob(rulesPattern)
	if err != nil {
		return err
	}
	
	for _, file := range files {
		if err := re.loadRuleFile(file); err != nil {
			re.logger.Errorf("加载规则文件失败 %s: %v", file, err)
			continue
		}
	}
	
	return nil
}

// loadRuleFile 加载单个规则文件
func (re *RuleEngine) loadRuleFile(filename string) error {
	data, err := ioutil.ReadFile(filename)
	if err != nil {
		return err
	}
	
	var rule ScanRule
	if err := json.Unmarshal(data, &rule); err != nil {
		return err
	}
	
	// 验证规则
	if err := re.validateRule(&rule); err != nil {
		return fmt.Errorf("规则验证失败: %v", err)
	}
	
	re.rules[rule.ID] = &rule
	re.logger.Debugf("加载规则: %s - %s", rule.ID, rule.Name)
	
	return nil
}

// loadRuleGroups 加载规则组
func (re *RuleEngine) loadRuleGroups() error {
	groupsPattern := filepath.Join(re.rulesDir, "groups", "*.json")
	files, err := filepath.Glob(groupsPattern)
	if err != nil {
		return err
	}
	
	for _, file := range files {
		if err := re.loadRuleGroupFile(file); err != nil {
			re.logger.Errorf("加载规则组文件失败 %s: %v", file, err)
			continue
		}
	}
	
	return nil
}

// loadRuleGroupFile 加载单个规则组文件
func (re *RuleEngine) loadRuleGroupFile(filename string) error {
	data, err := ioutil.ReadFile(filename)
	if err != nil {
		return err
	}
	
	var group RuleGroup
	if err := json.Unmarshal(data, &group); err != nil {
		return err
	}
	
	re.ruleGroups[group.ID] = &group
	re.logger.Debugf("加载规则组: %s - %s", group.ID, group.Name)
	
	return nil
}

// validateRule 验证规则
func (re *RuleEngine) validateRule(rule *ScanRule) error {
	if rule.ID == "" {
		return fmt.Errorf("规则ID不能为空")
	}
	
	if rule.Name == "" {
		return fmt.Errorf("规则名称不能为空")
	}
	
	if len(rule.Conditions) == 0 {
		return fmt.Errorf("规则必须包含至少一个条件")
	}
	
	// 验证条件
	for i, condition := range rule.Conditions {
		if err := re.validateCondition(condition); err != nil {
			return fmt.Errorf("条件 %d 验证失败: %v", i, err)
		}
	}
	
	return nil
}

// validateCondition 验证条件
func (re *RuleEngine) validateCondition(condition *RuleCondition) error {
	if condition.Type == "" {
		return fmt.Errorf("条件类型不能为空")
	}
	
	if condition.Field == "" {
		return fmt.Errorf("检查字段不能为空")
	}
	
	if condition.Operator == "" {
		return fmt.Errorf("操作符不能为空")
	}
	
	// 验证正则表达式
	if condition.Regex != "" {
		if _, err := regexp.Compile(condition.Regex); err != nil {
			return fmt.Errorf("正则表达式无效: %v", err)
		}
	}
	
	return nil
}

// ExecuteRules 执行规则
func (re *RuleEngine) ExecuteRules(ctx *RuleExecutionContext, ruleIDs []string) ([]*RuleResult, error) {
	re.mutex.RLock()
	defer re.mutex.RUnlock()
	
	var results []*RuleResult
	
	// 如果没有指定规则ID，执行所有启用的规则
	if len(ruleIDs) == 0 {
		for _, rule := range re.rules {
			if rule.Enabled {
				ruleIDs = append(ruleIDs, rule.ID)
			}
		}
	}
	
	// 执行指定的规则
	for _, ruleID := range ruleIDs {
		rule, exists := re.rules[ruleID]
		if !exists {
			re.logger.Warnf("规则不存在: %s", ruleID)
			continue
		}
		
		if !rule.Enabled {
			re.logger.Debugf("规则已禁用: %s", ruleID)
			continue
		}
		
		result := re.executeRule(rule, ctx)
		results = append(results, result)
	}
	
	return results, nil
}

// executeRule 执行单个规则
func (re *RuleEngine) executeRule(rule *ScanRule, ctx *RuleExecutionContext) *RuleResult {
	result := &RuleResult{
		RuleID:     rule.ID,
		Matched:    false,
		Confidence: 0.0,
		Evidence:   make([]string, 0),
		Variables:  make(map[string]interface{}),
	}
	
	// 执行所有条件
	allMatched := true
	for _, condition := range rule.Conditions {
		matched, evidence, variables := re.executeCondition(condition, ctx)
		
		if matched {
			result.Evidence = append(result.Evidence, evidence...)
			for k, v := range variables {
				result.Variables[k] = v
			}
		} else {
			allMatched = false
			break
		}
	}
	
	result.Matched = allMatched
	if allMatched {
		result.Confidence = 1.0 // 简化实现，实际可以根据条件计算
		
		// 执行动作
		re.executeActions(rule.Actions, ctx, result)
	}
	
	return result
}

// executeCondition 执行条件
func (re *RuleEngine) executeCondition(condition *RuleCondition, ctx *RuleExecutionContext) (bool, []string, map[string]interface{}) {
	evidence := make([]string, 0)
	variables := make(map[string]interface{})
	
	// 获取字段值
	fieldValue := re.getFieldValue(condition.Field, ctx)
	if fieldValue == nil {
		return false, evidence, variables
	}
	
	// 执行匹配
	matched := re.matchCondition(condition, fieldValue)
	
	if matched {
		evidence = append(evidence, fmt.Sprintf("字段 %s 匹配条件", condition.Field))
		variables[condition.Field] = fieldValue
	}
	
	return matched, evidence, variables
}

// getFieldValue 获取字段值
func (re *RuleEngine) getFieldValue(field string, ctx *RuleExecutionContext) interface{} {
	// 简化实现，实际需要根据字段路径解析
	switch field {
	case "target.url":
		return ctx.Target.Value
	case "target.type":
		return ctx.Target.Type
	case "response.status_code":
		if resp, ok := ctx.Response.(map[string]interface{}); ok {
			return resp["status_code"]
		}
	case "response.body":
		if resp, ok := ctx.Response.(map[string]interface{}); ok {
			return resp["body"]
		}
	case "response.headers":
		if resp, ok := ctx.Response.(map[string]interface{}); ok {
			return resp["headers"]
		}
	}
	
	// 检查变量
	if value, exists := ctx.Variables[field]; exists {
		return value
	}
	
	return nil
}

// matchCondition 匹配条件
func (re *RuleEngine) matchCondition(condition *RuleCondition, fieldValue interface{}) bool {
	switch condition.Operator {
	case "equals":
		return re.equals(fieldValue, condition.Value, condition.CaseSensitive)
	case "contains":
		return re.contains(fieldValue, condition.Value, condition.CaseSensitive)
	case "regex":
		return re.regexMatch(fieldValue, condition.Regex)
	case "greater_than":
		return re.greaterThan(fieldValue, condition.Value)
	case "less_than":
		return re.lessThan(fieldValue, condition.Value)
	default:
		re.logger.Warnf("未知的操作符: %s", condition.Operator)
		return false
	}
}

// equals 相等比较
func (re *RuleEngine) equals(fieldValue, expectedValue interface{}, caseSensitive bool) bool {
	fieldStr := fmt.Sprintf("%v", fieldValue)
	expectedStr := fmt.Sprintf("%v", expectedValue)
	
	if !caseSensitive {
		fieldStr = strings.ToLower(fieldStr)
		expectedStr = strings.ToLower(expectedStr)
	}
	
	return fieldStr == expectedStr
}

// contains 包含检查
func (re *RuleEngine) contains(fieldValue, expectedValue interface{}, caseSensitive bool) bool {
	fieldStr := fmt.Sprintf("%v", fieldValue)
	expectedStr := fmt.Sprintf("%v", expectedValue)
	
	if !caseSensitive {
		fieldStr = strings.ToLower(fieldStr)
		expectedStr = strings.ToLower(expectedStr)
	}
	
	return strings.Contains(fieldStr, expectedStr)
}

// regexMatch 正则匹配
func (re *RuleEngine) regexMatch(fieldValue interface{}, pattern string) bool {
	fieldStr := fmt.Sprintf("%v", fieldValue)
	
	matched, err := regexp.MatchString(pattern, fieldStr)
	if err != nil {
		re.logger.Errorf("正则匹配错误: %v", err)
		return false
	}
	
	return matched
}

// greaterThan 大于比较
func (re *RuleEngine) greaterThan(fieldValue, expectedValue interface{}) bool {
	// 简化实现，实际需要类型转换
	return false
}

// lessThan 小于比较
func (re *RuleEngine) lessThan(fieldValue, expectedValue interface{}) bool {
	// 简化实现，实际需要类型转换
	return false
}

// executeActions 执行动作
func (re *RuleEngine) executeActions(actions []*RuleAction, ctx *RuleExecutionContext, result *RuleResult) {
	for _, action := range actions {
		re.executeAction(action, ctx, result)
	}
}

// executeAction 执行单个动作
func (re *RuleEngine) executeAction(action *RuleAction, ctx *RuleExecutionContext, result *RuleResult) {
	switch action.Type {
	case "create_vulnerability":
		re.createVulnerability(action, ctx, result)
	case "set_variable":
		re.setVariable(action, ctx, result)
	case "log":
		re.logAction(action, ctx, result)
	default:
		re.logger.Warnf("未知的动作类型: %s", action.Type)
	}
}

// createVulnerability 创建漏洞
func (re *RuleEngine) createVulnerability(action *RuleAction, ctx *RuleExecutionContext, result *RuleResult) {
	// 实现创建漏洞的逻辑
	re.logger.Debugf("创建漏洞: %s", action.Target)
}

// setVariable 设置变量
func (re *RuleEngine) setVariable(action *RuleAction, ctx *RuleExecutionContext, result *RuleResult) {
	if varName, ok := action.Parameters["name"].(string); ok {
		if varValue, ok := action.Parameters["value"]; ok {
			ctx.Variables[varName] = varValue
		}
	}
}

// logAction 日志动作
func (re *RuleEngine) logAction(action *RuleAction, ctx *RuleExecutionContext, result *RuleResult) {
	if message, ok := action.Parameters["message"].(string); ok {
		re.logger.Info(message)
	}
}

// GetRule 获取规则
func (re *RuleEngine) GetRule(ruleID string) (*ScanRule, error) {
	re.mutex.RLock()
	defer re.mutex.RUnlock()
	
	rule, exists := re.rules[ruleID]
	if !exists {
		return nil, fmt.Errorf("规则不存在: %s", ruleID)
	}
	
	return rule, nil
}

// ListRules 列出所有规则
func (re *RuleEngine) ListRules() []*ScanRule {
	re.mutex.RLock()
	defer re.mutex.RUnlock()
	
	rules := make([]*ScanRule, 0, len(re.rules))
	for _, rule := range re.rules {
		rules = append(rules, rule)
	}
	
	return rules
}

// GetRuleGroup 获取规则组
func (re *RuleEngine) GetRuleGroup(groupID string) (*RuleGroup, error) {
	re.mutex.RLock()
	defer re.mutex.RUnlock()
	
	group, exists := re.ruleGroups[groupID]
	if !exists {
		return nil, fmt.Errorf("规则组不存在: %s", groupID)
	}
	
	return group, nil
}

// ListRuleGroups 列出所有规则组
func (re *RuleEngine) ListRuleGroups() []*RuleGroup {
	re.mutex.RLock()
	defer re.mutex.RUnlock()
	
	groups := make([]*RuleGroup, 0, len(re.ruleGroups))
	for _, group := range re.ruleGroups {
		groups = append(groups, group)
	}
	
	return groups
}
