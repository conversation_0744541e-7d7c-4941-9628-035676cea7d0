# 清理旧的引擎文件，只保留新架构的文件

$keepFiles = @(
    "information_gathering_engine.go",
    "web_vulnerability_engine.go"
)

$engineDir = "internal\scanner\engines"

# 获取所有.go文件
$allFiles = Get-ChildItem -Path $engineDir -Filter "*.go" -Recurse

foreach ($file in $allFiles) {
    $fileName = $file.Name
    
    # 如果不在保留列表中，则删除
    if ($fileName -notin $keepFiles) {
        Write-Host "删除文件: $($file.FullName)"
        Remove-Item $file.FullName -Force
    } else {
        Write-Host "保留文件: $($file.FullName)"
    }
}

# 删除data目录
$dataDir = Join-Path $engineDir "data"
if (Test-Path $dataDir) {
    Write-Host "删除目录: $dataDir"
    Remove-Item $dataDir -Recurse -Force
}

Write-Host "清理完成!"
