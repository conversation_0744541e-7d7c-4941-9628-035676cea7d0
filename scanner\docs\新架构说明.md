# 漏洞扫描引擎重构架构说明

## 🎯 重构目标

本次重构的主要目标是解决原有扫描引擎架构中存在的问题：

### 原有问题
- **架构混乱**：多个引擎文件重复功能，接口不统一
- **代码冗余**：大量重复的检测逻辑和类型定义
- **模块耦合**：组件之间依赖关系复杂，难以维护
- **扩展困难**：添加新的检测功能需要修改多个文件

### 重构成果
- **清晰架构**：模块化设计，职责分离
- **统一接口**：所有引擎实现相同的核心接口
- **易于扩展**：新增引擎只需实现核心接口
- **代码复用**：基础功能通过BaseEngine提供

## 🏗️ 新架构设计

### 核心设计原则

1. **单一职责**：每个引擎只负责特定类型的扫描
2. **接口统一**：所有引擎实现相同的核心接口
3. **模块解耦**：引擎之间通过标准接口通信
4. **易于扩展**：新增引擎只需实现核心接口

### 架构层次

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Web API       │  │   CLI Tool      │  │  Test Suite  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   管理层 (Management Layer)                  │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              EngineManager (引擎管理器)                  │ │
│  │  • 引擎注册与发现    • 任务调度与分发                    │ │
│  │  • 状态监控         • 资源管理                          │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   引擎层 (Engine Layer)                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Information     │  │ Web Vulnerability│  │ Network      │ │
│  │ Gathering       │  │ Engine          │  │ Engine       │ │
│  │ Engine          │  └─────────────────┘  └──────────────┘ │
│  └─────────────────┘  ┌─────────────────┐  ┌──────────────┐ │
│  ┌─────────────────┐  │ CVE Scanning    │  │ Custom       │ │
│  │ BaseEngine      │  │ Engine          │  │ Engine       │ │
│  │ (基础引擎)       │  └─────────────────┘  └──────────────┘ │
│  └─────────────────┘                                        │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   核心层 (Core Layer)                        │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                  Core Interfaces                        │ │
│  │  • ScanEngine     • ScanTarget     • ScanResult        │ │
│  │  • ScanRequest    • ScanConfig     • Vulnerability     │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                   Core Types                            │ │
│  │  • TargetInfo     • TechStack      • SecurityInfo      │ │
│  │  • ServiceInfo    • NetworkInfo    • ContentInfo       │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 📁 目录结构

```
scanner/
├── internal/scanner/
│   ├── core/                    # 核心层
│   │   ├── interfaces.go        # 核心接口定义
│   │   ├── types.go            # 核心类型定义
│   │   ├── base_engine.go      # 基础引擎实现
│   │   └── engine_manager.go   # 引擎管理器
│   └── engines/                # 引擎层
│       ├── information_gathering_engine.go  # 信息收集引擎
│       └── web_vulnerability_engine.go      # Web漏洞扫描引擎
├── cmd/
│   ├── main.go                 # 主程序入口
│   └── test_scanner.go         # 架构测试程序
└── docs/
    └── 新架构说明.md           # 本文档
```

## 🔧 核心组件

### 1. ScanEngine 接口

所有扫描引擎必须实现的核心接口：

```go
type ScanEngine interface {
    // 基础信息
    GetName() string
    GetType() EngineType
    GetVersion() string
    GetDescription() string
    
    // 能力检查
    IsEnabled() bool
    CanScan(target *ScanTarget) bool
    GetSupportedTargets() []TargetType
    
    // 配置管理
    ValidateConfig(config *ScanConfig) error
    GetDefaultConfig() *ScanConfig
    
    // 扫描执行
    Scan(ctx context.Context, request *ScanRequest) (*ScanResult, error)
    Stop(ctx context.Context, taskID string) error
    
    // 状态管理
    GetStatus(taskID string) (*ScanStatus, error)
    GetProgress(taskID string) (*ScanProgress, error)
    
    // 生命周期
    Initialize() error
    Cleanup() error
}
```

### 2. BaseEngine 基础引擎

提供所有扫描引擎的通用功能：

- **状态管理**：引擎启用/禁用状态
- **任务管理**：活跃任务跟踪和上下文管理
- **配置管理**：默认配置和验证
- **日志记录**：统一的日志接口
- **统计信息**：引擎性能统计

### 3. EngineManager 引擎管理器

负责管理所有扫描引擎：

- **引擎注册**：动态注册和注销引擎
- **任务调度**：智能选择合适的引擎
- **并发控制**：管理并发任务数量
- **状态监控**：实时监控引擎和任务状态
- **资源管理**：自动清理和资源回收

## 🚀 已实现的引擎

### 1. 信息收集引擎 (InformationGatheringEngine)

**功能特性：**
- 目标基础信息收集（URL、IP、域名解析）
- 技术栈识别（Web服务器、编程语言、框架）
- 服务发现（端口扫描、服务识别）
- 安全信息收集（安全头、认证方式）
- 内容分析（页面结构、链接提取）

**支持的目标类型：**
- URL目标
- IP地址目标
- 域名目标
- 主机目标

### 2. Web漏洞扫描引擎 (WebVulnerabilityEngine)

**功能特性：**
- 网站爬取（自动发现页面和表单）
- SQL注入检测（错误注入、时间盲注）
- XSS检测（反射型、存储型、DOM型）
- SSRF检测（内网访问、云元数据）
- 文件包含检测（LFI、RFI）
- 命令注入检测（系统命令执行）

**支持的目标类型：**
- URL目标

## 📊 测试结果

运行 `go run cmd/test_scanner.go` 的测试结果：

```
🚀 漏洞扫描引擎架构测试
==================================================
📋 注册扫描引擎...
✅ 信息收集引擎注册成功
✅ Web漏洞扫描引擎注册成功

🔧 初始化引擎管理器...
✅ 引擎管理器初始化成功

📊 已注册的扫描引擎:
1. 信息收集引擎 (information_gathering) - 负责目标信息收集、指纹识别、服务发现等功能
   版本: 1.0.0, 状态: running, 启用: true
2. Web漏洞扫描引擎 (web_vulnerability) - 负责检测SQL注入、XSS、SSRF、文件包含等Web应用漏洞
   版本: 1.0.0, 状态: running, 启用: true

🎯 创建扫描目标...
✅ 目标创建成功: http://httpbin.org (url)

⚙️ 创建扫描配置...
✅ 配置创建成功: 模式=standard, 超时=5m0s

🔍 测试信息收集扫描...
✅ 信息收集任务已提交: info_scan_1753767197
📊 监控信息收集任务进度: info_scan_1753767197
   状态: completed, 进度: 100.0%
✅ 信息收集任务完成!

📋 信息收集结果摘要:
   任务ID: test_task_1
   引擎类型: information_gathering
   扫描目标: http://httpbin.org
   开始时间: 2025-07-29 13:33:17
   完成时间: 2025-07-29 13:33:18
   扫描耗时: 1.1801161s
   发现漏洞: 0个

📈 管理器统计信息:
总引擎数: 2, 启用引擎数: 2, 运行引擎数: 2
总任务数: 2, 完成任务数: 1, 失败任务数: 0, 活跃任务数: 1

🔒 关闭引擎管理器...
✅ 引擎管理器已关闭

🎉 测试完成!
```

## 🔮 扩展计划

### 待实现的引擎

1. **网络漏洞扫描引擎** - 端口扫描、服务漏洞检测
2. **CVE漏洞扫描引擎** - 基于CVE数据库的历史漏洞检测
3. **合规检查引擎** - OWASP、PCI、GDPR等合规性检查
4. **API安全扫描引擎** - REST API、GraphQL安全检测
5. **移动应用扫描引擎** - Android、iOS应用安全检测

### 功能增强

1. **智能调度** - 基于目标特征自动选择最佳引擎组合
2. **结果关联** - 跨引擎的漏洞关联和去重
3. **插件系统** - 支持第三方插件扩展
4. **分布式扫描** - 支持多节点分布式扫描
5. **机器学习** - 基于ML的误报过滤和风险评估

## 🎉 总结

新的漏洞扫描引擎架构成功解决了原有系统的问题：

### ✅ 已解决的问题
- **架构清晰**：模块化设计，职责明确
- **接口统一**：所有引擎遵循相同接口规范
- **易于维护**：代码结构清晰，便于调试和修改
- **扩展性强**：新增引擎只需实现核心接口

### 🚀 技术优势
- **高内聚低耦合**：模块间依赖最小化
- **可测试性**：每个组件都可以独立测试
- **可观测性**：完整的日志和监控体系
- **容错性**：单个引擎故障不影响整体系统

### 📈 性能提升
- **并发处理**：支持多引擎并行扫描
- **资源管理**：自动任务清理和资源回收
- **状态监控**：实时任务进度和状态跟踪
- **智能调度**：根据目标类型自动选择引擎

这个新架构为漏洞扫描器的后续开发奠定了坚实的基础，支持快速添加新的扫描能力和功能扩展。
