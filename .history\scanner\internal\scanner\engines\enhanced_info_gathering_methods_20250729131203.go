package engines

import (
	"context"
	"net/http"
	"regexp"
	"strings"

	"scanner/internal/scanner/types"
)

// identifyEnhancedWebComponents 增强的Web组件识别 - 技术栈、框架、CMS、JavaScript库
func (e *InformationGatheringEngine) identifyEnhancedWebComponents(ctx context.Context, target string) (*types.WebComponentInfo, error) {
	components := &types.WebComponentInfo{
		Libraries:  make([]*types.ComponentVersion, 0),
		Middleware: make([]*types.ComponentVersion, 0),
	}

	// 发送HTTP请求
	req, err := http.NewRequestWithContext(ctx, "GET", target, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set("User-Agent", e.userAgent)

	resp, err := e.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 分析HTTP响应头
	e.analyzeHTTPHeaders(resp.Header, components)

	// 读取响应体
	body := make([]byte, 10240) // 读取前10KB
	n, _ := resp.Body.Read(body)
	bodyStr := string(body[:n])

	// 分析页面内容
	e.analyzePageContent(bodyStr, components)

	// 分析JavaScript库
	e.analyzeJavaScriptLibraries(bodyStr, components)

	// 分析CSS框架
	e.analyzeCSSFrameworks(bodyStr, components)

	// 分析Meta标签
	e.analyzeMetaTags(bodyStr, components)

	// CMS识别
	e.identifyCMS(bodyStr, resp.Header, components)

	// 框架识别
	e.identifyFrameworks(bodyStr, resp.Header, components)

	return components, nil
}

// analyzeHTTPHeaders 分析HTTP响应头
func (e *InformationGatheringEngine) analyzeHTTPHeaders(headers http.Header, components *types.WebComponentInfo) {
	// Server头分析
	if server := headers.Get("Server"); server != "" {
		if strings.Contains(strings.ToLower(server), "apache") {
			components.WebServer = &types.ComponentVersion{
				Name:    "Apache",
				Version: e.extractVersionFromString(server, "Apache"),
				Type:    "web_server",
			}
		} else if strings.Contains(strings.ToLower(server), "nginx") {
			components.WebServer = &types.ComponentVersion{
				Name:    "Nginx",
				Version: e.extractVersionFromString(server, "nginx"),
				Type:    "web_server",
			}
		} else if strings.Contains(strings.ToLower(server), "iis") {
			components.WebServer = &types.ComponentVersion{
				Name:    "IIS",
				Version: e.extractVersionFromString(server, "IIS"),
				Type:    "web_server",
			}
		}
	}

	// X-Powered-By头分析
	if poweredBy := headers.Get("X-Powered-By"); poweredBy != "" {
		if strings.Contains(strings.ToLower(poweredBy), "php") {
			components.Language = &types.ComponentVersion{
				Name:    "PHP",
				Version: e.extractVersionFromString(poweredBy, "PHP"),
				Type:    "language",
			}
		} else if strings.Contains(strings.ToLower(poweredBy), "asp.net") {
			components.Language = &types.ComponentVersion{
				Name:    "ASP.NET",
				Version: e.extractVersionFromString(poweredBy, "ASP.NET"),
				Type:    "language",
			}
		}
	}

	// 其他技术头
	if generator := headers.Get("X-Generator"); generator != "" {
		components.Libraries = append(components.Libraries, &types.ComponentVersion{
			Name:    "Generator",
			Version: generator,
			Type:    "generator",
		})
	}
}

// analyzePageContent 分析页面内容
func (e *InformationGatheringEngine) analyzePageContent(content string, components *types.WebComponentInfo) {
	// WordPress检测
	wpPatterns := []string{
		`wp-content`,
		`wp-includes`,
		`wordpress`,
		`/wp-json/`,
		`wp-admin`,
	}
	for _, pattern := range wpPatterns {
		if strings.Contains(strings.ToLower(content), pattern) {
			components.CMS = "WordPress"
			// 尝试提取版本
			if version := e.extractWordPressVersion(content); version != "" {
				components.CMSVersion = version
			}
			break
		}
	}

	// Drupal检测
	drupalPatterns := []string{
		`drupal`,
		`sites/default`,
		`/modules/`,
		`Drupal.settings`,
	}
	for _, pattern := range drupalPatterns {
		if strings.Contains(strings.ToLower(content), pattern) {
			components.CMS = "Drupal"
			break
		}
	}

	// Joomla检测
	joomlaPatterns := []string{
		`joomla`,
		`option=com_`,
		`/components/com_`,
		`Joomla!`,
	}
	for _, pattern := range joomlaPatterns {
		if strings.Contains(strings.ToLower(content), pattern) {
			components.CMS = "Joomla"
			break
		}
	}
}

// analyzeJavaScriptLibraries 分析JavaScript库
func (e *InformationGatheringEngine) analyzeJavaScriptLibraries(content string, components *types.WebComponentInfo) {
	jsLibraries := map[string][]string{
		"jQuery": {
			`jquery[.-]([0-9]+\.[0-9]+\.[0-9]+)`,
			`jQuery v([0-9]+\.[0-9]+\.[0-9]+)`,
		},
		"React": {
			`react[.-]([0-9]+\.[0-9]+\.[0-9]+)`,
			`React v([0-9]+\.[0-9]+\.[0-9]+)`,
		},
		"Vue.js": {
			`vue[.-]([0-9]+\.[0-9]+\.[0-9]+)`,
			`Vue v([0-9]+\.[0-9]+\.[0-9]+)`,
		},
		"Angular": {
			`angular[.-]([0-9]+\.[0-9]+\.[0-9]+)`,
			`Angular v([0-9]+\.[0-9]+\.[0-9]+)`,
		},
		"Bootstrap": {
			`bootstrap[.-]([0-9]+\.[0-9]+\.[0-9]+)`,
			`Bootstrap v([0-9]+\.[0-9]+\.[0-9]+)`,
		},
	}

	for libName, patterns := range jsLibraries {
		for _, pattern := range patterns {
			re := regexp.MustCompile(pattern)
			if matches := re.FindStringSubmatch(content); len(matches) > 1 {
				components.Libraries = append(components.Libraries, &types.ComponentVersion{
					Name:    libName,
					Version: matches[1],
					Type:    "javascript",
				})
				break
			}
		}
	}
}

// analyzeCSSFrameworks 分析CSS框架
func (e *InformationGatheringEngine) analyzeCSSFrameworks(content string, components *types.WebComponentInfo) {
	cssFrameworks := []string{
		"bootstrap",
		"foundation",
		"bulma",
		"tailwind",
		"materialize",
		"semantic-ui",
	}

	for _, framework := range cssFrameworks {
		if strings.Contains(strings.ToLower(content), framework) {
			components.Libraries = append(components.Libraries, &types.ComponentVersion{
				Name: strings.Title(framework),
				Type: "css",
			})
		}
	}
}

// analyzeMetaTags 分析Meta标签
func (e *InformationGatheringEngine) analyzeMetaTags(content string, components *types.WebComponentInfo) {
	// Generator meta标签
	generatorPattern := `<meta\s+name=["']generator["']\s+content=["']([^"']+)["']`
	re := regexp.MustCompile(generatorPattern)
	if matches := re.FindStringSubmatch(content); len(matches) > 1 {
		generator := matches[1]
		components.Libraries = append(components.Libraries, &types.ComponentVersion{
			Name:    "Generator",
			Version: generator,
			Type:    "generator",
		})
	}
}

// identifyCMS 识别CMS系统
func (e *InformationGatheringEngine) identifyCMS(content string, headers http.Header, components *types.WebComponentInfo) {
	// 如果已经识别了CMS，跳过
	if components.CMS != "" {
		return
	}

	// 通过特定文件路径识别
	cmsPatterns := map[string][]string{
		"WordPress": {
			"/wp-content/",
			"/wp-includes/",
			"/wp-admin/",
			"wp-json",
		},
		"Drupal": {
			"/sites/default/",
			"/modules/",
			"/themes/",
			"Drupal.settings",
		},
		"Joomla": {
			"/components/com_",
			"/modules/mod_",
			"/templates/",
			"option=com_",
		},
		"Magento": {
			"/skin/frontend/",
			"/js/mage/",
			"Mage.Cookies",
		},
		"PrestaShop": {
			"/modules/",
			"/themes/",
			"prestashop",
		},
	}

	for cms, patterns := range cmsPatterns {
		found := false
		for _, pattern := range patterns {
			if strings.Contains(strings.ToLower(content), strings.ToLower(pattern)) {
				components.CMS = cms
				found = true
				break
			}
		}
		if found {
			break
		}
	}
}

// identifyFrameworks 识别Web框架
func (e *InformationGatheringEngine) identifyFrameworks(content string, headers http.Header, components *types.WebComponentInfo) {
	// 通过Cookie识别框架
	if setCookie := headers.Get("Set-Cookie"); setCookie != "" {
		if strings.Contains(setCookie, "JSESSIONID") {
			components.Framework = "Java Servlet"
		} else if strings.Contains(setCookie, "PHPSESSID") {
			components.Framework = "PHP Session"
		} else if strings.Contains(setCookie, "ASP.NET_SessionId") {
			components.Framework = "ASP.NET"
		} else if strings.Contains(setCookie, "django") {
			components.Framework = "Django"
		} else if strings.Contains(setCookie, "laravel") {
			components.Framework = "Laravel"
		}
	}

	// 通过页面内容识别框架
	frameworkPatterns := map[string][]string{
		"Laravel": {
			"laravel_session",
			"laravel_token",
			"Laravel",
		},
		"Django": {
			"csrfmiddlewaretoken",
			"django",
			"Django",
		},
		"Spring": {
			"jsessionid",
			"spring",
			"Spring",
		},
		"Rails": {
			"rails",
			"Ruby on Rails",
			"csrf-token",
		},
		"Express": {
			"express",
			"Express",
		},
		"Flask": {
			"flask",
			"Flask",
		},
	}

	for framework, patterns := range frameworkPatterns {
		for _, pattern := range patterns {
			if strings.Contains(strings.ToLower(content), strings.ToLower(pattern)) {
				if components.Framework == "" {
					components.Framework = framework
				}
				break
			}
		}
	}
}

// extractVersionFromString 从字符串中提取版本信息
func (e *InformationGatheringEngine) extractVersionFromString(text, software string) string {
	patterns := []string{
		software + `/([0-9]+\.[0-9]+\.[0-9]+)`,
		software + `/([0-9]+\.[0-9]+)`,
		software + `\s+([0-9]+\.[0-9]+\.[0-9]+)`,
		software + `\s+([0-9]+\.[0-9]+)`,
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(`(?i)` + pattern)
		if matches := re.FindStringSubmatch(text); len(matches) > 1 {
			return matches[1]
		}
	}
	return ""
}

// extractWordPressVersion 提取WordPress版本
func (e *InformationGatheringEngine) extractWordPressVersion(content string) string {
	patterns := []string{
		`wp-includes/js/wp-embed\.min\.js\?ver=([0-9]+\.[0-9]+\.[0-9]+)`,
		`wp-includes/css/dist/block-library/style\.min\.css\?ver=([0-9]+\.[0-9]+\.[0-9]+)`,
		`<meta name="generator" content="WordPress ([0-9]+\.[0-9]+\.[0-9]+)"`,
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		if matches := re.FindStringSubmatch(content); len(matches) > 1 {
			return matches[1]
		}
	}
	return ""
}
