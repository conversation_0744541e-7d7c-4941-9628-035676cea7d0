package scanner

import (
	"log"
	"scanner/internal/scanner/engines"
	"scanner/internal/services"
	"scanner/internal/services/vulnerability"
)

// InitializeEngines 初始化所有扫描引擎
func InitializeEngines(logService *services.ScanLogService, systemConfigService *services.SystemConfigService, vulnerabilityService *vulnerability.Service) (*EngineManager, *TaskScheduler, error) {
	// 创建引擎管理器
	engineManager := NewEngineManager(logService, systemConfigService, vulnerabilityService)

	// 注册网络扫描引擎
	networkEngine := engines.NewNetworkVulnerabilityEngine()
	if err := engineManager.RegisterEngine(networkEngine); err != nil {
		return nil, nil, err
	}
	log.Printf("已注册网络漏洞扫描引擎: %s", networkEngine.GetName())

	// 注册Web漏洞扫描引擎
	webVulnEngine := engines.NewWebVulnerabilityEngine()
	if err := engineManager.RegisterEngine(webVulnEngine); err != nil {
		return nil, nil, err
	}
	log.Printf("已注册Web漏洞扫描引擎: %s", webVulnEngine.GetName())

	// 注册CVE漏洞扫描引擎
	cveEngine := engines.NewCVEVulnerabilityEngine()
	if err := engineManager.RegisterEngine(cveEngine); err != nil {
		return nil, nil, err
	}
	log.Printf("已注册CVE漏洞扫描引擎: %s", cveEngine.GetName())

	// 注册信息收集引擎
	infoEngine := engines.NewInformationGatheringEngine()
	if err := engineManager.RegisterEngine(infoEngine); err != nil {
		return nil, nil, err
	}
	log.Printf("已注册信息收集引擎: %s", infoEngine.GetName())

	// 创建任务调度器
	scheduler := NewTaskScheduler(engineManager)
	log.Println("任务调度器已初始化")

	log.Printf("扫描引擎系统初始化完成，共注册 %d 个引擎", len(engineManager.GetEngines()))

	return engineManager, scheduler, nil
}

// ShutdownEngines 关闭扫描引擎系统
func ShutdownEngines(engineManager *EngineManager, scheduler *TaskScheduler) error {
	log.Println("正在关闭扫描引擎系统...")

	// 关闭任务调度器
	if scheduler != nil {
		if err := scheduler.Shutdown(); err != nil {
			log.Printf("关闭任务调度器失败: %v", err)
		}
	}

	// 关闭引擎管理器
	if engineManager != nil {
		if err := engineManager.Shutdown(); err != nil {
			log.Printf("关闭引擎管理器失败: %v", err)
		}
	}

	log.Println("扫描引擎系统已关闭")
	return nil
}
