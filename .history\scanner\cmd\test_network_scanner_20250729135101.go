package main

import (
	"fmt"
	"log"
	"strings"
	"time"

	"scanner/internal/scanner/core"
	"scanner/internal/scanner/engines"
)

// 测试网络漏洞扫描引擎
func main() {
	fmt.Println("🌐 网络漏洞扫描引擎测试")
	fmt.Println(strings.Repeat("=", 50))
	
	// 创建引擎管理器
	manager := core.NewEngineManager()
	
	// 注册网络漏洞扫描引擎
	fmt.Println("📋 注册网络漏洞扫描引擎...")
	networkEngine := engines.NewNetworkVulnerabilityEngine()
	if err := manager.RegisterEngine(networkEngine); err != nil {
		log.Fatalf("注册网络漏洞扫描引擎失败: %v", err)
	}
	fmt.Println("✅ 网络漏洞扫描引擎注册成功")
	
	// 初始化管理器
	fmt.Println("\n🔧 初始化引擎管理器...")
	if err := manager.Initialize(); err != nil {
		log.Fatalf("引擎管理器初始化失败: %v", err)
	}
	fmt.Println("✅ 引擎管理器初始化成功")
	
	// 显示引擎信息
	fmt.Println("\n📊 已注册的扫描引擎:")
	engines := manager.ListEngines()
	for i, engine := range engines {
		fmt.Printf("%d. %s (%s) - %s\n", i+1, engine.GetName(), engine.GetType(), engine.GetDescription())
		fmt.Printf("   版本: %s, 状态: %s, 启用: %t\n", engine.GetVersion(), "running", engine.IsEnabled())
		fmt.Printf("   支持的目标类型: %v\n", engine.GetSupportedTargets())
		fmt.Println()
	}
	
	// 测试不同类型的网络扫描
	testNetworkScanning(manager)
	
	// 关闭管理器
	fmt.Println("\n🔒 关闭引擎管理器...")
	if err := manager.Shutdown(); err != nil {
		log.Printf("关闭引擎管理器失败: %v", err)
	} else {
		fmt.Println("✅ 引擎管理器已关闭")
	}
	
	fmt.Println("\n🎉 网络漏洞扫描引擎测试完成!")
}

// testNetworkScanning 测试网络扫描
func testNetworkScanning(manager *core.EngineManager) {
	// 创建扫描配置
	config := &core.ScanConfig{
		TaskID:          "network_test_task",
		ScanMode:        core.ScanModeStandard,
		Timeout:         5 * time.Minute,
		MaxConcurrency:  10,
		EnabledChecks:   []string{"port_scan", "service_detection", "vuln_detection"},
		CustomOptions:   make(map[string]interface{}),
		CreatedAt:       time.Now(),
	}
	
	// 测试本地主机扫描
	fmt.Println("\n🔍 测试本地主机网络扫描...")
	testLocalHostScanning(manager, config)
	
	// 测试域名扫描
	fmt.Println("\n🔍 测试域名网络扫描...")
	testDomainScanning(manager, config)
	
	// 测试网络段扫描
	fmt.Println("\n🔍 测试网络段扫描...")
	testNetworkRangeScanning(manager, config)
}

// testLocalHostScanning 测试本地主机扫描
func testLocalHostScanning(manager *core.EngineManager, config *core.ScanConfig) {
	// 创建本地主机目标
	target := &core.ScanTarget{
		ID:          "localhost_target",
		Type:        core.TargetTypeIP,
		Value:       "127.0.0.1",
		Description: "本地主机网络扫描测试",
		Metadata:    make(map[string]string),
		CreatedAt:   time.Now(),
	}
	
	// 创建扫描请求
	request := &core.ScanRequest{
		ID:        "localhost_scan_" + fmt.Sprintf("%d", time.Now().Unix()),
		Target:    target,
		Config:    config,
		Priority:  1,
		CreatedAt: time.Now(),
	}
	
	// 提交扫描任务
	taskInfo, err := manager.SubmitScanRequest(request)
	if err != nil {
		fmt.Printf("❌ 提交本地主机扫描任务失败: %v\n", err)
		return
	}
	
	fmt.Printf("✅ 本地主机扫描任务已提交: %s\n", taskInfo.ID)
	
	// 监控任务进度
	monitorNetworkTask(manager, taskInfo.ID, "本地主机扫描")
}

// testDomainScanning 测试域名扫描
func testDomainScanning(manager *core.EngineManager, config *core.ScanConfig) {
	// 创建域名目标
	target := &core.ScanTarget{
		ID:          "domain_target",
		Type:        core.TargetTypeDomain,
		Value:       "httpbin.org",
		Description: "域名网络扫描测试",
		Metadata:    make(map[string]string),
		CreatedAt:   time.Now(),
	}
	
	// 创建扫描请求
	request := &core.ScanRequest{
		ID:        "domain_scan_" + fmt.Sprintf("%d", time.Now().Unix()),
		Target:    target,
		Config:    config,
		Priority:  1,
		CreatedAt: time.Now(),
	}
	
	// 提交扫描任务
	taskInfo, err := manager.SubmitScanRequest(request)
	if err != nil {
		fmt.Printf("❌ 提交域名扫描任务失败: %v\n", err)
		return
	}
	
	fmt.Printf("✅ 域名扫描任务已提交: %s\n", taskInfo.ID)
	
	// 监控任务进度
	monitorNetworkTask(manager, taskInfo.ID, "域名扫描")
}

// testNetworkRangeScanning 测试网络段扫描
func testNetworkRangeScanning(manager *core.EngineManager, config *core.ScanConfig) {
	// 创建网络段目标（小范围测试）
	target := &core.ScanTarget{
		ID:          "network_target",
		Type:        core.TargetTypeNetwork,
		Value:       "*********/30", // 只扫描4个IP
		Description: "网络段扫描测试",
		Metadata:    make(map[string]string),
		CreatedAt:   time.Now(),
	}
	
	// 创建扫描请求
	request := &core.ScanRequest{
		ID:        "network_scan_" + fmt.Sprintf("%d", time.Now().Unix()),
		Target:    target,
		Config:    config,
		Priority:  1,
		CreatedAt: time.Now(),
	}
	
	// 提交扫描任务
	taskInfo, err := manager.SubmitScanRequest(request)
	if err != nil {
		fmt.Printf("❌ 提交网络段扫描任务失败: %v\n", err)
		return
	}
	
	fmt.Printf("✅ 网络段扫描任务已提交: %s\n", taskInfo.ID)
	
	// 监控任务进度
	monitorNetworkTask(manager, taskInfo.ID, "网络段扫描")
}

// monitorNetworkTask 监控网络扫描任务
func monitorNetworkTask(manager *core.EngineManager, taskID, taskName string) {
	fmt.Printf("📊 监控%s任务进度: %s\n", taskName, taskID)
	
	maxWaitTime := 2 * time.Minute
	checkInterval := 2 * time.Second
	startTime := time.Now()
	
	for {
		// 检查超时
		if time.Since(startTime) > maxWaitTime {
			fmt.Printf("⏰ %s任务监控超时\n", taskName)
			break
		}
		
		// 获取任务状态
		status, err := manager.GetTaskStatus(taskID)
		if err != nil {
			fmt.Printf("❌ 获取任务状态失败: %v\n", err)
			break
		}
		
		fmt.Printf("   状态: %s, 进度: %.1f%%\n", status.Status, status.Progress)
		
		// 检查任务是否完成
		if status.Status == core.StatusCompleted || status.Status == core.StatusFailed || status.Status == core.StatusCancelled {
			fmt.Printf("✅ %s任务完成!\n", taskName)
			
			// 显示扫描结果摘要
			if result := status.Result; result != nil {
				displayNetworkScanResult(result, taskName)
			}
			break
		}
		
		time.Sleep(checkInterval)
	}
}

// displayNetworkScanResult 显示网络扫描结果
func displayNetworkScanResult(result *core.ScanResult, taskName string) {
	fmt.Printf("\n📋 %s结果摘要:\n", taskName)
	fmt.Printf("   任务ID: %s\n", result.TaskID)
	fmt.Printf("   引擎类型: %s\n", result.EngineType)
	fmt.Printf("   扫描目标: %s\n", result.Target.Value)
	fmt.Printf("   开始时间: %s\n", result.StartedAt.Format("2006-01-02 15:04:05"))
	if result.CompletedAt != nil {
		fmt.Printf("   完成时间: %s\n", result.CompletedAt.Format("2006-01-02 15:04:05"))
	}
	fmt.Printf("   扫描耗时: %s\n", result.Duration)
	fmt.Printf("   发现漏洞: %d个\n", len(result.Vulnerabilities))
	
	// 显示发现的漏洞
	if len(result.Vulnerabilities) > 0 {
		fmt.Printf("\n🔍 发现的漏洞:\n")
		for i, vuln := range result.Vulnerabilities {
			fmt.Printf("   %d. %s (%s)\n", i+1, vuln.Name, vuln.Severity)
			fmt.Printf("      URL: %s\n", vuln.URL)
			fmt.Printf("      描述: %s\n", vuln.Description)
			if vuln.CVE != "" {
				fmt.Printf("      CVE: %s\n", vuln.CVE)
			}
			fmt.Printf("      置信度: %.2f\n", vuln.Confidence)
			fmt.Println()
		}
	}
	
	// 显示扫描统计
	if result.Statistics != nil {
		fmt.Printf("📊 扫描统计:\n")
		fmt.Printf("   总目标数: %d\n", result.Statistics.TotalTargets)
		fmt.Printf("   已扫描目标: %d\n", result.Statistics.ScannedTargets)
		fmt.Printf("   发送请求数: %d\n", result.Statistics.RequestsSent)
		fmt.Printf("   接收响应数: %d\n", result.Statistics.ResponsesReceived)
		if result.Statistics.TotalVulns > 0 {
			fmt.Printf("   漏洞统计: 严重=%d, 高危=%d, 中危=%d, 低危=%d, 信息=%d\n",
				result.Statistics.CriticalVulns,
				result.Statistics.HighVulns,
				result.Statistics.MediumVulns,
				result.Statistics.LowVulns,
				result.Statistics.InfoVulns)
		}
	}
	
	// 显示扫描元数据
	if len(result.Metadata) > 0 {
		fmt.Printf("\n📋 扫描元数据:\n")
		for key, value := range result.Metadata {
			switch key {
			case "resolved_ips":
				if ips, ok := value.([]string); ok {
					fmt.Printf("   解析的IP: %v\n", ips)
				}
			case "port_scan_results":
				if portResults, ok := value.([]*engines.PortScanResult); ok {
					fmt.Printf("   开放端口数: %d\n", len(portResults))
					if len(portResults) > 0 {
						fmt.Printf("   开放端口: ")
						for i, port := range portResults {
							if i > 0 {
								fmt.Printf(", ")
							}
							fmt.Printf("%d/%s", port.Port, port.Protocol)
							if i >= 9 { // 最多显示10个端口
								fmt.Printf("...")
								break
							}
						}
						fmt.Println()
					}
				}
			case "service_scan_results":
				if serviceResults, ok := value.([]*engines.ServiceScanResult); ok {
					fmt.Printf("   识别服务数: %d\n", len(serviceResults))
					if len(serviceResults) > 0 {
						fmt.Printf("   识别的服务:\n")
						for i, service := range serviceResults {
							fmt.Printf("     - %s:%d (%s", service.Host, service.Port, service.Service)
							if service.Version != "" {
								fmt.Printf(" %s", service.Version)
							}
							fmt.Printf(")\n")
							if i >= 4 { // 最多显示5个服务
								fmt.Printf("     ...\n")
								break
							}
						}
					}
				}
			}
		}
	}
	
	// 显示警告信息
	if len(result.Warnings) > 0 {
		fmt.Printf("\n⚠️  警告信息:\n")
		for _, warning := range result.Warnings {
			fmt.Printf("   - %s\n", warning)
		}
	}
}
