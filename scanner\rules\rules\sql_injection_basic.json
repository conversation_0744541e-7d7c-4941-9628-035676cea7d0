{"id": "sql_injection_basic", "name": "基础SQL注入检测", "description": "检测基础的SQL注入漏洞，通过错误信息识别", "category": "sql_injection", "severity": "high", "conditions": [{"type": "response_analysis", "field": "response.body", "operator": "regex", "regex": "(?i)(mysql_fetch_array|ORA-\\d+|Microsoft.*ODBC.*SQL Server|PostgreSQL.*ERROR|Warning.*mysql_.*|valid MySQL result|MySqlClient\\.|SQL syntax.*MySQL|Warning.*\\Wmysql_|valid MySQL result|PostgreSQL query failed|SQL error|syntax error)", "case_sensitive": false, "metadata": {"description": "检测SQL错误信息"}}, {"type": "request_analysis", "field": "request.method", "operator": "equals", "value": "GET", "case_sensitive": false, "metadata": {"description": "仅检测GET请求"}}], "actions": [{"type": "create_vulnerability", "target": "sql_injection", "parameters": {"name": "SQL注入漏洞", "description": "检测到SQL注入漏洞，攻击者可能能够执行任意SQL查询", "impact": "可能导致数据泄露、数据篡改或系统完全妥协", "solution": "使用参数化查询或预编译语句，验证和过滤用户输入", "references": ["https://owasp.org/www-community/attacks/SQL_Injection"]}}, {"type": "log", "parameters": {"message": "检测到SQL注入漏洞"}}], "tags": ["sql-injection", "database", "injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection", "https://portswigger.net/web-security/sql-injection"], "author": "VulnScanner Team", "version": "1.0.0", "enabled": true, "config": {"timeout": 30, "max_payloads": 10, "confidence_threshold": 0.8}}