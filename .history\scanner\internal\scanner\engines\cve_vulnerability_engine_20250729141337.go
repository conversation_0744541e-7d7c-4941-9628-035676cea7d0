package engines

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"scanner/internal/scanner/core"
)

// CVEVulnerabilityEngine CVE漏洞扫描引擎
// 负责基于CVE数据库的历史漏洞检测
type CVEVulnerabilityEngine struct {
	*core.BaseEngine

	// HTTP客户端
	httpClient *http.Client

	// 配置
	config *CVEVulnConfig

	// CVE数据库
	cveDatabase *CVEDatabase

	// 检测器
	cveDetector *CVEDetector

	// 规则引擎
	ruleEngine *core.RuleEngine
}

// CVEVulnConfig CVE漏洞扫描配置
type CVEVulnConfig struct {
	// 基础配置
	CVEDatabasePath string   `json:"cve_database_path"`
	EnabledYears    []int    `json:"enabled_years"`
	SeverityFilter  []string `json:"severity_filter"`
	CategoryFilter  []string `json:"category_filter"`

	// 扫描配置
	ScanTimeout    time.Duration `json:"scan_timeout"`
	RequestTimeout time.Duration `json:"request_timeout"`
	MaxConcurrency int           `json:"max_concurrency"`
	ScanDelay      time.Duration `json:"scan_delay"`

	// 检测配置
	EnableFingerprint  bool `json:"enable_fingerprint"`
	EnableVersionCheck bool `json:"enable_version_check"`
	EnablePayloadTest  bool `json:"enable_payload_test"`
	EnableHistoryCheck bool `json:"enable_history_check"`

	// 高级配置
	UserAgent           string  `json:"user_agent"`
	MaxRetries          int     `json:"max_retries"`
	ConfidenceThreshold float64 `json:"confidence_threshold"`
}

// CVEDatabase CVE数据库
type CVEDatabase struct {
	cveRules      map[string]*CVERule
	yearIndex     map[int][]*CVERule
	categoryIndex map[string][]*CVERule
	severityIndex map[string][]*CVERule
	mutex         sync.RWMutex
	loaded        bool
}

// CVERule CVE规则
type CVERule struct {
	ID             string           `json:"id"`
	Year           int              `json:"year"`
	Description    string           `json:"description"`
	CVSS           float64          `json:"cvss"`
	Severity       string           `json:"severity"`
	Vector         string           `json:"vector"`
	Category       string           `json:"category"`
	CWE            []string         `json:"cwe"`
	Framework      []string         `json:"framework"`
	Component      []string         `json:"component"`
	POCAvailable   bool             `json:"poc_available"`
	POCPath        string           `json:"poc_path"`
	ExploitCode    string           `json:"exploit_code"`
	DetectionRules []string         `json:"detection_rules"`
	Payloads       []string         `json:"payloads"`
	Verification   *CVEVerification `json:"verification"`
	PublishedDate  time.Time        `json:"published_date"`
	ModifiedDate   time.Time        `json:"modified_date"`
	References     []string         `json:"references"`
	Tags           []string         `json:"tags"`
	ExploitCount   int              `json:"exploit_count"`
	SuccessRate    float64          `json:"success_rate"`
	LastUsed       time.Time        `json:"last_used"`
}

// CVEVerification CVE验证配置
type CVEVerification struct {
	Method            string   `json:"method"`
	ExpectedResults   []string `json:"expected_results"`
	VerificationSteps []string `json:"verification_steps"`
	Confidence        float64  `json:"confidence"`
}

// CVEDetector CVE检测器
type CVEDetector struct {
	config     *CVEVulnConfig
	httpClient *http.Client
	database   *CVEDatabase
}

// CVEDetectionResult CVE检测结果
type CVEDetectionResult struct {
	CVERule    *CVERule  `json:"cve_rule"`
	Matched    bool      `json:"matched"`
	Confidence float64   `json:"confidence"`
	Evidence   []string  `json:"evidence"`
	Response   string    `json:"response"`
	Timestamp  time.Time `json:"timestamp"`
}

// NewCVEVulnerabilityEngine 创建CVE漏洞扫描引擎
func NewCVEVulnerabilityEngine() *CVEVulnerabilityEngine {
	baseEngine := core.NewBaseEngine(
		"CVE漏洞扫描引擎",
		core.EngineTypeCVEScanning,
		"1.0.0",
		"负责基于CVE数据库的历史漏洞检测，支持从2000年至今的CVE漏洞扫描",
	)

	// 创建HTTP客户端
	httpClient := &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,
		},
	}

	config := &CVEVulnConfig{
		CVEDatabasePath:     "rules/cve",
		EnabledYears:        generateYearRange(2000, 2025), // 支持2000-2025年的CVE
		SeverityFilter:      []string{"critical", "high", "medium", "low"},
		CategoryFilter:      []string{}, // 空表示所有类别
		ScanTimeout:         10 * time.Minute,
		RequestTimeout:      30 * time.Second,
		MaxConcurrency:      10,
		ScanDelay:           100 * time.Millisecond,
		EnableFingerprint:   true,
		EnableVersionCheck:  true,
		EnablePayloadTest:   true,
		EnableHistoryCheck:  true,
		UserAgent:           "CVE-Scanner/1.0",
		MaxRetries:          3,
		ConfidenceThreshold: 0.7,
	}

	// 创建CVE数据库
	cveDatabase := NewCVEDatabase()

	// 创建规则引擎
	ruleEngine := core.NewRuleEngine("rules")

	engine := &CVEVulnerabilityEngine{
		BaseEngine:  baseEngine,
		httpClient:  httpClient,
		config:      config,
		cveDatabase: cveDatabase,
		cveDetector: NewCVEDetector(config, httpClient, cveDatabase),
		ruleEngine:  ruleEngine,
	}

	return engine
}

// generateYearRange 生成年份范围
func generateYearRange(start, end int) []int {
	years := make([]int, 0, end-start+1)
	for year := start; year <= end; year++ {
		years = append(years, year)
	}
	return years
}

// GetSupportedTargets 获取支持的目标类型
func (e *CVEVulnerabilityEngine) GetSupportedTargets() []core.TargetType {
	return []core.TargetType{
		core.TargetTypeURL,
		core.TargetTypeIP,
		core.TargetTypeDomain,
		core.TargetTypeHost,
	}
}

// CanScan 检查是否能扫描指定目标
func (e *CVEVulnerabilityEngine) CanScan(target *core.ScanTarget) bool {
	if !e.IsEnabled() {
		return false
	}

	supportedTargets := e.GetSupportedTargets()
	for _, supportedType := range supportedTargets {
		if target.Type == supportedType {
			return true
		}
	}
	return false
}

// Initialize 初始化引擎
func (e *CVEVulnerabilityEngine) Initialize() error {
	if err := e.BaseEngine.Initialize(); err != nil {
		return err
	}

	// 加载CVE数据库
	if err := e.cveDatabase.LoadCVEDatabase(e.config.CVEDatabasePath); err != nil {
		return fmt.Errorf("加载CVE数据库失败: %v", err)
	}

	// 初始化规则引擎
	if err := e.ruleEngine.Initialize(); err != nil {
		e.LogWarn("规则引擎初始化失败: %v", err)
	}

	e.LogInfo("CVE漏洞扫描引擎初始化完成，加载了 %d 个CVE规则", e.cveDatabase.GetCVECount())
	return nil
}

// Scan 执行CVE漏洞扫描
func (e *CVEVulnerabilityEngine) Scan(ctx context.Context, request *core.ScanRequest) (*core.ScanResult, error) {
	// 创建任务上下文
	task, taskCtx := e.CreateTaskContext(request)
	defer func() {
		if task.CancelFunc != nil {
			task.CancelFunc()
		}
	}()

	e.LogInfo("开始CVE漏洞扫描: %s", request.Target.Value)

	// 创建扫描结果
	result := &core.ScanResult{
		ID:              fmt.Sprintf("cve_%s_%d", request.ID, time.Now().Unix()),
		TaskID:          request.Config.TaskID,
		EngineType:      e.GetType(),
		Target:          request.Target,
		Status:          core.StatusRunning,
		Progress:        0.0,
		StartedAt:       time.Now(),
		Vulnerabilities: make([]*core.Vulnerability, 0),
		Statistics:      &core.ScanStatistics{},
		Warnings:        make([]string, 0),
		Metadata:        make(map[string]interface{}),
	}

	// 执行扫描步骤
	steps := []struct {
		name     string
		progress float64
		function func(context.Context, *core.ScanRequest, *core.ScanResult) error
	}{
		{"目标分析", 10, e.analyzeTarget},
		{"指纹识别", 25, e.fingerprintTarget},
		{"版本检测", 40, e.detectVersions},
		{"CVE匹配", 60, e.matchCVEs},
		{"载荷测试", 80, e.testPayloads},
		{"历史检查", 90, e.checkHistory},
		{"结果分析", 100, e.analyzeCVEResults},
	}

	for _, step := range steps {
		select {
		case <-taskCtx.Done():
			result.Status = core.StatusCancelled
			e.CompleteTask(request.ID, result, fmt.Errorf("扫描被取消"))
			return result, fmt.Errorf("扫描被取消")
		default:
		}

		e.LogInfo("执行步骤: %s", step.name)
		e.UpdateTaskProgress(request.ID, step.progress, step.name)

		if err := step.function(taskCtx, request, result); err != nil {
			e.LogError("步骤执行失败 %s: %v", step.name, err)
			result.Warnings = append(result.Warnings, fmt.Sprintf("步骤 %s 执行失败: %v", step.name, err))
		}

		// 添加扫描延迟
		time.Sleep(e.config.ScanDelay)
	}

	// 完成扫描
	result.Status = core.StatusCompleted
	result.Progress = 100.0
	completedAt := time.Now()
	result.CompletedAt = &completedAt
	result.Duration = time.Since(result.StartedAt)

	// 更新统计信息
	e.updateCVEStatistics(result)

	e.LogInfo("CVE漏洞扫描完成: %s，发现 %d 个CVE漏洞", request.Target.Value, len(result.Vulnerabilities))
	e.CompleteTask(request.ID, result, nil)

	return result, nil
}

// analyzeTarget 分析目标
func (e *CVEVulnerabilityEngine) analyzeTarget(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	target := request.Target

	// 记录目标信息
	result.Metadata["target_type"] = target.Type
	result.Metadata["target_value"] = target.Value
	result.Metadata["scan_start_time"] = time.Now()

	// 根据目标类型进行不同的分析
	switch target.Type {
	case core.TargetTypeURL:
		// URL目标分析
		if err := e.analyzeURLTarget(ctx, target, result); err != nil {
			return err
		}

	case core.TargetTypeIP, core.TargetTypeDomain, core.TargetTypeHost:
		// 网络目标分析
		if err := e.analyzeNetworkTarget(ctx, target, result); err != nil {
			return err
		}

	default:
		return fmt.Errorf("不支持的目标类型: %s", target.Type)
	}

	e.LogInfo("目标分析完成: %s", target.Value)
	return nil
}

// analyzeURLTarget 分析URL目标
func (e *CVEVulnerabilityEngine) analyzeURLTarget(ctx context.Context, target *core.ScanTarget, result *core.ScanResult) error {
	// 发送HTTP请求获取基础信息
	req, err := http.NewRequestWithContext(ctx, "GET", target.Value, nil)
	if err != nil {
		return err
	}
	req.Header.Set("User-Agent", e.config.UserAgent)

	resp, err := e.httpClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	// 保存响应信息
	result.Metadata["http_status"] = resp.StatusCode
	result.Metadata["http_headers"] = e.convertHeaders(resp.Header)
	result.Metadata["http_body"] = string(body)
	result.Metadata["content_length"] = len(body)

	return nil
}

// analyzeNetworkTarget 分析网络目标
func (e *CVEVulnerabilityEngine) analyzeNetworkTarget(ctx context.Context, target *core.ScanTarget, result *core.ScanResult) error {
	// 对于网络目标，尝试HTTP和HTTPS连接
	schemes := []string{"http", "https"}
	ports := []string{"80", "443", "8080", "8443"}

	for _, scheme := range schemes {
		for _, port := range ports {
			url := fmt.Sprintf("%s://%s:%s", scheme, target.Value, port)

			req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
			if err != nil {
				continue
			}
			req.Header.Set("User-Agent", e.config.UserAgent)

			resp, err := e.httpClient.Do(req)
			if err != nil {
				continue
			}
			resp.Body.Close()

			// 记录成功的连接
			if result.Metadata["accessible_urls"] == nil {
				result.Metadata["accessible_urls"] = make([]string, 0)
			}
			urls := result.Metadata["accessible_urls"].([]string)
			result.Metadata["accessible_urls"] = append(urls, url)

			break // 找到一个可访问的URL就够了
		}
	}

	return nil
}

// convertHeaders 转换HTTP头格式
func (e *CVEVulnerabilityEngine) convertHeaders(headers http.Header) map[string]string {
	result := make(map[string]string)
	for name, values := range headers {
		if len(values) > 0 {
			result[strings.ToLower(name)] = values[0]
		}
	}
	return result
}

// fingerprintTarget 指纹识别
func (e *CVEVulnerabilityEngine) fingerprintTarget(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	// 从HTTP响应中提取指纹信息
	if headers, exists := result.Metadata["http_headers"]; exists {
		headerMap := headers.(map[string]string)

		// 提取服务器信息
		if server, exists := headerMap["server"]; exists {
			result.Metadata["server_fingerprint"] = e.parseServerFingerprint(server)
		}

		// 提取X-Powered-By信息
		if poweredBy, exists := headerMap["x-powered-by"]; exists {
			result.Metadata["powered_by"] = poweredBy
		}

		// 提取其他技术栈信息
		techStack := e.extractTechStack(headerMap)
		if len(techStack) > 0 {
			result.Metadata["tech_stack"] = techStack
		}
	}

	// 从响应体中提取指纹信息
	if body, exists := result.Metadata["http_body"]; exists {
		bodyStr := body.(string)

		// 提取框架信息
		frameworks := e.extractFrameworks(bodyStr)
		if len(frameworks) > 0 {
			result.Metadata["frameworks"] = frameworks
		}

		// 提取版本信息
		versions := e.extractVersions(bodyStr)
		if len(versions) > 0 {
			result.Metadata["versions"] = versions
		}
	}

	e.LogInfo("指纹识别完成")
	return nil
}

// parseServerFingerprint 解析服务器指纹
func (e *CVEVulnerabilityEngine) parseServerFingerprint(server string) map[string]string {
	fingerprint := make(map[string]string)

	// 常见服务器指纹模式
	patterns := map[string]*regexp.Regexp{
		"apache":   regexp.MustCompile(`(?i)apache[/\s]+([0-9.]+)`),
		"nginx":    regexp.MustCompile(`(?i)nginx[/\s]+([0-9.]+)`),
		"iis":      regexp.MustCompile(`(?i)microsoft-iis[/\s]+([0-9.]+)`),
		"tomcat":   regexp.MustCompile(`(?i)apache-tomcat[/\s]+([0-9.]+)`),
		"jetty":    regexp.MustCompile(`(?i)jetty[/\s]+([0-9.]+)`),
		"lighttpd": regexp.MustCompile(`(?i)lighttpd[/\s]+([0-9.]+)`),
	}

	for name, pattern := range patterns {
		if matches := pattern.FindStringSubmatch(server); len(matches) > 1 {
			fingerprint[name] = matches[1]
		}
	}

	return fingerprint
}

// extractTechStack 提取技术栈信息
func (e *CVEVulnerabilityEngine) extractTechStack(headers map[string]string) []string {
	var techStack []string

	// 检查常见的技术栈标识
	techIndicators := map[string][]string{
		"php":     {"x-powered-by"},
		"asp.net": {"x-aspnet-version", "x-powered-by"},
		"jsp":     {"x-powered-by"},
		"nodejs":  {"x-powered-by"},
		"express": {"x-powered-by"},
		"django":  {"x-powered-by"},
		"rails":   {"x-powered-by"},
		"spring":  {"x-powered-by"},
	}

	for tech, headerKeys := range techIndicators {
		for _, key := range headerKeys {
			if value, exists := headers[key]; exists {
				if strings.Contains(strings.ToLower(value), tech) {
					techStack = append(techStack, tech)
					break
				}
			}
		}
	}

	return techStack
}

// extractFrameworks 从响应体提取框架信息
func (e *CVEVulnerabilityEngine) extractFrameworks(body string) []string {
	var frameworks []string

	// 常见框架特征
	frameworkPatterns := map[string]*regexp.Regexp{
		"struts":    regexp.MustCompile(`(?i)struts[_-]?([0-9.]+)`),
		"spring":    regexp.MustCompile(`(?i)spring[_-]?([0-9.]+)`),
		"hibernate": regexp.MustCompile(`(?i)hibernate[_-]?([0-9.]+)`),
		"log4j":     regexp.MustCompile(`(?i)log4j[_-]?([0-9.]+)`),
		"jackson":   regexp.MustCompile(`(?i)jackson[_-]?([0-9.]+)`),
		"fastjson":  regexp.MustCompile(`(?i)fastjson[_-]?([0-9.]+)`),
		"shiro":     regexp.MustCompile(`(?i)shiro[_-]?([0-9.]+)`),
		"drupal":    regexp.MustCompile(`(?i)drupal[_-]?([0-9.]+)`),
		"wordpress": regexp.MustCompile(`(?i)wordpress[_-]?([0-9.]+)`),
		"joomla":    regexp.MustCompile(`(?i)joomla[_-]?([0-9.]+)`),
	}

	for framework, pattern := range frameworkPatterns {
		if pattern.MatchString(body) {
			frameworks = append(frameworks, framework)
		}
	}

	return frameworks
}

// extractVersions 提取版本信息
func (e *CVEVulnerabilityEngine) extractVersions(body string) map[string]string {
	versions := make(map[string]string)

	// 版本提取模式
	versionPatterns := map[string]*regexp.Regexp{
		"jquery":    regexp.MustCompile(`(?i)jquery[_-]?v?([0-9.]+)`),
		"bootstrap": regexp.MustCompile(`(?i)bootstrap[_-]?v?([0-9.]+)`),
		"angular":   regexp.MustCompile(`(?i)angular[_-]?v?([0-9.]+)`),
		"react":     regexp.MustCompile(`(?i)react[_-]?v?([0-9.]+)`),
		"vue":       regexp.MustCompile(`(?i)vue[_-]?v?([0-9.]+)`),
	}

	for component, pattern := range versionPatterns {
		if matches := pattern.FindStringSubmatch(body); len(matches) > 1 {
			versions[component] = matches[1]
		}
	}

	return versions
}

// detectVersions 检测版本
func (e *CVEVulnerabilityEngine) detectVersions(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	// 基于指纹信息检测具体版本
	detectedVersions := make(map[string]string)

	// 从服务器指纹中获取版本
	if fingerprint, exists := result.Metadata["server_fingerprint"]; exists {
		fingerprintMap := fingerprint.(map[string]string)
		for server, version := range fingerprintMap {
			detectedVersions[server] = version
		}
	}

	// 从技术栈中获取版本
	if versions, exists := result.Metadata["versions"]; exists {
		versionMap := versions.(map[string]string)
		for component, version := range versionMap {
			detectedVersions[component] = version
		}
	}

	// 保存检测到的版本
	result.Metadata["detected_versions"] = detectedVersions

	e.LogInfo("版本检测完成，检测到 %d 个组件版本", len(detectedVersions))
	return nil
}

// matchCVEs CVE匹配
func (e *CVEVulnerabilityEngine) matchCVEs(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	// 获取检测到的版本信息
	detectedVersions, exists := result.Metadata["detected_versions"]
	if !exists {
		e.LogWarn("未检测到版本信息，跳过CVE匹配")
		return nil
	}

	versionMap := detectedVersions.(map[string]string)
	var matchedCVEs []*CVERule

	// 遍历所有CVE规则进行匹配
	for component, version := range versionMap {
		cves := e.cveDatabase.FindCVEsByComponent(component)
		for _, cve := range cves {
			if e.isVersionVulnerable(component, version, cve) {
				matchedCVEs = append(matchedCVEs, cve)
			}
		}
	}

	// 保存匹配的CVE
	result.Metadata["matched_cves"] = matchedCVEs

	e.LogInfo("CVE匹配完成，匹配到 %d 个潜在CVE漏洞", len(matchedCVEs))
	return nil
}

// isVersionVulnerable 检查版本是否存在漏洞
func (e *CVEVulnerabilityEngine) isVersionVulnerable(component, version string, cve *CVERule) bool {
	// 检查组件是否匹配
	componentMatch := false
	for _, cveComponent := range cve.Component {
		if strings.Contains(strings.ToLower(cveComponent), strings.ToLower(component)) {
			componentMatch = true
			break
		}
	}

	if !componentMatch {
		return false
	}

	// 检查框架是否匹配
	frameworkMatch := false
	for _, framework := range cve.Framework {
		if strings.Contains(strings.ToLower(framework), strings.ToLower(component)) {
			frameworkMatch = true
			break
		}
	}

	// 组件匹配或框架匹配都可以
	if !componentMatch && !frameworkMatch {
		return false
	}

	// 简化的版本比较（实际应该更复杂）
	// 这里假设如果CVE年份较新且组件匹配，则可能存在漏洞
	currentYear := time.Now().Year()
	if cve.Year >= currentYear-5 { // 最近5年的CVE
		return true
	}

	return false
}

// testPayloads 载荷测试
func (e *CVEVulnerabilityEngine) testPayloads(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	if !e.config.EnablePayloadTest {
		e.LogInfo("载荷测试已禁用，跳过")
		return nil
	}

	// 获取匹配的CVE
	matchedCVEs, exists := result.Metadata["matched_cves"]
	if !exists {
		return nil
	}

	cveList := matchedCVEs.([]*CVERule)
	var testedCVEs []*CVEDetectionResult

	// 对每个匹配的CVE进行载荷测试
	for _, cve := range cveList {
		if len(cve.Payloads) == 0 {
			continue
		}

		detectionResult := e.cveDetector.TestCVEPayloads(ctx, request.Target, cve)
		if detectionResult != nil {
			testedCVEs = append(testedCVEs, detectionResult)

			// 如果检测成功，创建漏洞记录
			if detectionResult.Matched && detectionResult.Confidence >= e.config.ConfidenceThreshold {
				vuln := e.convertCVEToVulnerability(detectionResult, request.Target)
				result.Vulnerabilities = append(result.Vulnerabilities, vuln)
			}
		}
	}

	// 保存测试结果
	result.Metadata["tested_cves"] = testedCVEs

	e.LogInfo("载荷测试完成，测试了 %d 个CVE，发现 %d 个漏洞", len(testedCVEs), len(result.Vulnerabilities))
	return nil
}

// checkHistory 历史检查
func (e *CVEVulnerabilityEngine) checkHistory(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	if !e.config.EnableHistoryCheck {
		e.LogInfo("历史检查已禁用，跳过")
		return nil
	}

	// 基于目标特征进行历史CVE检查
	// 这里可以实现更复杂的历史漏洞检查逻辑

	e.LogInfo("历史检查完成")
	return nil
}

// analyzeCVEResults 分析CVE结果
func (e *CVEVulnerabilityEngine) analyzeCVEResults(ctx context.Context, request *core.ScanRequest, result *core.ScanResult) error {
	// 统计CVE漏洞
	criticalCount := 0
	highCount := 0
	mediumCount := 0
	lowCount := 0

	for _, vuln := range result.Vulnerabilities {
		switch vuln.Severity {
		case core.SeverityCritical:
			criticalCount++
		case core.SeverityHigh:
			highCount++
		case core.SeverityMedium:
			mediumCount++
		case core.SeverityLow:
			lowCount++
		}
	}

	// 更新统计信息
	result.Statistics.TotalVulns = len(result.Vulnerabilities)
	result.Statistics.CriticalVulns = criticalCount
	result.Statistics.HighVulns = highCount
	result.Statistics.MediumVulns = mediumCount
	result.Statistics.LowVulns = lowCount

	// 生成CVE扫描报告摘要
	summary := map[string]interface{}{
		"total_cves_checked":   e.cveDatabase.GetCVECount(),
		"matched_cves":         len(result.Metadata["matched_cves"].([]*CVERule)),
		"confirmed_vulns":      len(result.Vulnerabilities),
		"scan_duration":        result.Duration,
		"confidence_threshold": e.config.ConfidenceThreshold,
	}
	result.Metadata["scan_summary"] = summary

	return nil
}

// updateCVEStatistics 更新CVE扫描统计信息
func (e *CVEVulnerabilityEngine) updateCVEStatistics(result *core.ScanResult) {
	result.Statistics.TotalTargets = 1
	result.Statistics.ScannedTargets = 1
	result.Statistics.ScanDuration = result.Duration

	// 计算请求统计
	if testedCVEs, exists := result.Metadata["tested_cves"]; exists {
		testedList := testedCVEs.([]*CVEDetectionResult)
		result.Statistics.RequestsSent = len(testedList)
		result.Statistics.ResponsesReceived = len(testedList)
	}
}

// convertCVEToVulnerability 将CVE检测结果转换为漏洞信息
func (e *CVEVulnerabilityEngine) convertCVEToVulnerability(detection *CVEDetectionResult, target *core.ScanTarget) *core.Vulnerability {
	cve := detection.CVERule

	vuln := &core.Vulnerability{
		ID:          fmt.Sprintf("cve_%s_%d", cve.ID, time.Now().Unix()),
		Name:        fmt.Sprintf("%s - %s", cve.ID, cve.Description),
		Type:        cve.Category,
		URL:         target.Value,
		Description: cve.Description,
		CVE:         cve.ID,
		CVSS:        cve.CVSS,
		References:  cve.References,
		Tags:        cve.Tags,
		Evidence: &core.VulnEvidence{
			Type:     "CVE Detection",
			Content:  strings.Join(detection.Evidence, "; "),
			Location: target.Value,
		},
		Response:     detection.Response,
		Confidence:   detection.Confidence,
		DiscoveredAt: detection.Timestamp,
	}

	// 设置严重程度
	switch cve.Severity {
	case "critical":
		vuln.Severity = core.SeverityCritical
		vuln.Risk = core.RiskCritical
	case "high":
		vuln.Severity = core.SeverityHigh
		vuln.Risk = core.RiskHigh
	case "medium":
		vuln.Severity = core.SeverityMedium
		vuln.Risk = core.RiskMedium
	case "low":
		vuln.Severity = core.SeverityLow
		vuln.Risk = core.RiskLow
	default:
		vuln.Severity = core.SeverityInfo
		vuln.Risk = core.RiskInfo
	}

	return vuln
}

// NewCVEDatabase 创建CVE数据库
func NewCVEDatabase() *CVEDatabase {
	return &CVEDatabase{
		cveRules:      make(map[string]*CVERule),
		yearIndex:     make(map[int][]*CVERule),
		categoryIndex: make(map[string][]*CVERule),
		severityIndex: make(map[string][]*CVERule),
		loaded:        false,
	}
}

// LoadCVEDatabase 加载CVE数据库
func (db *CVEDatabase) LoadCVEDatabase(cveDir string) error {
	db.mutex.Lock()
	defer db.mutex.Unlock()

	if db.loaded {
		return nil
	}

	// 查找所有CVE JSON文件
	pattern := filepath.Join(cveDir, "*.json")
	files, err := filepath.Glob(pattern)
	if err != nil {
		return fmt.Errorf("查找CVE文件失败: %v", err)
	}

	loadedCount := 0
	for _, file := range files {
		if err := db.loadCVEFile(file); err != nil {
			// 记录错误但继续加载其他文件
			continue
		}
		loadedCount++
	}

	// 构建索引
	db.buildIndexes()

	db.loaded = true
	return nil
}

// loadCVEFile 加载单个CVE文件
func (db *CVEDatabase) loadCVEFile(filename string) error {
	data, err := ioutil.ReadFile(filename)
	if err != nil {
		return err
	}

	var cve CVERule
	if err := json.Unmarshal(data, &cve); err != nil {
		return err
	}

	// 验证CVE规则
	if cve.ID == "" {
		return fmt.Errorf("CVE ID不能为空")
	}

	db.cveRules[cve.ID] = &cve
	return nil
}

// buildIndexes 构建索引
func (db *CVEDatabase) buildIndexes() {
	for _, cve := range db.cveRules {
		// 年份索引
		db.yearIndex[cve.Year] = append(db.yearIndex[cve.Year], cve)

		// 分类索引
		db.categoryIndex[cve.Category] = append(db.categoryIndex[cve.Category], cve)

		// 严重程度索引
		db.severityIndex[cve.Severity] = append(db.severityIndex[cve.Severity], cve)
	}
}

// GetCVECount 获取CVE数量
func (db *CVEDatabase) GetCVECount() int {
	db.mutex.RLock()
	defer db.mutex.RUnlock()
	return len(db.cveRules)
}

// FindCVEsByComponent 根据组件查找CVE
func (db *CVEDatabase) FindCVEsByComponent(component string) []*CVERule {
	db.mutex.RLock()
	defer db.mutex.RUnlock()

	var results []*CVERule
	component = strings.ToLower(component)

	for _, cve := range db.cveRules {
		// 检查组件匹配
		for _, cveComponent := range cve.Component {
			if strings.Contains(strings.ToLower(cveComponent), component) {
				results = append(results, cve)
				break
			}
		}

		// 检查框架匹配
		for _, framework := range cve.Framework {
			if strings.Contains(strings.ToLower(framework), component) {
				results = append(results, cve)
				break
			}
		}
	}

	return results
}

// FindCVEsByYear 根据年份查找CVE
func (db *CVEDatabase) FindCVEsByYear(year int) []*CVERule {
	db.mutex.RLock()
	defer db.mutex.RUnlock()

	return db.yearIndex[year]
}

// FindCVEsByCategory 根据分类查找CVE
func (db *CVEDatabase) FindCVEsByCategory(category string) []*CVERule {
	db.mutex.RLock()
	defer db.mutex.RUnlock()

	return db.categoryIndex[category]
}

// FindCVEsBySeverity 根据严重程度查找CVE
func (db *CVEDatabase) FindCVEsBySeverity(severity string) []*CVERule {
	db.mutex.RLock()
	defer db.mutex.RUnlock()

	return db.severityIndex[severity]
}

// GetCVE 获取指定CVE
func (db *CVEDatabase) GetCVE(cveID string) (*CVERule, bool) {
	db.mutex.RLock()
	defer db.mutex.RUnlock()

	cve, exists := db.cveRules[cveID]
	return cve, exists
}

// NewCVEDetector 创建CVE检测器
func NewCVEDetector(config *CVEVulnConfig, httpClient *http.Client, database *CVEDatabase) *CVEDetector {
	return &CVEDetector{
		config:     config,
		httpClient: httpClient,
		database:   database,
	}
}

// TestCVEPayloads 测试CVE载荷
func (detector *CVEDetector) TestCVEPayloads(ctx context.Context, target *core.ScanTarget, cve *CVERule) *CVEDetectionResult {
	result := &CVEDetectionResult{
		CVERule:    cve,
		Matched:    false,
		Confidence: 0.0,
		Evidence:   make([]string, 0),
		Timestamp:  time.Now(),
	}

	// 如果没有载荷，跳过测试
	if len(cve.Payloads) == 0 {
		return result
	}

	// 根据目标类型选择测试方法
	switch target.Type {
	case core.TargetTypeURL:
		return detector.testHTTPPayloads(ctx, target, cve, result)
	case core.TargetTypeIP, core.TargetTypeDomain, core.TargetTypeHost:
		return detector.testNetworkPayloads(ctx, target, cve, result)
	default:
		return result
	}
}

// testHTTPPayloads 测试HTTP载荷
func (detector *CVEDetector) testHTTPPayloads(ctx context.Context, target *core.ScanTarget, cve *CVERule, result *CVEDetectionResult) *CVEDetectionResult {
	baseURL := target.Value

	// 测试每个载荷
	for _, payload := range cve.Payloads {
		// 构造测试URL
		testURL := detector.buildTestURL(baseURL, payload, cve)

		// 发送HTTP请求
		req, err := http.NewRequestWithContext(ctx, "GET", testURL, nil)
		if err != nil {
			continue
		}

		// 设置特殊头部（如果载荷包含头部信息）
		detector.setSpecialHeaders(req, payload, cve)

		resp, err := detector.httpClient.Do(req)
		if err != nil {
			continue
		}

		// 读取响应
		body, err := ioutil.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			continue
		}

		responseStr := string(body)
		result.Response = responseStr

		// 分析响应
		if detector.analyzeResponse(resp, responseStr, cve, result) {
			result.Matched = true
			result.Confidence = cve.Verification.Confidence
			break
		}
	}

	return result
}

// testNetworkPayloads 测试网络载荷
func (detector *CVEDetector) testNetworkPayloads(ctx context.Context, target *core.ScanTarget, cve *CVERule, result *CVEDetectionResult) *CVEDetectionResult {
	// 对于网络目标，尝试HTTP/HTTPS连接
	schemes := []string{"http", "https"}
	ports := []string{"80", "443", "8080", "8443"}

	for _, scheme := range schemes {
		for _, port := range ports {
			url := fmt.Sprintf("%s://%s:%s", scheme, target.Value, port)

			// 创建临时URL目标进行测试
			urlTarget := &core.ScanTarget{
				Type:  core.TargetTypeURL,
				Value: url,
			}

			testResult := detector.testHTTPPayloads(ctx, urlTarget, cve, result)
			if testResult.Matched {
				return testResult
			}
		}
	}

	return result
}

// buildTestURL 构造测试URL
func (detector *CVEDetector) buildTestURL(baseURL, payload string, cve *CVERule) string {
	// 如果载荷是完整URL，直接使用
	if strings.HasPrefix(payload, "http://") || strings.HasPrefix(payload, "https://") {
		return payload
	}

	// 如果载荷是路径，拼接到基础URL
	if strings.HasPrefix(payload, "/") {
		return strings.TrimRight(baseURL, "/") + payload
	}

	// 如果载荷是参数，添加到URL
	if strings.Contains(payload, "=") {
		separator := "?"
		if strings.Contains(baseURL, "?") {
			separator = "&"
		}
		return baseURL + separator + payload
	}

	// 默认作为路径处理
	return strings.TrimRight(baseURL, "/") + "/" + payload
}

// setSpecialHeaders 设置特殊头部
func (detector *CVEDetector) setSpecialHeaders(req *http.Request, payload string, cve *CVERule) {
	req.Header.Set("User-Agent", detector.config.UserAgent)

	// 根据CVE类型设置特殊头部
	switch cve.Category {
	case "remote_code_execution":
		// 对于RCE类型，可能需要特殊的Content-Type
		if strings.Contains(payload, "Content-Type:") {
			// 从载荷中提取Content-Type
			if idx := strings.Index(payload, "Content-Type:"); idx != -1 {
				contentType := strings.TrimSpace(payload[idx+13:])
				if endIdx := strings.Index(contentType, "\n"); endIdx != -1 {
					contentType = contentType[:endIdx]
				}
				req.Header.Set("Content-Type", contentType)
			}
		}

	case "command_injection":
		// 对于命令注入，设置特殊的User-Agent
		req.Header.Set("User-Agent", detector.config.UserAgent+" (CVE-Test)")

	default:
		// 默认头部
	}
}

// analyzeResponse 分析响应
func (detector *CVEDetector) analyzeResponse(resp *http.Response, body string, cve *CVERule, result *CVEDetectionResult) bool {
	matched := false

	// 检查HTTP状态码
	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		result.Evidence = append(result.Evidence, fmt.Sprintf("HTTP状态码: %d", resp.StatusCode))
	}

	// 根据验证方法进行分析
	switch cve.Verification.Method {
	case "response_analysis":
		matched = detector.analyzeResponseContent(resp, body, cve, result)

	case "historical_analysis":
		matched = detector.analyzeHistoricalPattern(resp, body, cve, result)

	default:
		// 默认分析方法
		matched = detector.analyzeResponseContent(resp, body, cve, result)
	}

	return matched
}

// analyzeResponseContent 分析响应内容
func (detector *CVEDetector) analyzeResponseContent(resp *http.Response, body string, cve *CVERule, result *CVEDetectionResult) bool {
	matched := false

	// 检查期望的结果
	for _, expectedResult := range cve.Verification.ExpectedResults {
		if strings.Contains(strings.ToLower(body), strings.ToLower(expectedResult)) {
			result.Evidence = append(result.Evidence, fmt.Sprintf("发现期望结果: %s", expectedResult))
			matched = true
		}
	}

	// 检查错误模式（可能表示漏洞存在）
	errorPatterns := []string{
		"error", "exception", "stack trace", "debug", "warning",
		"sql", "mysql", "oracle", "postgresql", "sqlite",
		"java.lang", "javax.servlet", "org.apache",
		"php warning", "php error", "php notice",
		"python traceback", "ruby error",
	}

	bodyLower := strings.ToLower(body)
	for _, pattern := range errorPatterns {
		if strings.Contains(bodyLower, pattern) {
			result.Evidence = append(result.Evidence, fmt.Sprintf("发现错误模式: %s", pattern))
			matched = true
		}
	}

	// 检查特定CVE的特征
	if detector.checkCVESpecificPatterns(body, cve, result) {
		matched = true
	}

	return matched
}

// analyzeHistoricalPattern 分析历史模式
func (detector *CVEDetector) analyzeHistoricalPattern(resp *http.Response, body string, cve *CVERule, result *CVEDetectionResult) bool {
	// 对于历史CVE，主要检查版本信息和已知特征
	matched := false

	// 检查版本特征
	if detector.checkVersionPatterns(body, cve, result) {
		matched = true
	}

	// 检查历史攻击特征
	if detector.checkHistoricalFeatures(body, cve, result) {
		matched = true
	}

	return matched
}

// checkCVESpecificPatterns 检查CVE特定模式
func (detector *CVEDetector) checkCVESpecificPatterns(body string, cve *CVERule, result *CVEDetectionResult) bool {
	matched := false
	bodyLower := strings.ToLower(body)

	// 根据CVE ID检查特定模式
	switch {
	case strings.Contains(cve.ID, "log4j") || strings.Contains(cve.ID, "44228"):
		// Log4j相关检查
		if strings.Contains(bodyLower, "log4j") || strings.Contains(bodyLower, "jndi") {
			result.Evidence = append(result.Evidence, "检测到Log4j相关特征")
			matched = true
		}

	case strings.Contains(cve.ID, "struts") || strings.Contains(cve.ID, "5638"):
		// Struts2相关检查
		if strings.Contains(bodyLower, "struts") || strings.Contains(bodyLower, "ognl") {
			result.Evidence = append(result.Evidence, "检测到Struts2相关特征")
			matched = true
		}

	case strings.Contains(cve.ID, "shellshock") || strings.Contains(cve.ID, "6271"):
		// Shellshock相关检查
		if strings.Contains(bodyLower, "bash") || strings.Contains(bodyLower, "cgi") {
			result.Evidence = append(result.Evidence, "检测到Shellshock相关特征")
			matched = true
		}

	default:
		// 通用检查
		for _, tag := range cve.Tags {
			if strings.Contains(bodyLower, strings.ToLower(tag)) {
				result.Evidence = append(result.Evidence, fmt.Sprintf("检测到标签特征: %s", tag))
				matched = true
			}
		}
	}

	return matched
}

// checkVersionPatterns 检查版本模式
func (detector *CVEDetector) checkVersionPatterns(body string, cve *CVERule, result *CVEDetectionResult) bool {
	matched := false

	// 检查组件版本信息
	for _, component := range cve.Component {
		pattern := regexp.MustCompile(fmt.Sprintf(`(?i)%s[_\s-]*v?([0-9.]+)`, regexp.QuoteMeta(component)))
		if matches := pattern.FindStringSubmatch(body); len(matches) > 1 {
			result.Evidence = append(result.Evidence, fmt.Sprintf("检测到组件版本: %s %s", component, matches[1]))
			matched = true
		}
	}

	return matched
}

// checkHistoricalFeatures 检查历史特征
func (detector *CVEDetector) checkHistoricalFeatures(body string, cve *CVERule, result *CVEDetectionResult) bool {
	matched := false

	// 检查历史攻击特征
	historicalPatterns := map[string][]string{
		"2014": {"shellshock", "heartbleed", "bash"},
		"2017": {"wannacry", "struts", "equifax"},
		"2021": {"log4shell", "log4j", "jndi"},
	}

	yearStr := strconv.Itoa(cve.Year)
	if patterns, exists := historicalPatterns[yearStr]; exists {
		bodyLower := strings.ToLower(body)
		for _, pattern := range patterns {
			if strings.Contains(bodyLower, pattern) {
				result.Evidence = append(result.Evidence, fmt.Sprintf("检测到%d年历史特征: %s", cve.Year, pattern))
				matched = true
			}
		}
	}

	return matched
}
